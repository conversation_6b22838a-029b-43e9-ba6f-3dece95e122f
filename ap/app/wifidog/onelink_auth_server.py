#!/usr/bin/env python3
"""
OneLink Authentication Server Simulator for ZXIC_ONELINK_TEST
模拟 wireless.cmonelink.com 认证服务器
"""

import http.server
import socketserver
import urllib.parse
import json
import ssl
from datetime import datetime

# 服务器配置
HTTP_PORT = 8080
HTTPS_PORT = 8443

class OneLinkauthHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path
        query = urllib.parse.parse_qs(parsed_path.query)
        
        print(f"[{datetime.now()}] {self.command} {self.path}")
        
        if path.startswith('/wirelessAuthentication/authentication'):
            self.handle_authentication(query)
        elif path.startswith('/wirelessAuthentication/success'):
            self.handle_success(query)
        elif path.startswith('/wirelessAuthentication/error'):
            self.handle_error(query)
        elif path.startswith('/wirelessAuthentication/ping'):
            self.handle_ping(query)
        elif path.startswith('/wirelessAuthentication/auth'):
            self.handle_auth(query)
        else:
            self.send_error(404, "Not Found")
    
    def do_POST(self):
        """Handle POST requests"""
        self.do_GET()
    
    def handle_ping(self, query):
        """Handle ping requests from WiFiDog gateway"""
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'Pong')
        print("  -> Responded with Pong")
    
    def handle_auth(self, query):
        """Handle authentication requests from WiFiDog gateway"""
        stage = query.get('stage', [''])[0]
        ip = query.get('ip', [''])[0]
        mac = query.get('mac', [''])[0]
        token = query.get('token', [''])[0]
        
        print(f"  -> Auth request: stage={stage}, ip={ip}, mac={mac}, token={token}")
        
        # 对于 OneLink 测试，我们允许所有用户
        auth_response = 'Auth: 1'  # 1 = allow, 0 = deny
        
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(auth_response.encode())
        print(f"  -> Responded with: {auth_response}")
    
    def handle_authentication(self, query):
        """Handle authentication page requests"""
        terminal_mac = query.get('terminalMac', ['unknown'])[0]
        mac = query.get('mac', ['unknown'])[0]
        iccid = query.get('iccid', ['unknown'])[0]
        redirect_to = query.get('redirectTo', ['https://www.baidu.com'])[0]
        
        print(f"  -> Authentication request:")
        print(f"     terminalMac: {terminal_mac}")
        print(f"     mac: {mac}")
        print(f"     iccid: {iccid}")
        print(f"     redirectTo: {redirect_to}")
        
        # 生成认证页面
        auth_page = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国移动 OneLink 认证</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .container {{
            background: white;
            border-radius: 10px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }}
        .logo {{
            width: 80px;
            height: 80px;
            background: #ff6b35;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }}
        h1 {{
            color: #333;
            margin-bottom: 10px;
        }}
        .subtitle {{
            color: #666;
            margin-bottom: 30px;
        }}
        .info-box {{
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }}
        .info-item {{
            margin: 10px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }}
        .info-item:last-child {{
            border-bottom: none;
        }}
        .label {{
            font-weight: bold;
            color: #555;
            display: inline-block;
            width: 100px;
        }}
        .value {{
            color: #333;
        }}
        .button {{
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }}
        .button:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255,107,53,0.4);
        }}
        .footer {{
            margin-top: 30px;
            color: #999;
            font-size: 12px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">移动</div>
        <h1>中国移动 OneLink 认证</h1>
        <p class="subtitle">欢迎使用中国移动网络服务</p>
        
        <div class="info-box">
            <div class="info-item">
                <span class="label">设备MAC:</span>
                <span class="value">{terminal_mac}</span>
            </div>
            <div class="info-item">
                <span class="label">网关MAC:</span>
                <span class="value">{mac}</span>
            </div>
            <div class="info-item">
                <span class="label">ICCID:</span>
                <span class="value">{iccid}</span>
            </div>
            <div class="info-item">
                <span class="label">客户端IP:</span>
                <span class="value">{self.client_address[0]}</span>
            </div>
        </div>
        
        <p>点击下方按钮完成认证并开始上网</p>
        
        <a href="/wirelessAuthentication/success?terminalMac={terminal_mac}&mac={mac}&iccid={iccid}&redirectTo={urllib.parse.quote(redirect_to)}" class="button">
            立即认证上网
        </a>
        
        <div class="footer">
            <p>OneLink 测试认证服务器</p>
            <p>模拟 wireless.cmonelink.com 服务</p>
        </div>
    </div>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(auth_page.encode('utf-8'))
        print("  -> Sent OneLink authentication page")
    
    def handle_success(self, query):
        """Handle successful authentication"""
        terminal_mac = query.get('terminalMac', ['unknown'])[0]
        redirect_to = query.get('redirectTo', ['https://www.baidu.com'])[0]
        
        print(f"  -> Authentication success for MAC: {terminal_mac}")
        
        # 生成成功页面，自动跳转到目标URL
        success_page = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证成功</title>
    <meta http-equiv="refresh" content="3;url={redirect_to}">
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .container {{
            background: white;
            border-radius: 10px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }}
        .success-icon {{
            width: 80px;
            height: 80px;
            background: #4CAF50;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
        }}
        h1 {{
            color: #4CAF50;
            margin-bottom: 20px;
        }}
        .countdown {{
            font-size: 18px;
            color: #666;
            margin: 20px 0;
        }}
        .link {{
            color: #4CAF50;
            text-decoration: none;
            font-weight: bold;
        }}
    </style>
    <script>
        let countdown = 3;
        function updateCountdown() {{
            document.getElementById('countdown').textContent = countdown;
            if (countdown > 0) {{
                countdown--;
                setTimeout(updateCountdown, 1000);
            }}
        }}
        window.onload = updateCountdown;
    </script>
</head>
<body>
    <div class="container">
        <div class="success-icon">✓</div>
        <h1>认证成功！</h1>
        <p>您已成功通过 OneLink 认证</p>
        <p>现在可以正常使用网络服务</p>
        <div class="countdown">
            <span id="countdown">3</span> 秒后自动跳转...
        </div>
        <p>如果没有自动跳转，请点击：<a href="{redirect_to}" class="link">继续浏览</a></p>
    </div>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(success_page.encode('utf-8'))
        print(f"  -> Sent success page, redirecting to: {redirect_to}")
    
    def handle_error(self, query):
        """Handle authentication errors"""
        message = query.get('message', ['认证失败'])[0]
        
        error_page = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证错误</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .container {{
            background: white;
            border-radius: 10px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }}
        .error-icon {{
            width: 80px;
            height: 80px;
            background: #ff6b6b;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
        }}
        h1 {{
            color: #ff6b6b;
            margin-bottom: 20px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">✗</div>
        <h1>认证失败</h1>
        <p>{message}</p>
        <p>请稍后重试或联系管理员</p>
    </div>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(error_page.encode('utf-8'))
        print(f"  -> Sent error page: {message}")

def create_ssl_context():
    """Create SSL context for HTTPS"""
    context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
    # 生成自签名证书（仅用于测试）
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

def main():
    print("Starting OneLink Authentication Server Simulator")
    print("模拟 wireless.cmonelink.com 认证服务器")
    print()
    print("Endpoints:")
    print("  - /wirelessAuthentication/authentication - 认证页面")
    print("  - /wirelessAuthentication/success - 成功页面")
    print("  - /wirelessAuthentication/error - 错误页面")
    print("  - /wirelessAuthentication/ping - 健康检查")
    print("  - /wirelessAuthentication/auth - WiFiDog 认证协议")
    print()
    print(f"HTTP Server: http://localhost:{HTTP_PORT}")
    print(f"HTTPS Server: https://localhost:{HTTPS_PORT} (自签名证书)")
    print()
    print("配置 wifidog.conf:")
    print("  AuthServer {")
    print(f"    Hostname localhost")
    print(f"    HTTPPort {HTTP_PORT}")
    print(f"    SSLAvailable yes")
    print(f"    SSLPort {HTTPS_PORT}")
    print("    Path /wirelessAuthentication/")
    print("  }")
    print()
    
    # 启动 HTTP 服务器
    try:
        with socketserver.TCPServer(("", HTTP_PORT), OneLinkauthHandler) as httpd:
            print(f"OneLink Auth Server running on port {HTTP_PORT}")
            print("Press Ctrl+C to stop...")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nShutting down server...")

if __name__ == "__main__":
    main()
