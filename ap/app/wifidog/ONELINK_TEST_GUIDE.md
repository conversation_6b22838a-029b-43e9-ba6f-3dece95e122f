# ZXIC_ONELINK_TEST WiFiDog 配置指南

## 📋 概述

本指南详细说明如何配置 WiFiDog 来适配 ZXIC_ONELINK_TEST 流程，实现与中国移动 OneLink 认证系统的集成。

## 🔍 ZXIC_ONELINK_TEST 流程分析

### 流程概述

1. **编译时启用**：通过 `ZXIC_ONELINK_TEST` 宏定义启用
2. **客户端连接**：设备连接 WiFi 时检查认证状态
3. **MAC 地址检查**：通过 `one_link_authed_mac` 配置项检查是否已认证
4. **流量控制**：未认证设备添加 iptables 规则阻止流量
5. **认证重定向**：构造 OneLink 认证 URL 并重定向

### 认证 URL 构造

```c
// 测试 URL 模板
"https://wireless.cmonelink.com/wirelessAuthentication/authentication?terminalMac=12-34-56-78-FF-BB&mac=00-1A-2B-3d-4D-5E&iccid=898604891523D0022173&redirectTo=https://www.baidu.com"
```

**参数说明：**
- `terminalMac`: 客户端设备 MAC 地址（xx-xx-xx-xx-xx-xx 格式）
- `mac`: WiFi 路由器 MAC 地址（xx-xx-xx-xx-xx-xx 格式）
- `iccid`: SIM 卡 ICCID 号码
- `redirectTo`: 认证成功后跳转的目标 URL

### 关键组件

1. **zte_web_util.c**: 构造认证 URL
2. **qrzl_captive_portal_server.c**: 处理 captive portal 请求
3. **one_link_http_control.c**: OneLink API 交互
4. **wlan_main.c**: WiFi 连接时的流量控制

## 📝 WiFiDog 配置

### 配置文件：wifidog_onelink_test.conf

```bash
# Gateway identification
GatewayID zte_mifi_onelink_test

# Network interfaces - 使用 br0，IP 为 *************
GatewayInterface br0
GatewayAddress *************
GatewayPort 2060

# Authentication server - 使用 wireless.cmonelink.com
AuthServer {
    Hostname wireless.cmonelink.com
    HTTPPort 443
    SSLAvailable yes
    SSLPort 443
    Path /
    LoginScriptPathFragment wirelessAuthentication/authentication?
    PortalScriptPathFragment wirelessAuthentication/success?
    MsgScriptPathFragment wirelessAuthentication/error?
    PingScriptPathFragment wirelessAuthentication/ping?
    AuthScriptPathFragment wirelessAuthentication/auth?
}
```

### 防火墙规则配置

```bash
FirewallRuleSet unknown-users {
    # 允许 DNS 解析
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
    
    # 允许 DHCP
    FirewallRule allow udp port 67
    
    # 允许访问认证服务器
    FirewallRule allow tcp port 443 to wireless.cmonelink.com
    
    # 允许各种设备的 captive portal 检测
    FirewallRule allow tcp port 80 to apple.com
    FirewallRule allow tcp port 80 to google.com
    FirewallRule allow tcp port 80 to baidu.com
    
    # 允许访问网关管理
    FirewallRule allow tcp port 2060 to *************
}
```

## 🧪 测试环境搭建

### 1. 使用模拟认证服务器

启动 OneLink 认证服务器模拟器：

```bash
python3 onelink_auth_server.py
```

修改配置文件使用本地服务器：

```bash
AuthServer {
    Hostname *************  # 或运行服务器的IP
    HTTPPort 8080
    SSLAvailable no
    Path /wirelessAuthentication/
}
```

### 2. 部署到设备

```bash
# 复制配置文件
scp wifidog_onelink_test.conf root@*************:/etc/wifidog.conf

# 启动 WiFiDog
ssh root@*************
wifidog -c /etc/wifidog.conf -f -d 7  # 调试模式
```

### 3. 验证网络配置

```bash
# 检查 br0 接口
ip addr show br0

# 确认 IP 地址
ping *************

# 检查 iptables 规则
iptables -L -n -v
```

## 🔧 集成要点

### 1. 与现有系统集成

WiFiDog 需要与现有的 ZXIC_ONELINK_TEST 系统协同工作：

```bash
# 检查已认证 MAC 列表
cfg_get_item("one_link_authed_mac", mac_list, sizeof(mac_list))

# 更新认证状态
cfg_set_item("one_link_authed_mac", "aa:bb:cc:dd:ee:ff;11:22:33:44:55:66;")
```

### 2. 流量控制协调

确保 WiFiDog 的 iptables 规则与现有的流量控制不冲突：

```bash
# WiFiDog 创建的链
iptables -L WiFiDog_* -n -v

# 现有的 OneLink 规则
iptables -L | grep -i onelink
```

### 3. 认证状态同步

WiFiDog 认证成功后，需要更新 OneLink 系统的认证状态：

```c
// 在 WiFiDog 认证回调中
cfg_set_item("one_link_authed_mac", authenticated_mac_list);
```

## 🚀 部署步骤

### 1. 编译环境准备

确保启用了相关宏定义：

```makefile
# 在 config.mk 中
export ZXIC_ONELINK_TEST := yes
CUSTOM_MACRO += -DZXIC_ONELINK_TEST
```

### 2. 配置网络

```bash
# 确认 br0 配置
ifconfig br0 ************* netmask *************

# 启用 IP 转发
echo 1 > /proc/sys/net/ipv4/ip_forward
```

### 3. 启动服务

```bash
# 启动 WiFiDog
wifidog -c /etc/wifidog.conf

# 检查状态
wdctl status

# 查看日志
tail -f /var/log/messages | grep wifidog
```

## 🔍 测试验证

### 1. 基本功能测试

```bash
# 连接测试设备到 WiFi
# 尝试访问网站，应该被重定向到认证页面
curl -v http://www.baidu.com

# 检查重定向 URL
# 应该包含 OneLink 认证服务器地址
```

### 2. 认证流程测试

1. **连接 WiFi 网络**
2. **打开浏览器访问任意网站**
3. **应该被重定向到 OneLink 认证页面**
4. **URL 应该包含正确的参数**：
   - terminalMac: 设备 MAC 地址
   - mac: 路由器 MAC 地址  
   - iccid: SIM 卡 ICCID
   - redirectTo: 目标 URL

### 3. 状态检查

```bash
# 检查 WiFiDog 状态
wdctl status

# 查看客户端列表
wdctl clients

# 检查防火墙规则
iptables -L WiFiDog_Internet -n -v
```

## 🐛 故障排除

### 常见问题

1. **认证页面无法访问**
   - 检查 DNS 解析
   - 确认防火墙规则
   - 验证认证服务器可达性

2. **重定向循环**
   - 检查 captive portal 检测规则
   - 确认网关地址配置
   - 验证 HTTP 响应头

3. **认证后无法上网**
   - 检查路由配置
   - 确认 NAT 规则
   - 验证上游连接

### 调试命令

```bash
# 详细日志
wifidog -c /etc/wifidog.conf -f -d 7

# 网络连通性测试
ping wireless.cmonelink.com

# 手动测试认证服务器
curl -v "https://wireless.cmonelink.com/wirelessAuthentication/ping"

# 检查 iptables 规则
iptables-save | grep -i wifidog
```

## 📊 性能优化

### 配置优化

```bash
# 调整检查间隔
CheckInterval 30

# 优化连接数
HTTPDMaxConn 50

# 减少客户端超时
ClientTimeout 3
```

### 系统优化

```bash
# 优化内核参数
echo 1024 > /proc/sys/net/netfilter/nf_conntrack_max
echo 300 > /proc/sys/net/netfilter/nf_conntrack_tcp_timeout_established
```

## 📚 参考信息

- OneLink 认证 URL 格式
- WiFiDog 配置参数说明
- iptables 规则配置
- 网络接口配置方法
