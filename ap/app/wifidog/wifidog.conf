GatewayID wdog_id01
ExternalInterface wan1
GatewayInterface br0
GatewayPort 2060				
HTTPDMaxConn 100
CheckInterval 60
ClientTimeout 50
Daemon 1
AuthServer {
    Hostname www.server.com		
    HTTPPort 80
    Path /wifidog/					
}

FirewallRuleSet global {
}
FirewallRuleSet validating-users {
    FirewallRule allow to 0.0.0.0/0
}
FirewallRuleSet known-users {
    FirewallRule allow to 0.0.0.0/0
}
FirewallRuleSet unknown-users {
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
    FirewallRule allow udp port 67
	FirewallRule allow tcp port 67
}
FirewallRuleSet locked-users {
    FirewallRule block to 0.0.0.0/0
}
