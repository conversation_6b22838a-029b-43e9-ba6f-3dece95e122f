# WiFiDog 部署和测试指南

## 📋 概述

本指南详细说明如何在 ZTE MiFi 设备上部署和测试 WiFiDog captive portal 功能。

## 🔧 配置文件分析

### 当前配置问题

1. **❌ 缺少必需的 AuthServer 配置**
   - 原配置文件中所有 AuthServer 都被注释
   - WiFiDog 要求至少配置一个认证服务器

2. **⚠️ 网络接口配置**
   - 当前使用 `br-lan`（OpenWrt 风格）
   - ZTE MiFi 可能使用不同的接口名

3. **⚠️ PopularServers 过时**
   - 建议使用更可靠的服务器进行连通性检测

## 📝 测试配置文件

已创建 `wifidog_test.conf` 包含：

### 基本配置
```bash
GatewayID zte_mifi_test
GatewayInterface wlan0          # 需要根据实际接口调整
GatewayAddress ***********
GatewayPort 2060
```

### 认证服务器配置
```bash
AuthServer {
    Hostname *************      # 认证服务器IP
    HTTPPort 80
    Path /wifidog/
    LoginScriptPathFragment login/?
    PortalScriptPathFragment portal/?
    MsgScriptPathFragment gw_message.php?
    PingScriptPathFragment ping/?
    AuthScriptPathFragment auth/?
}
```

### 防火墙规则
- 允许 DNS 查询（端口 53）
- 允许 DHCP（端口 67）
- 允许常见 Web 端口（80, 443）
- 支持各种设备的 captive portal 检测

## 🚀 部署步骤

### 1. 准备工作

```bash
# 1. 确认编译成功
ls -la /home/<USER>/work/Code-u28/ap/output/rootfs/bin/wifidog*

# 2. 检查网络接口
ip link show

# 3. 确认 iptables 可用
iptables -L
```

### 2. 配置网络接口

在目标设备上确认正确的网络接口名：

```bash
# 查看所有网络接口
ip link show

# 常见接口名：
# - wlan0: WiFi 接口
# - br0: 桥接接口
# - eth0: 以太网接口
```

### 3. 修改配置文件

根据实际网络环境修改 `wifidog_test.conf`：

```bash
# 修改网络接口
GatewayInterface wlan0  # 替换为实际接口

# 修改认证服务器
AuthServer {
    Hostname *************  # 替换为实际服务器IP
    HTTPPort 8080           # 根据需要调整端口
}
```

### 4. 部署到设备

```bash
# 复制配置文件到设备
scp wifidog_test.conf root@device_ip:/etc/wifidog.conf

# 或者直接在设备上创建
cat > /etc/wifidog.conf << 'EOF'
# 粘贴配置内容
EOF
```

## 🧪 测试方法

### 方法1：使用简单认证服务器

1. **启动测试认证服务器**：
```bash
python3 simple_auth_server.py
```

2. **配置 WiFiDog**：
```bash
# 修改配置文件中的认证服务器地址
AuthServer {
    Hostname *************  # 运行认证服务器的IP
    HTTPPort 8080
    Path /wifidog/
}
```

3. **启动 WiFiDog**：
```bash
# 前台运行（用于调试）
wifidog -c /etc/wifidog.conf -f -d 7

# 后台运行
wifidog -c /etc/wifidog.conf
```

### 方法2：使用现有认证服务器

如果有现成的 WiFiDog-auth 服务器：

```bash
AuthServer {
    Hostname auth.example.com
    HTTPPort 80
    SSLAvailable no
    Path /
}
```

### 方法3：无认证测试

对于基本功能测试，可以使用信任 MAC 地址：

```bash
# 添加到配置文件
TrustedMACList aa:bb:cc:dd:ee:ff,11:22:33:44:55:66
```

## 🔍 验证功能

### 1. 检查 WiFiDog 状态

```bash
# 检查进程
ps aux | grep wifidog

# 检查端口监听
netstat -ln | grep 2060

# 使用控制工具
wdctl status
```

### 2. 检查防火墙规则

```bash
# 查看 iptables 规则
iptables -L -n -v

# 查看 WiFiDog 创建的链
iptables -L WiFiDog_* -n -v
```

### 3. 测试客户端连接

1. **连接到 WiFi 网络**
2. **尝试访问网站** - 应该被重定向到登录页面
3. **检查重定向 URL** - 应该包含认证服务器地址
4. **完成认证流程**
5. **验证网络访问**

### 4. 日志检查

```bash
# 查看系统日志
tail -f /var/log/messages | grep wifidog

# 查看 WiFiDog 日志（如果配置了）
tail -f /var/log/wifidog.log
```

## 🐛 故障排除

### 常见问题

1. **WiFiDog 启动失败**
   - 检查配置文件语法
   - 确认网络接口存在
   - 检查端口是否被占用

2. **客户端无法重定向**
   - 检查 iptables 规则
   - 确认 DNS 解析正常
   - 检查网关地址配置

3. **认证服务器无法访问**
   - 检查服务器是否运行
   - 确认网络连通性
   - 检查防火墙设置

4. **认证后无法上网**
   - 检查路由配置
   - 确认 NAT 规则
   - 检查上游网络连接

### 调试命令

```bash
# 详细日志模式启动
wifidog -c /etc/wifidog.conf -f -d 7

# 检查配置
wifidog -c /etc/wifidog.conf -t

# 手动测试认证服务器
curl http://*************:8080/wifidog/ping

# 检查网络连通性
ping www.google.com
```

## 📊 性能优化

### 配置优化

```bash
# 调整检查间隔
CheckInterval 30        # 减少到30秒

# 调整客户端超时
ClientTimeout 3         # 减少到3个检查周期

# 限制最大连接数
HTTPDMaxConn 20         # 根据设备性能调整
```

### 系统优化

```bash
# 增加文件描述符限制
ulimit -n 1024

# 优化内核参数
echo 1 > /proc/sys/net/ipv4/ip_forward
echo 1 > /proc/sys/net/netfilter/nf_conntrack_tcp_loose
```

## 📚 参考资源

- [WiFiDog 官方文档](http://dev.wifidog.org/)
- [WiFiDog 协议规范](http://dev.wifidog.org/wiki/doc/developer/WiFiDogProtocol_V1)
- [iptables 防火墙配置](https://netfilter.org/documentation/)

## 🔒 安全注意事项

1. **更改默认密码**：如果启用了 HTTP 认证
2. **使用 HTTPS**：生产环境建议启用 SSL
3. **限制管理访问**：配置适当的防火墙规则
4. **定期更新**：保持软件版本最新
5. **监控日志**：定期检查异常活动
