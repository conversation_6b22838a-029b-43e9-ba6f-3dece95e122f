#!/bin/bash

# Test script for wifidog compilation and installation
# This script verifies that wifidog has been properly compiled and installed

echo "=== WiFiDog Compilation and Installation Test ==="

# Check if source files exist
echo "1. Checking source files..."
if [ -f "wifidog-gateway-1.3.0/src/wifidog" ]; then
    echo "✓ wifidog binary exists in source directory"
    file wifidog-gateway-1.3.0/src/wifidog
else
    echo "✗ wifidog binary not found in source directory"
    exit 1
fi

if [ -f "wifidog-gateway-1.3.0/src/wdctl" ]; then
    echo "✓ wdctl binary exists in source directory"
    file wifidog-gateway-1.3.0/src/wdctl
else
    echo "✗ wdctl binary not found in source directory"
    exit 1
fi

# Check if installed files exist
echo ""
echo "2. Checking installed files..."
ROOTFS_DIR="/home/<USER>/work/Code-u28/ap/output/rootfs"

if [ -f "$ROOTFS_DIR/bin/wifidog" ]; then
    echo "✓ wifidog installed in rootfs"
    file $ROOTFS_DIR/bin/wifidog
else
    echo "✗ wifidog not found in rootfs"
    exit 1
fi

if [ -f "$ROOTFS_DIR/bin/wdctl" ]; then
    echo "✓ wdctl installed in rootfs"
    file $ROOTFS_DIR/bin/wdctl
else
    echo "✗ wdctl not found in rootfs"
    exit 1
fi

# Check architecture
echo ""
echo "3. Verifying architecture..."
WIFIDOG_ARCH=$(file $ROOTFS_DIR/bin/wifidog | grep -o "ARM")
WDCTL_ARCH=$(file $ROOTFS_DIR/bin/wdctl | grep -o "ARM")

if [ "$WIFIDOG_ARCH" = "ARM" ] && [ "$WDCTL_ARCH" = "ARM" ]; then
    echo "✓ Both binaries are ARM architecture"
else
    echo "✗ Architecture mismatch"
    exit 1
fi

# Check if binaries are stripped
echo ""
echo "4. Checking if binaries are properly stripped..."
if file $ROOTFS_DIR/bin/wifidog | grep -q "stripped"; then
    echo "✓ wifidog binary is stripped"
else
    echo "! wifidog binary is not stripped (this is OK for debugging)"
fi

if file $ROOTFS_DIR/bin/wdctl | grep -q "stripped"; then
    echo "✓ wdctl binary is stripped"
else
    echo "! wdctl binary is not stripped (this is OK for debugging)"
fi

# Show file sizes
echo ""
echo "5. Binary sizes:"
ls -lh $ROOTFS_DIR/bin/wifidog $ROOTFS_DIR/bin/wdctl

echo ""
echo "=== All tests passed! WiFiDog has been successfully compiled and installed ==="
echo ""
echo "Summary:"
echo "- wifidog: Captive portal gateway daemon"
echo "- wdctl: WiFiDog control utility"
echo "- Both binaries are ARM 32-bit and ready for deployment"
