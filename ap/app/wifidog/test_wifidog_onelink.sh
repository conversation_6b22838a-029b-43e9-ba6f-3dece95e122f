#!/bin/sh

# WiFiDog OneLink 测试脚本
# 用于验证 wifidog 功能是否正常工作

echo "=== WiFiDog OneLink 功能测试 ==="
echo "时间: $(date)"
echo ""

# 配置文件路径
WIFIDOG_CONF="/etc_rw/wifidog_onelink_test.conf"
WIFIDOG_BIN="/tmp/wifidog"

# 1. 检查配置文件
echo "1. 检查配置文件:"
if [ -f "$WIFIDOG_CONF" ]; then
    echo "   ✓ 配置文件存在: $WIFIDOG_CONF"
    
    # 检查关键配置项
    gateway_id=$(grep "^GatewayID" "$WIFIDOG_CONF" | awk '{print $2}')
    gateway_addr=$(grep "^GatewayAddress" "$WIFIDOG_CONF" | awk '{print $2}')
    gateway_port=$(grep "^GatewayPort" "$WIFIDOG_CONF" | awk '{print $2}')
    auth_hostname=$(grep "Hostname" "$WIFIDOG_CONF" | awk '{print $2}')
    
    echo "   Gateway ID: $gateway_id"
    echo "   Gateway Address: $gateway_addr"
    echo "   Gateway Port: $gateway_port"
    echo "   Auth Server: $auth_hostname"
else
    echo "   ✗ 配置文件不存在: $WIFIDOG_CONF"
    echo "   请先复制配置文件到正确位置"
    exit 1
fi
echo ""

# 2. 检查wifidog二进制文件
echo "2. 检查wifidog二进制文件:"
if [ -f "$WIFIDOG_BIN" ]; then
    echo "   ✓ wifidog二进制文件存在: $WIFIDOG_BIN"
    echo "   版本信息:"
    $WIFIDOG_BIN -h 2>&1 | head -3 | sed 's/^/     /'
else
    echo "   ✗ wifidog二进制文件不存在: $WIFIDOG_BIN"
    echo "   请先编译并安装wifidog"
    exit 1
fi
echo ""

# 3. 检查网络接口
echo "3. 检查网络接口:"
br0_ip=$(ifconfig br0 2>/dev/null | grep "inet addr" | awk -F: '{print $2}' | awk '{print $1}')
if [ -n "$br0_ip" ]; then
    echo "   ✓ br0接口IP: $br0_ip"
    if [ "$br0_ip" = "$gateway_addr" ]; then
        echo "   ✓ br0 IP与配置文件中的GatewayAddress匹配"
    else
        echo "   ⚠ br0 IP与配置文件中的GatewayAddress不匹配"
    fi
else
    echo "   ✗ br0接口未配置或无IP地址"
fi

wan1_ip=$(ifconfig wan1 2>/dev/null | grep "inet addr" | awk -F: '{print $2}' | awk '{print $1}')
if [ -n "$wan1_ip" ]; then
    echo "   ✓ wan1接口IP: $wan1_ip"
else
    echo "   ⚠ wan1接口未配置或无IP地址"
fi
echo ""

# 4. 停止现有的wifidog进程
echo "4. 停止现有的wifidog进程:"
wifidog_pid=$(ps | grep wifidog | grep -v grep | awk '{print $1}')
if [ -n "$wifidog_pid" ]; then
    echo "   发现运行中的wifidog进程 (PID: $wifidog_pid)，正在停止..."
    kill $wifidog_pid
    sleep 2
    
    # 检查是否成功停止
    wifidog_pid=$(ps | grep wifidog | grep -v grep | awk '{print $1}')
    if [ -z "$wifidog_pid" ]; then
        echo "   ✓ wifidog进程已停止"
    else
        echo "   ⚠ wifidog进程仍在运行，强制终止..."
        kill -9 $wifidog_pid
    fi
else
    echo "   ✓ 没有运行中的wifidog进程"
fi
echo ""

# 5. 清理现有的iptables规则
echo "5. 清理现有的iptables规则:"
echo "   清理WiFiDog相关的iptables规则..."

# 清理filter表
iptables -t filter -F WiFiDog_br0_Internet 2>/dev/null
iptables -t filter -F WiFiDog_br0_AuthServers 2>/dev/null
iptables -t filter -F WiFiDog_br0_Locked 2>/dev/null
iptables -t filter -F WiFiDog_br0_Global 2>/dev/null
iptables -t filter -F WiFiDog_br0_Validate 2>/dev/null
iptables -t filter -F WiFiDog_br0_Known 2>/dev/null
iptables -t filter -F WiFiDog_br0_Unknown 2>/dev/null

# 清理nat表
iptables -t nat -F WiFiDog_br0_AuthServers 2>/dev/null
iptables -t nat -F WiFiDog_br0_Outgoing 2>/dev/null
iptables -t nat -F WiFiDog_br0_Router 2>/dev/null
iptables -t nat -F WiFiDog_br0_Internet 2>/dev/null
iptables -t nat -F WiFiDog_br0_Global 2>/dev/null
iptables -t nat -F WiFiDog_br0_Unknown 2>/dev/null

echo "   ✓ iptables规则已清理"
echo ""

# 6. 启动wifidog测试
echo "6. 启动wifidog进行测试:"
echo "   使用配置文件: $WIFIDOG_CONF"
echo "   启动命令: $WIFIDOG_BIN -f -c $WIFIDOG_CONF -d 7"
echo ""
echo "   注意：wifidog将在前台运行，按Ctrl+C停止"
echo "   请在另一个终端中进行连接测试"
echo ""

# 启动wifidog
exec $WIFIDOG_BIN -f -c $WIFIDOG_CONF -d 7
