
ssv_phy=""
#if [[ ${1} =~ "wlan" ]]; then
#	wlan_dirs=/sys/class/net/${1}/device/ieee80211/
#	if [ ! -e ${wlan_dirs} ]; then
#		echo "Could not find the ${1}."
#		exit 1;
#	fi
#	# shift wlanX
#	shift 1
#	ssv_phy=`ls ${wlan_dirs}`
#else
	phy_dirs="/sys/class/ieee80211/*"

	for phy_dir in $phy_dirs; do
    if [ -d ${phy_dir}/device/driver ]; then
    	ssv_phy=${phy_dir##*/};
	    break;
    fi
	done
#fi


# excute CLI
if [ ${ssv_phy} ]; then
	SSV_CMD_FILE=/proc/ssv/${ssv_phy}/ssv_cmd
	if [ -f $SSV_CMD_FILE ]; then
		echo "$*" > $SSV_CMD_FILE
		cat $SSV_CMD_FILE
	else
	    SSV_CMD_FILE=/proc/tu_ssv/${ssv_phy}/ssv_cmd
	fi
	if [ -f $SSV_CMD_FILE ]; then
		echo "$*" > $SSV_CMD_FILE
		cat $SSV_CMD_FILE
	else
        echo "please enable wifi" 
	fi
else 
	echo "./cli [CMD]"
fi


