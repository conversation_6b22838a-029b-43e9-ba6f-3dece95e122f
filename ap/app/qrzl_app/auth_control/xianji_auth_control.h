#ifndef __QRZL_XIANJI_AUTH_CONTROL_H_
#define __QRZL_XIANJI_AUTH_CONTROL_H_

/**
 * @brief 获取短信验证码
 * @param mac          设备mac地址
 * @param terminalMac  终端mac地址
 * @param phoneNum     手机号码
 * @param iccid        iccid号码
 * @return             0 成功，-1 失败
 */
int xianji_get_sms_code(const char *mac, const char *terminalMac, const char *phoneNum, const char *iccid);

/**
 * 终端认证
 * @param mac          设备mac地址
 * @param terminalMac  终端mac地址
 * @param phoneNum     手机号码
 * @param iccid        iccid号码
 * @param smsCode      短信验证码
 * @return             0 成功，-1 失败
 */
int xianji_terminal_auth(const char *mac, const char *terminalMac, const char *phoneNum, const char *iccid, const char *smsCode);

/**
 * 获取已认证终端列表
 * @param mac   设备mac地址
 * @param iccid iccid号码
 * @return      认证终端mac列表字符串（格式 "MAC1;MAC2;MAC3;"），需要调用者 free()，失败返回 NULL
 */
char *xianji_authed_list(const char *mac, const char *iccid);

/**
 * 终端上下线状态上报
 * @param push_type    上线/下线 1:上线 0:下线
 * @param mac          设备mac地址
 * @param terminalMac  终端mac地址
 * @param longitude    经度
 * @param latitude     纬度
 * @param iccid        iccid号码
 * @return             0 成功，-1 失败
 */
int xianji_terminal_line_status_reporting(int push_type, const char *mac, const char *terminalMac, const char *longitude, const char *latitude, const char *iccid);

#endif