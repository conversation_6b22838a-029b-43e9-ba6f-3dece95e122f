ExternalInterface eth0
GatewayInterface internal0

HtmlMessageFile ../../wifidog-msg.html


AuthServer {
    Hostname localhost
    HTTPPort 8080
    Path /
}

ClientTimeout 5

FirewallRuleSet global {
}

FirewallRuleSet validating-users {
    FirewallRule allow to 0.0.0.0/0
}

FirewallRuleSet known-users {
    FirewallRule allow to 0.0.0.0/0
}

FirewallRuleSet unknown-users {
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
    FirewallRule allow udp port 67
    FirewallRule allow tcp port 67
}

FirewallRuleSet locked-users {
    FirewallRule block to 0.0.0.0/0
}
