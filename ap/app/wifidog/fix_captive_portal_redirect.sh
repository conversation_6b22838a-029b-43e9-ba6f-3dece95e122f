#!/bin/sh

# 修复WiFiDog认证页面重定向问题
# 根本原因：HTML消息文件缺失导致重定向响应格式不完整

echo "=== WiFiDog 认证页面重定向修复 ==="
echo "时间: $(date)"
echo ""

echo "问题分析："
echo "  - WiFiDog正确拦截HTTP请求并生成重定向响应"
echo "  - 但HTML消息文件 /etc/wifidog-msg.html 不存在"
echo "  - 导致重定向响应格式不完整，客户端无法正确处理"
echo ""

# 1. 检查当前状态
echo "1. 检查当前状态:"
echo ""

echo "   检查HTML消息文件:"
if [ -f "/etc/wifidog-msg.html" ]; then
    echo "     ✓ /etc/wifidog-msg.html 存在"
    ls -la /etc/wifidog-msg.html | sed 's/^/     /'
else
    echo "     ✗ /etc/wifidog-msg.html 不存在 (这是问题根源)"
fi
echo ""

echo "   检查WiFiDog进程:"
wifidog_pid=$(ps | grep wifidog | grep -v grep | awk '{print $1}')
if [ -n "$wifidog_pid" ]; then
    echo "     ✓ WiFiDog进程运行中 (PID: $wifidog_pid)"
else
    echo "     ✗ WiFiDog进程未运行"
fi
echo ""

# 2. 创建HTML消息文件
echo "2. 创建HTML消息文件:"
echo ""

echo "   创建 /etc/wifidog-msg.html ..."

# 确保目录存在
mkdir -p /etc

# 创建HTML消息文件
cat > /etc/wifidog-msg.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>WiFiDog Authentication Required</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 50px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        .redirect-link {
            display: inline-block;
            background-color: #007cba;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .redirect-link:hover {
            background-color: #005a87;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007cba;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <script>
        // 自动重定向倒计时
        var countdown = 3;
        function updateCountdown() {
            var element = document.getElementById('countdown');
            if (element) {
                element.textContent = countdown;
                if (countdown > 0) {
                    countdown--;
                    setTimeout(updateCountdown, 1000);
                }
            }
        }
        
        // 页面加载完成后开始倒计时
        window.onload = function() {
            updateCountdown();
        };
    </script>
</head>
<body>
    <div class="container">
        <h1>WiFi Authentication Required</h1>
        <div class="spinner"></div>
        <p>You need to authenticate to access the internet.</p>
        <p>You will be redirected to the login page in <span id="countdown">3</span> seconds...</p>
        <p>If you are not redirected automatically, please click the button below:</p>
        <a href="#" class="redirect-link" onclick="window.location.href=document.referrer || '/';">
            Go to Login Page
        </a>
        <p style="margin-top: 30px; font-size: 12px; color: #999;">
            WiFiDog Captive Portal System
        </p>
    </div>
</body>
</html>
EOF

if [ $? -eq 0 ]; then
    echo "     ✓ HTML消息文件创建成功"
    ls -la /etc/wifidog-msg.html | sed 's/^/     /'
else
    echo "     ✗ HTML消息文件创建失败"
    exit 1
fi
echo ""

# 3. 验证文件内容
echo "3. 验证文件内容:"
echo ""

echo "   文件大小: $(wc -c < /etc/wifidog-msg.html) 字节"
echo "   文件权限: $(ls -la /etc/wifidog-msg.html | awk '{print $1}')"
echo ""

# 4. 重启WiFiDog服务
echo "4. 重启WiFiDog服务:"
echo ""

if [ -n "$wifidog_pid" ]; then
    echo "   停止当前WiFiDog进程 (PID: $wifidog_pid)..."
    kill $wifidog_pid
    sleep 2
    
    # 确认进程已停止
    if ps | grep -q $wifidog_pid; then
        echo "     强制终止WiFiDog进程..."
        kill -9 $wifidog_pid
        sleep 1
    fi
    echo "     ✓ WiFiDog进程已停止"
fi

echo "   启动WiFiDog..."
if [ -f "/etc_rw/wifidog_local_test.conf" ]; then
    echo "     使用配置文件: /etc_rw/wifidog_local_test.conf"
    /tmp/wifidog -f -c /etc_rw/wifidog_local_test.conf -d 7 &
    sleep 3
    
    # 检查新进程
    new_wifidog_pid=$(ps | grep wifidog | grep -v grep | awk '{print $1}')
    if [ -n "$new_wifidog_pid" ]; then
        echo "     ✓ WiFiDog重启成功 (新PID: $new_wifidog_pid)"
    else
        echo "     ✗ WiFiDog重启失败"
        exit 1
    fi
else
    echo "     ✗ 配置文件 /etc_rw/wifidog_local_test.conf 不存在"
    exit 1
fi
echo ""

# 5. 测试验证
echo "5. 测试验证:"
echo ""

echo "   等待WiFiDog完全启动..."
sleep 5

echo "   检查WiFiDog日志中的错误..."
if tail -20 /tmp/wifidog.log 2>/dev/null | grep -q "Failed to open HTML message file"; then
    echo "     ✗ 仍然存在HTML消息文件错误"
    echo "     最近的错误:"
    tail -5 /tmp/wifidog.log | grep "Failed to open HTML message file" | sed 's/^/       /'
else
    echo "     ✓ 没有发现HTML消息文件错误"
fi
echo ""

# 6. 提供测试指导
echo "6. 测试指导:"
echo ""

echo "   现在可以进行以下测试:"
echo ""

echo "   方法1: 客户端设备测试"
echo "     1. 客户端设备连接WiFi"
echo "     2. 在浏览器中访问 http://www.baidu.com"
echo "     3. 应该自动弹出认证页面"
echo ""

echo "   方法2: 手动测试重定向"
echo "     1. 确保认证服务器运行: python3 simple_auth_server.py"
echo "     2. 客户端访问任意HTTP网站"
echo "     3. 观察是否正确重定向到认证页面"
echo ""

echo "   方法3: 检查日志"
echo "     tail -f /tmp/wifidog.log"
echo "     应该看到正常的重定向日志，不再有HTML文件错误"
echo ""

# 7. 故障排除
echo "7. 故障排除:"
echo ""

echo "   如果仍然没有弹出认证页面:"
echo "   1. 检查认证服务器是否在***************:9080运行"
echo "   2. 检查客户端设备网络设置"
echo "   3. 尝试清除浏览器缓存"
echo "   4. 检查WiFiDog日志: tail -f /tmp/wifidog.log"
echo "   5. 验证iptables规则: iptables -t nat -L -n | grep WiFiDog"
echo ""

echo "=== 修复完成 ==="
echo ""
echo "修复要点总结:"
echo "1. ✓ 创建了 /etc/wifidog-msg.html 文件"
echo "2. ✓ 重启了WiFiDog服务"
echo "3. ✓ 解决了重定向响应格式不完整的问题"
echo ""
echo "预期效果: 客户端访问HTTP网站时应该正确弹出认证页面"
