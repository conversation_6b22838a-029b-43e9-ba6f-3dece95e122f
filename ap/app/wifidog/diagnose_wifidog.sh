#!/bin/sh

# WiFiDog 问题诊断脚本
# 用于诊断 ZTE MiFi 设备上的 WiFiDog 问题

echo "=== WiFiDog 问题诊断报告 ==="
echo "时间: $(date)"
echo ""

# 1. 检查网络接口
echo "1. 网络接口状态:"
echo "br0 接口:"
ifconfig br0 2>/dev/null || echo "  br0 接口不存在"
echo ""

echo "wan1 接口:"
ifconfig wan1 2>/dev/null || echo "  wan1 接口不存在"
echo ""

echo "所有接口:"
ip link show | grep -E "^[0-9]+:" | cut -d: -f2 | sed 's/^ */  /'
echo ""

# 2. 检查路由
echo "2. 路由表:"
route -n | head -10
echo ""

# 3. 检查 DNS
echo "3. DNS 解析测试:"
echo "  测试 wireless.cmonelink.com:"
nslookup wireless.cmonelink.com 2>/dev/null | grep -A2 "Name:" || echo "    DNS 解析失败"

echo "  测试 www.baidu.com:"
nslookup www.baidu.com 2>/dev/null | grep -A2 "Name:" || echo "    DNS 解析失败"
echo ""

# 4. 检查网络连通性
echo "4. 网络连通性测试:"
echo "  ping *******:"
ping -c 2 ******* >/dev/null 2>&1 && echo "    成功" || echo "    失败"

echo "  ping wireless.cmonelink.com:"
ping -c 2 wireless.cmonelink.com >/dev/null 2>&1 && echo "    成功" || echo "    失败"
echo ""

# 5. 检查 iptables
echo "5. iptables 规则:"
echo "  WiFiDog 相关链:"
iptables -L | grep -i wifidog | head -5
echo ""

echo "  filter 表统计:"
iptables -L -n -v | grep -E "(Chain|pkts)" | head -10
echo ""

# 6. 检查进程
echo "6. 进程状态:"
echo "  WiFiDog 进程:"
ps aux | grep wifidog | grep -v grep || echo "    WiFiDog 未运行"
echo ""

echo "  网络相关进程:"
ps aux | grep -E "(dnsmasq|dhcp)" | grep -v grep
echo ""

# 7. 检查端口监听
echo "7. 端口监听状态:"
echo "  端口 2060 (WiFiDog):"
netstat -ln | grep :2060 || echo "    端口 2060 未监听"

echo "  端口 53 (DNS):"
netstat -ln | grep :53 | head -2

echo "  端口 80 (HTTP):"
netstat -ln | grep :80 | head -2
echo ""

# 8. 检查配置文件
echo "8. 配置文件检查:"
if [ -f "/etc_rw/wifidog_onelink_test.conf" ]; then
    echo "  配置文件存在: /etc_rw/wifidog_onelink_test.conf"
    echo "  认证服务器配置:"
    grep -A5 "AuthServer" /etc_rw/wifidog_onelink_test.conf | head -10
else
    echo "  配置文件不存在: /etc_rw/wifidog_onelink_test.conf"
fi
echo ""

# 9. 检查日志
echo "9. 系统日志 (最近10行):"
if [ -f "/var/log/messages" ]; then
    tail -10 /var/log/messages | grep -i wifidog
elif [ -f "/tmp/messages" ]; then
    tail -10 /tmp/messages | grep -i wifidog
else
    echo "  未找到系统日志文件"
fi
echo ""

# 10. 测试认证服务器连接
echo "10. 认证服务器连接测试:"
echo "  测试 wireless.cmonelink.com:80:"
timeout 5 telnet wireless.cmonelink.com 80 </dev/null >/dev/null 2>&1 && echo "    连接成功" || echo "    连接失败"

echo "  测试 wireless.cmonelink.com:443:"
timeout 5 telnet wireless.cmonelink.com 443 </dev/null >/dev/null 2>&1 && echo "    连接成功" || echo "    连接失败"
echo ""

# 11. 建议解决方案
echo "11. 建议解决方案:"
echo ""

# 检查 SSL 问题
if grep -q "SSLAvailable.*yes" /etc_rw/wifidog_onelink_test.conf 2>/dev/null; then
    echo "  ❌ SSL 配置问题:"
    echo "     WiFiDog 没有编译 SSL 支持，请将 SSLAvailable 设置为 no"
    echo "     并使用 HTTPPort 80 而不是 443"
    echo ""
fi

# 检查域名解析问题
if ! nslookup wireless.cmonelink.com >/dev/null 2>&1; then
    echo "  ❌ DNS 解析问题:"
    echo "     无法解析 wireless.cmonelink.com"
    echo "     建议使用本地测试服务器或检查 DNS 配置"
    echo ""
fi

# 检查网络连通性
if ! ping -c 1 ******* >/dev/null 2>&1; then
    echo "  ❌ 网络连通性问题:"
    echo "     无法访问外网，请检查路由和上游连接"
    echo ""
fi

echo "  ✅ 推荐操作:"
echo "     1. 使用本地测试配置: wifidog_local_test.conf"
echo "     2. 启动本地认证服务器: ./simple_auth_test.sh"
echo "     3. 使用修复后的配置重新启动 WiFiDog"
echo ""

echo "=== 诊断完成 ==="
