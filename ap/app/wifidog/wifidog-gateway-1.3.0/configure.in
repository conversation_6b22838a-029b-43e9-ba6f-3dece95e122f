## -*-m4-*-
# $Id$

dnl Process this file with autoconf to produce a configure script.

# FILE:
# configure.in
#
# FUNCTION:
# implements checks for a variety of system-specific functions

AC_INIT(src/common.h)
AM_CONFIG_HEADER(config.h)
AC_CONFIG_AUX_DIR(config)
AC_PROG_CC
AC_PROG_CXX
#AC_PROG_RANLIB

AC_SUBST(BUILDROOT)

# we use Semantic Versioning x.y.z tag for release, docs: http://semver.org/
WIFIDOG_MAJOR_VERSION=1
WIFIDOG_MINOR_VERSION=3
WIFIDOG_MICRO_VERSION=0
WIFIDOG_VERSION=$WIFIDOG_MAJOR_VERSION.$WIFIDOG_MINOR_VERSION.$WIFIDOG_MICRO_VERSION

AC_SUBST(WIFIDOG_MAJOR_VERSION)
AC_SUBST(WIFIDOG_MINOR_VERSION)
AC_SUBST(WIFIDOG_MICRO_VERSION)
AC_SUBST(WIFIDOG_VERSION)
AM_INIT_AUTOMAKE(wifidog,$WIFIDOG_VERSION)


AM_MAINTAINER_MODE

AC_PROG_INSTALL

AC_LIBTOOL_DLOPEN
AM_PROG_LIBTOOL

AC_ISC_POSIX
AC_C_BIGENDIAN
AC_PROG_MAKE_SET
AC_HEADER_STDC

 
# check for doxygen, mostly stolen from http://log4cpp.sourceforge.net/
# ----------------------------------------------------------------------------
AC_DEFUN([BB_ENABLE_DOXYGEN],
[
AC_ARG_ENABLE(doxygen, [  --enable-doxygen        enable documentation generation with doxygen (auto)])
AC_ARG_ENABLE(dot, [  --enable-dot            use 'dot' to generate graphs in doxygen (auto)])              
AC_ARG_ENABLE(html-docs, [  --enable-html-docs      enable HTML generation with doxygen (yes)], [], [ enable_html_docs=yes])              
AC_ARG_ENABLE(latex-docs, [  --enable-latex-docs     enable LaTeX documentation generation with doxygen (no)], [], [ enable_latex_docs=no])              
if test "x$enable_doxygen" = xno; then
        enable_doc=no
else 
        AC_PATH_PROG(DOXYGEN, doxygen, , $PATH)
        if test x$DOXYGEN = x; then
                if test "x$enable_doxygen" = xyes; then
                        AC_MSG_ERROR([could not find doxygen])
                fi
                enable_doc=no
        else
                enable_doc=yes
                AC_PATH_PROG(DOT, dot, , $PATH)
        fi
fi
AM_CONDITIONAL(DOC, test x$enable_doc = xyes)

if test x$DOT = x; then
        if test "x$enable_dot" = xyes; then
                AC_MSG_ERROR([could not find dot])
        fi
        enable_dot=no
else
        enable_dot=yes
fi
AM_CONDITIONAL(ENABLE_DOXYGEN, test x$enable_doc = xtrue)
AC_SUBST(enable_dot)
AC_SUBST(enable_html_docs)
AC_SUBST(enable_latex_docs)
])

# Acutally perform the doxygen check
BB_ENABLE_DOXYGEN

# Enable cyassl?
AC_DEFUN([BB_CYASSL],
[
AC_ARG_ENABLE(cyassl, [  --enable-cyassl        enable TLS support for auth server communication (no)], [], [enable_cyassl=no])
if test "x$enable_cyassl" = xyes; then
        # CyaSSL has been renamed wolfSSL. Old method names are still available
        # via cyassl/ssl.h, which maps old methods to new methods via macros.
        # To find the proper lib to link against (cyassl or wolfssl), we do have
        # the use the new naming scheme below as cyassl/ssl.h is not available for
        # AC_SEARCH_LIBS
        AC_CHECK_HEADERS(cyassl/ssl.h)
        AC_SEARCH_LIBS([CyaTLSv1_client_method], [cyassl], [], [
            AC_SEARCH_LIBS([wolfTLSv1_client_method], [wolfssl], [], [
                            AC_MSG_ERROR([unable to locate SSL lib: either wolfSSL or CyaSSL needed.])
            ])
        ])

        AC_MSG_CHECKING([for the CyaSSL SNI enabled])
        AC_LINK_IFELSE([AC_LANG_PROGRAM(
        [[
                #define HAVE_SNI
                #include <cyassl/ssl.h>
        ]], [[
                CYASSL_CTX *ctx;
                CyaSSL_Init();
                ctx = CyaSSL_CTX_new(CyaTLSv1_client_method());
                CyaSSL_CTX_UseSNI(ctx, CYASSL_SNI_HOST_NAME, "wifidog.org", 11);
        ]])], [enabled_sni=yes], [enabled_sni=no])

        if test "x$enabled_sni" = xyes; then
                AC_MSG_RESULT([yes])
                AC_DEFINE([HAVE_SNI],, "Compile with CyaSSL SNI support")
        else
                AC_MSG_RESULT([no])
        fi

        AC_DEFINE(USE_CYASSL,, "Compile with CyaSSL support")
fi
])

# Actually perform the cyassl check
BB_CYASSL



# check for pthread
AC_CHECK_HEADER(pthread.h, , AC_MSG_ERROR(You need the pthread headers) )
AC_CHECK_LIB(pthread, pthread_create, , AC_MSG_ERROR(You need the pthread library) )

# libhttpd dependencies
echo "Begining libhttpd dependencies check"
AC_CHECK_HEADERS(string.h strings.h stdarg.h unistd.h)
AC_HAVE_LIBRARY(socket)
AC_HAVE_LIBRARY(nsl)
echo "libhttpd dependencies check complete"

AC_OUTPUT( 		Makefile 
	   		wifidog.spec
			wifidog-msg.html
			src/Makefile
			libhttpd/Makefile
			doc/Makefile
			)

