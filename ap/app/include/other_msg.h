/**
 * @file other_msg.h
 * @brief �ṩ��ƽ̨�ڲ�Сģ�����ϢID����DM��FOTA��SNTP�ȣ�at_ctl��mainctl������������Ĺ��ܵ���ϢID�Ͻ��ڴ˶���
 *
 * Copyright (C) 2017 Sanechips Technology Co., Ltd.
 * <AUTHOR>
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 */
#ifndef __OTHER_MSG_H__
#define __OTHER_MSG_H__

/*******************************************************************************
 *                           Include header files                              *
 ******************************************************************************/
#include"message.h"

/*******************************************************************************
 *                             Macro definitions                               *
 ******************************************************************************/


/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/

/**
 * @brief
 */
enum other_msg_cmd {

	MSG_CMD_EMBMS_CTRL_CLOSE_SOCKET = MSG_CMD_OTHER_BASE,
	MSG_CMD_LINKUP_END,    //zte_mainctrlִ��linkup�ű��󣬷�����Ϣ��at_ctl
	MSG_CMD_LINKDOWN_END,  //zte_mainctrlִ��linkdown�ű��󣬷�����Ϣ��at_ctl
	//MSG_CMD_V6ADDR_IND,//��Ӧ�ṹ��V6ADDR_INFORM��IPv6 PDP����ʱ��slaac��ȡ��ipv6��ַ�ɹ���ʧ�ܺ󣬷�����Ϣ��at_ctl
	MSG_CMD_DATA_START,  //����ͳ��
	MSG_CMD_DATA_END,     //����ͳ��
	MSG_CMD_PARENT_CONTROL_SET, //���üҳ�����
	MSG_CMD_PARENT_CONTROL_CLOSE,                // �رռҳ�����
	MSG_CMD_SOFTTIMER_CREATE,
	MSG_CMD_SOFTTIMER_DELETE,
	MSG_CMD_SOFTTIMER_TIMEOUT,
	MSG_CMD_LOCALUPDATE_START,
	MSG_CMD_LOCALUPDATE_GET_RESULT,
	MSG_CMD_SNTP_SET_MODE,                  //����SNTPģʽ: �ֶ����Զ�
	MSG_CMD_SNTP_SET_TIME,                  //����SNTPʱ��
	MSG_CMD_SNTP_START,
	MSG_CMD_SNTP_REGISTER, //��Ҫ׼ȷʱ���Ӧ����sntpע��
	MSG_CMD_SNTP_UNREGISTER,
	MSG_CMD_SNTP_SUCCESS,

	/*���ٿ���/�ػ�֪ͨ��Ϣ��ö�ٶ�Ӧ�˿�*/
	MSG_CMD_FASTPOWER_IND,
	/*ģ��USB����/�γ�*/
	MSG_CMD_USBMOD_SETREQ,

	//����ΪоƬ��֤��Ϣ
	MSG_CMD_SEND_ATCMD_STREAM_REQ,
	MSG_CMD_SEND_ATCMD_STREAM_RSP,

	/****************************ISMS*******************************/
	MSG_CMD_ISMSSTART_REQ,	              //�������ܶ��Ž���
	MSG_CMD_ISMSREADY_RSP,              	//���ܶ����������֪ͨ
	MSG_CMD_ISMSINFO_REQ,                   //���ܶ��Ų�ѯ����
	MSG_CMD_ISMSDEL_REQ,                    //ɾ��ָ�����ܶ�������
	MSG_CMD_ISMSCLEAN_REQ,                  //���ȫ�����ܶ�������
	MSG_CMD_ISMSINFO_RSP,                  //�������ܶ�������(���������һ��)
	MSG_CMD_ISMSUD_REQ,                     // �ϴ��ն���Ϣ������

	/***************************DM*********************************/
	MSG_CMD_DM_CONFIG_REQ,                  //����dm���ֿ�����Ϣ

	/*************************�ػ���SAFECARE*************************/
	MSG_CMD_SAFECARE_ONOFF_REQ,             //������ر��ػ�����������
	MSG_CMD_SAFECARE_ONOFF_RSP,             //������ر��ػ���������Ӧ
	MSG_CMD_SAFECARE_ACCOUNT_REQ,           //��������
	MSG_CMD_SAFECARE_ACCOUNT_RSP,           //������Ӧ
	MSG_CMD_SAFECARE_MOBSITE_REQ,           //����Э���ַ����
	MSG_CMD_SAFECARE_MOBSITE_RSP,           //����Э���ַ��Ӧ
	MSG_CMD_SAFECARE_PLATLOCATION_REQ,      //ƽ̨��λ����
	MSG_CMD_SAFECARE_PLATLOCATION_RSP,      //ƽ̨��λ��Ӧ
	MSG_CMD_SAFECARE_PERIOD_LOCATION_REQ,   //���ڶ�λ����
	MSG_CMD_SAFECARE_PERIOD_LOCATION_RSP,   //���ڶ�λ��Ӧ
	MSG_CMD_SAFECARE_SMSNUM_REQ,            //���ж��ź����ȡ����
	MSG_CMD_SAFECARE_SMSNUM_RSP,            //���ж��ź����ȡ��Ӧ
	MSG_CMD_SAFECARE_LOCATION_REQ,          //��λ����
	MSG_CMD_SAFECARE_LOCATION_RSP,          //��λ��Ӧ

	MSG_CMD_SAFECARE_DATAUPDATE_REQ,        //����ȫ����������
	MSG_CMD_SAFECARE_DATAUPDATE_RSP,        //����ȫ��������Ӧ

	MSG_CMD_SAFECARE_SOS_LOCATION_REQ,      //SOS��λ����
	MSG_CMD_SAFECARE_SOS_LOCATION_RSP,      //SOS��λ��Ӧ

	MSG_CMD_SAFECARE_SENDKHSMS_REQ,         //�����Ϳ�������

	MSG_CMD_SAFECARE_POWER_ONOFF_REQ,       //������ر��ն�����
	MSG_CMD_SAFECARE_POWER_ONOFF_RSP,       //������ر��ն���Ӧ

	/*K318 T���л�ʹ�õ���Ϣbegin*/
	MSG_CMD_UDISKSTAT_SET_REQ,             //������������л���U��ģʽ/�����ģʽ
	MSG_CMD_UDISKSTAT_SET_RSP,             //��������Ļظ����л���U��ģʽ/�����ģʽ
	MSG_CMD_USBFLAG_STAT_REQ,              //����/�γ�USB�ߵ�֪ͨ��Ϣ
	/*K318 T���л�ʹ�õ���Ϣend*/

	/******************fota���*************************/
	MSG_CMD_FOTAMANUAL_REQ,
	MSG_CMD_FOTADL_REQ,
	MSG_CMD_FOTADY_REQ,
	MSG_CMD_FOTAVERSION_IND,
	MSG_CMD_FOTAPKG_IND,
	MSG_CMD_FOTARLT_IND,
	MSG_CMD_FOTADLRESUME_IND,
	MSG_CMD_FOTAUPRESUME_IND,

	/******************fota���*************************/

	MSG_GET_HOST_BY_NAME_SUCCESS, //��ȡhost��Ϣ�ɹ�
	MSG_GET_HOST_BY_NAME_TIMEOUT, //gehostbyname��Ϣ��ʱ
	
	/*************************�̶�̨WiFi*************************/
	MSG_CMD_WIFI_GET_STATE_REQ,				//��ȡWiFi״̬(����)����
	MSG_CMD_WIFI_GET_STATE_RSP,				//��ȡWiFi״̬(����)��Ӧ
	MSG_CMD_WIFI_GET_INFO_REQ,				//��ȡWiFi��Ϣ(SSID�����롢��ȫģʽ)����
	MSG_CMD_WIFI_GET_INFO_RSP,				//��ȡWiFi��Ϣ(SSID�����롢��ȫģʽ)��Ӧ
	MSG_CMD_WIFI_GET_USER_LIST_REQ,			//��ȡWiFi�û��б�����
	MSG_CMD_WIFI_GET_USER_LIST_RSP,			//��ȡWiFi�û��б���Ӧ
	
	MSG_CMD_WIFI_SET_ON_REQ,				//��WiFi����
	MSG_CMD_WIFI_SET_ON_RSP,				//��WiFi��Ӧ
	MSG_CMD_WIFI_SET_OFF_REQ,				//�ر�WiFi����
	MSG_CMD_WIFI_SET_OFF_RSP,				//�ر�WiFi��Ӧ
	MSG_CMD_WIFI_SET_SECURITY_REQ,			//����WiFi��ȫģʽ��SSID����������
	MSG_CMD_WIFI_SET_SECURITY_RSP,			//����WiFi��ȫģʽ��SSID�������Ӧ
	MSG_CMD_WIFI_NOTIFY_CHARGER_STATUS,
	MSG_CMD_WIFI_IS_READY,

	/*************************�̶�̨��������*************************/
	MSG_CMD_DATAMANAGER_SET_SWITCH_REQ,		 //���������������ش�����
	MSG_CMD_DATAMANAGER_SET_SWITCH_RSP,     //���������������ش򿪻�Ӧ
	MSG_CMD_DATAMANAGER_GET_INFO_REQ,		//��ȡ��ʹ�����������������澯ֵ����
	MSG_CMD_DATAMANAGER_GET_INFO_RSP,		//��ȡ��ʹ�����������������澯ֵ��Ӧ
	MSG_CMD_DATAMANAGER_SET_TOTAL_REQ,		//��������������
	MSG_CMD_DATAMANAGER_SET_TOTAL_RSP,		//������������Ӧ
	MSG_CMD_DATAMANAGER_SET_WARNVALUE_REQ,	//���������澯ֵ����
	MSG_CMD_DATAMANAGER_SET_WARNVALUE_RSP,	//���������澯ֵ��Ӧ
	MSG_CMD_DATAMANAGER_REACH_WARNVALUE,	//����ʹ�ôﵽ����
	MSG_CMD_DATAMANAGER_REACH_LIMIT,		//����ʹ�ôﵽ����
	MSG_CMD_DATAMANAGER_FLUXSTAT_READY,		//����ģ���������
	MSG_CMD_PDPSTATUS_SET_SWITCH_REQ,       //�����������ӿ���֪ͨ
	MSG_CMD_PDPSTATUS_SET_SWITCH_RSP,       //�����������ӿ�����Ӧ
    /*BIP���,BIP�ڲ��Լ��շ�����Ϣ*/
    MSG_CMD_ID_BIP_AT_RCV, 
    /*������ص���Ϣ*/
	MSG_CMD_LOCKLISTAUTH_REQ,
	MSG_CMD_LOCKNETLIST_SET_REQ,
	MSG_CMD_LOCKNETKEY_REQ,
	MSG_CMD_LOCKNETAMTSTATUS_GET_REQ,
	MSG_CMD_LOCKNETDIGEST_GET_REQ,
	MSG_CMD_LOCKNETSIGN_SET_REQ,
	MSG_CMD_LOCKNETLEVEL_GET_REQ,
	MSG_CMD_LOCKNETLIST_GET_REQ,
	MSG_CMD_LOCKNETAUTH_REQ,
	MSG_CMD_LOCKNETUNLOCKTIMES_GET_REQ,
	MSG_CMD_LOCKNETSTATUS_GET_REQ,
	MSG_CMD_LOCKNETUNLOCK_REQ,
	MSG_CMD_ZURDY_LOCKAUTH_REQ,
	/* zmr qrzl app msg cmd */
#ifdef ENABLE_QRZL_APP
	MSG_CMD_QRZL_APP_SET_BAND,
	MSG_CMD_QRZL_APP_WIFI_CONNECTED,
	MSG_CMD_QRZL_APP_WIFI_DISCONNECT
#endif
};

/**
 * @brief DMģ�������Ϣ������
 */
enum dm_msg_cmd {
	MSG_CMD_DMREG_INFO = MSG_CMD_DM_BASE,
	MSG_CMD_DMREG_FLAG,
	MSG_CMD_SEND_REGMSG
};

/**
 * @brief FOTAģ�������Ϣ������
 */
typedef enum {

	MSG_CMD_FOTA_DM_EVENT_START = MSG_CMD_FOTA_BASE,
	MSG_CMD_FOTA_DM_CI_POLLING,
	MSG_CMD_FOTA_DM_CI_REPORTING,        //�������ϱ�
	MSG_CMD_FOTA_DM_UI_INIT_FOTA,
	MSG_CMD_FOTA_DM_UI_CHANGE_PARA,  //WEBUI֪ͨDM���ò���(���ز˵�)
	MSG_CMD_FOTA_DM_NI_NIA,
	MSG_CMD_FOTA_DM_NI_BOOTSTRAP,
	MSG_CMD_FOTA_DM_MC_CHANGE_APN_SUCCEED,       //����֪ͨDM�л�APN�ɹ���Ϣ
	MSG_CMD_FOTA_DM_MC_CHANGE_APN_FAIL,  //����֪ͨDM�л�APNʧ����Ϣ
	MSG_CMD_FOTA_DM_MC_DATA_STATUS,      //����֪ͨDM������ҵ��
	MSG_CMD_FOTA_DM_MC_NODATA_STATUS,    //����֪ͨDM������ҵ��
	MSG_CMD_FOTA_DM_BSP_PROC_OK,         //bsp֪ͨDM�������
	MSG_CMD_FOTA_DM_BSP_PROC_FAILED,     //bsp֪ͨDM����ʧ��
	MSG_CMD_FOTA_DM_WAKE_UP_OK,//�����豸�ɹ�
	MSG_CMD_FOTA_DM_FALL_ASLEEP_OK,//�豸����
	MSG_CMD_FOTA_DM_POWER_ON,//��ʾdmģ�������
	MSG_CMD_FOTA_DM_DEVICE_IS_POWER_ON,//�豸�ӹػ�����ģʽ������������
	MSG_CMD_FOTA_DM_DEVICE_IS_POWER_OFF,//�豸���ڽ��г��ȷ�����״̬
	MSG_CMD_FOTA_DM_QUERY_SLEEP_FROM_SLEEP_MODULE,//˯��ģ���ø���Ϣ��ѯfota�ܹ�˯��
	MSG_CMD_FOTA_DM_IF_GET_VERSION_STATUS,
	MSG_CMD_FOTA_DM_IF_GET_CUR_OPS,
	MSG_CMD_FOTA_DM_IF_GET_UPDATE_CMD,
	MSG_CMD_FOTA_DM_IF_SET_UPDATE_CMD,
	MSG_CMD_FOTA_DM_IF_GET_DL_PROGRESS,
	MSG_CMD_FOTA_DM_IF_GET_FOTA_PKG_INFO,
	MSG_CMD_FOTA_DM_IF_GET_UPDATE_RESULT,
	MSG_CMD_FOTA_DM_IF_GET_UPDATE_SETTINGS,
	MSG_CMD_FOTA_DM_IF_SET_UPDATE_SETTINGS,

	MSG_CMD_FOTA_WEBUI_START_FOTA,
	MSG_CMD_FOTA_WEBUI_START_DOWNLOAD,
	MSG_CMD_FOTA_WEBUI_CHANGE_PARAMETER,
	MSG_CMD_FOTA_DM_START_UPDATE,

	MSG_CMD_FOTA_DM_VERIFY,
	MSG_CMD_FOTA_DM_DO_UPDATE,

	MSG_CMD_FOTA_DM_ENEVT_END

} E_DM_Event_Type;

enum amt_device_test_msg_cmd  {
	MSG_CMD_AMT_KEY_TEST_START_REQ   = MSG_CMD_AMT_DEVICE_TEST_BASE,			//��ʼ����������Ϣ����
	MSG_CMD_AMT_KEY_TEST_START_RSP,				//��ʼ����������Ϣ��Ӧ
	MSG_CMD_AMT_KEY_TEST_STOP_REQ,				//ֹͣ����������Ϣ����
	MSG_CMD_AMT_KEY_TEST_STOP_RSP,				//ֹͣ����������Ϣ��Ӧ
	MSG_CMD_AMT_LCD_TEST_START_REQ,				//��ʼLCD��Ļ������Ϣ����
	MSG_CMD_AMT_LCD_TEST_START_RSP,				//��ʼLCD��Ļ������Ϣ��Ӧ
	MSG_CMD_AMT_LCD_TEST_STOP_REQ,				//ֹͣLCD��Ļ������Ϣ����
	MSG_CMD_AMT_LCD_TEST_STOP_RSP,				//ֹͣLCD��Ļ������Ϣ��Ӧ
	MSG_CMD_AMT_BATTERY_VOLTAGE_TEST_REQ,		//��ص�ѹ������Ϣ����
	MSG_CMD_AMT_BATTERY_VOLTAGE_TEST_RSP,		//��ص�ѹ������Ϣ��Ӧ
	MSG_CMD_AMT_LCD_BACKLIGHT_TEST_START_REQ,		//��ʼLCD���������Ϣ����
	MSG_CMD_AMT_LCD_BACKLIGHT_TEST_START_RSP,		//��ʼLCD���������Ϣ��Ӧ
	MSG_CMD_AMT_LCD_BACKLIGHT_TEST_STOP_REQ,		//ֹͣLCD���������Ϣ����
	MSG_CMD_AMT_LCD_BACKLIGHT_TEST_STOP_RSP,		//ֹͣLCD���������Ϣ��Ӧ
	MSG_CMD_AMT_VIBRATOR_TEST_START_REQ,		//��ʼ���������Ϣ����
	MSG_CMD_AMT_VIBRATOR_TEST_START_RSP,		//��ʼ���������Ϣ��Ӧ
	MSG_CMD_AMT_VIBRATOR_TEST_STOP_REQ,		//ֹͣ���������Ϣ����
	MSG_CMD_AMT_VIBRATOR_TEST_STOP_RSP,		//ֹͣ���������Ϣ��Ӧ
	MSG_CMD_AMT_CAMERA_TEST_START_REQ,		//��ʼ����ͷ������Ϣ����
	MSG_CMD_AMT_CAMERA_TEST_START_RSP,		//��ʼ����ͷ������Ϣ��Ӧ
	MSG_CMD_AMT_CAMERA_TEST_STOP_REQ,		//ֹͣ����ͷ������Ϣ����
	MSG_CMD_AMT_CAMERA_TEST_STOP_RSP,		//ֹͣ����ͷ������Ϣ��Ӧ
	MSG_CMD_AMT_SPEAKER_TEST_REQ,		//���-���Ȼ�·������Ϣ����
	MSG_CMD_AMT_SPEAKER_TEST_RSP,		//���-���Ȼ�·������Ϣ��Ӧ
	MSG_CMD_AMT_SPEAKER_TEST_STOP_REQ,		//���-���Ȼ�·����ֹͣ��Ϣ����
	MSG_CMD_AMT_SPEAKER_TEST_STOP_RSP,		//���-���Ȼ�·����ֹͣ��Ϣ��Ӧ
	MSG_CMD_AMT_RECEIVER_TEST_REQ,		//������������Ϣ����
	MSG_CMD_AMT_RECEIVER_TEST_RSP,		//������������Ϣ��Ӧ
	MSG_CMD_AMT_RECEIVER_TEST_STOP_REQ,		//����������ֹͣ��Ϣ����
	MSG_CMD_AMT_RECEIVER_TEST_STOP_RSP,		//����������ֹͣ��Ϣ��Ӧ
	MSG_CMD_AMT_TP_TEST_START_REQ,		//��ʼ������������Ϣ����
	MSG_CMD_AMT_TP_TEST_START_RSP,		//��ʼ������������Ϣ��Ӧ
	MSG_CMD_AMT_TP_TEST_STOP_REQ,		//ֹͣ������������Ϣ����
	MSG_CMD_AMT_TP_TEST_STOP_RSP,		//ֹͣ������������Ϣ��Ӧ
	MSG_CMD_AMT_GSENSOR_TEST_START_REQ,		//��ʼG-Sensor������Ϣ����
	MSG_CMD_AMT_GSENSOR_TEST_START_RSP,		//��ʼG-Sensor������Ϣ��Ӧ
	MSG_CMD_AMT_GSENSOR_TEST_STOP_REQ,		//ֹͣG-Sensor������Ϣ����
	MSG_CMD_AMT_GSENSOR_TEST_STOP_RSP,		//ֹͣG-Sensor������Ϣ��Ӧ
	MSG_CMD_AMT_WIFI_TEST_START_REQ,		//��ʼwifi������Ϣ����
	MSG_CMD_AMT_WIFI_TEST_START_RSP,		//��ʼwifi������Ϣ��Ӧ
	MSG_CMD_AMT_FLASHLIGHT_START_REQ,       //��ʼ�ֵ�Ͳ������Ϣ����
	MSG_CMD_AMT_FLASHLIGHT_START_RSP,       //��ʼ�ֵ�Ͳ������Ϣ��Ӧ
};

/*********************************************************************
			�ҳ�����ģ����Ϣ��
**********************************************************************/
typedef struct {
	char strAtCmd[128];
	int deviceID;
} PCS_MSG;

typedef struct{
	int cid;
	int iptype;  //enum ipv4v6_flag
}TScriptMSG;
#endif
