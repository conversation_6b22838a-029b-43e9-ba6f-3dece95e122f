/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_keystokes.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��MMI��������
*  ��            ��  ��
*  ��            ��  ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
*******************************************************************************/
/*****************************************************************************
                         ͷ�ļ�
******************************************************************************/
#include <fcntl.h>
#include <sys/syscall.h>
#include <linux/reboot.h>
#include <linux/input.h>
#include "mmi_common.h"
#include "mmi_lcd.h"

extern int g_customer_type;

/************************************************************************************
                            ȫ�ֱ�������
***********************************************************************************/
static int g_mmi_kpd_handle = -1;//��ȡ�����豸���
static BOOL g_longPressFlag = FALSE;//����Ƿ񳤰�
static BOOL g_key_wakeup_flag = FALSE;//��ʾMMI����ʱ���� ֻ���ѵ� ���������ܲ���Ӧ
static long s_mmi_keypress_begin = 0;//�������º�ʼ��ʱ
BOOL g_mmi_key_longpress_flag = FALSE;//����Ƿ�Ϊmmi����power������Ĺػ�
extern E_zMmiShowMode g_showMode;
extern UINT32 g_fast_poweron;
#ifdef QRZL_APP_CUSTOMIZATION_HMM
static int g_mmi_kpd_longpress_time = 5;//�������ж�ʱ��S
static int g_mmi_wifi_longpress_time = 6;//����wifi���ж�ʱ��S, ҢԶcpe
#else
static int g_mmi_kpd_longpress_time = 3;//�������ж�ʱ��S
static int g_mmi_wifi_longpress_time = 5;//����wifi���ж�ʱ��S, ҢԶcpe
#endif

static int g_mmi_wps_longpress_time = 6;//����wps������wpsʱ��S, demo
static int g_mmi_wps_bandswitch_time = 3;//����wps������bandswitchʱ��S, demo



BOOL mmi_get_wakeup_flag(VOID);
/**********************************************************************************
��������:��ȡ����ʱ��
***********************************************************************************/
static long mmi_get_keypress_time()
{
	struct timeval tv;
	long second;
	gettimeofday(&tv, NULL);
	second = tv.tv_sec;
	return second;
}

/* Started by AICoder, pid:gac14u7fcdk415a14acc0b6e2061f106cf662f0b */
static long long mmi_get_keypress_time_ms()
{
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (long long)tv.tv_sec * 1000 + tv.tv_usec / 1000;
}
/* Ended by AICoder, pid:gac14u7fcdk415a14acc0b6e2061f106cf662f0b */


/************************************************************************************
 ��������:��ȡ������Ϣ
***********************************************************************************/
static VOID mmi_get_keystrokes_data(MMI_KP_INFO *kp_info)
{

	int retR = -1;
	struct input_event kp_event = {0};

	retR = read(g_mmi_kpd_handle, (CHAR *)(&kp_event), sizeof(kp_event));
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI  Key's type:%d, value:%d, code:%d\n", kp_event.type, kp_event.value, kp_event.code);
	if (retR < 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI get_keystrokes_data read falied k-err:%d!",errno);
		return;
	}

	if (kp_event.type == 1) {
		if (kp_event.value == 1) {
			set_wake_lock(MMI_MAIN_LOCK_ID);
			kp_info->status = KEY_STATUS_DOWN;
			s_mmi_keypress_begin = mmi_get_keypress_time();
			if (kp_event.code == KEY_WIFI_CODE) {
				if (mmi_get_wifi_state()) {
					g_mmi_wifi_longpress_time = 5;
				} else {
					g_mmi_wifi_longpress_time = 1;
				}
			}
		} else if (kp_event.value == 0) {
			kp_info->status = KEY_STATUS_UP;
		#ifdef WIFI_UNCOEXIST_5G
			if (kp_event.code == KEY_WPS_CODE) {
				long key_up = mmi_get_keypress_time();
				int key_interval = (int)(key_up - s_mmi_keypress_begin);
				if (key_interval >= g_mmi_wps_bandswitch_time &&
					key_interval < g_mmi_wps_longpress_time) {
					kp_info->status = KEY_STATUS_LONGUP;
				}
			}
		#endif
			s_mmi_keypress_begin = 0;
		} else if (kp_event.value == 2) {
			long sec_end = mmi_get_keypress_time();
			int sec_lingpress = (int)(sec_end - s_mmi_keypress_begin);
			if (kp_event.code == KEY_WIFI_CODE) {
				if (sec_lingpress >= g_mmi_wifi_longpress_time) {
					kp_info->status = KEY_STATUS_LONGPRESS;
				}
			}
		#ifdef WIFI_UNCOEXIST_5G
			else if (kp_event.code == KEY_WPS_CODE) {
				if (sec_lingpress >= g_mmi_wps_longpress_time) {
					kp_info->status = KEY_STATUS_LONGPRESS;
				}
			}
		#endif
			else {
				if (sec_lingpress >= g_mmi_kpd_longpress_time) {
					kp_info->status = KEY_STATUS_LONGPRESS;
				}
			}
		}
		if (kp_event.code == KEY_POWER_CODE) {
			kp_info->type = KEY_TYPE_POWER;
		} else if (kp_event.code == KEY_RESET_CODE) {
			kp_info->type = KEY_TYPE_RESET;
		} else if (kp_event.code == KEY_WPS_CODE) {
			kp_info->type = KEY_TYPE_WPS;
		} else if (kp_event.code == KEY_WIFI_CODE) {
			kp_info->type = KEY_TYPE_WIFI;
		}
	} else {
		kp_info->status = (KEY_STATUS)0;
		return;
	}

}

/***********************************************************************************
   ��������:��������wifi
***********************************************************************************/
static VOID mmi_wakeup_wifi(int key_type)
{
#ifndef QRZL_KEY_NO_WAKEUP_WIFI
	if (key_type == KEY_TYPE_WPS || key_type == KEY_TYPE_RESET) {
		if (!mmi_get_wifi_state()) {
			if (g_fast_poweron) {
				if (mmi_get_lcd_mode() != MMI_FAKE_POWEROFF_MODE && mmi_get_lcd_mode() != MMI_FAKE_POWEROFF_CHARGE_MODE
				    && mmi_get_led_mode() != MMI_FAKE_POWEROFF_MODE && mmi_get_led_mode() != MMI_FAKE_POWEROFF_CHARGE_MODE) {
					mmi_wifi_operate(WIFI_TURN_ON);
				}
			} else
				mmi_wifi_operate(WIFI_TURN_ON);
		}
	}
	if (key_type == KEY_TYPE_POWER) { //LCDģʽ��ע�������ͻ��ѣ�LEDģʽ��û����Ʋ���
		if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
			if ((mmi_get_lcd_mode() == MMI_ACTIVE_MODE || mmi_get_led_mode() == MMI_ACTIVE_MODE) && mmi_get_wakeup_flag()) {
				mmi_wifi_operate(WIFI_TURN_ON);
			}
		} else {
			if ((mmi_get_lcd_mode() == MMI_ACTIVE_MODE || mmi_get_led_mode() == MMI_ACTIVE_MODE) && !mmi_get_wifi_state()) {
				mmi_wifi_operate(WIFI_TURN_ON);
			}
		}
	}
#endif
}
/***********************************************************************************
   ��������:���ð������ѱ�ʶ
***********************************************************************************/
VOID mmi_set_wakeup_flag(BOOL flag)
{
	if(g_customer_type == CUSTOMER_NANDIAN){//����û��lcd led����������Ҫ���ѹ���
		g_key_wakeup_flag = FALSE;
	}else{
		g_key_wakeup_flag = flag;
	}
}

/***********************************************************************************
   ��������:���ذ������ѱ�ʶ
***********************************************************************************/
BOOL mmi_get_wakeup_flag(VOID)
{
	return g_key_wakeup_flag;
}

/***********************************************************************************
   ��������:���ð���������ʶ
***********************************************************************************/
VOID mmi_set_longpress_flag(BOOL flag)
{
	g_longPressFlag = flag;
}

/***********************************************************************************
   ��������:���ذ���������ʶ
***********************************************************************************/
BOOL mmi_get_longpress_flag(VOID)
{
	return g_longPressFlag;
}

/***********************************************************************************
   ��������:��������LED/LCD �����û��ѱ�־λ
***********************************************************************************/
static VOID mmi_set_active_mode_key()
{
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		E_zMmi_Work_Mode lcd_mode = mmi_get_lcd_mode();
		if (lcd_mode == MMI_BACKLIGHT_OFF_MODE || lcd_mode == MMI_BACKLIGHT_HALFBRIGHT_MODE) {
			mmi_set_wakeup_flag(TRUE);
		} else {
			if (g_fast_poweron) {
				if (lcd_mode == MMI_FAKE_POWEROFF_MODE && (mmi_get_charge_state() == STATE_CHARGING || mmi_get_charge_state() == STATE_FULL)) {
					mmi_set_lcd_mode(MMI_FAKE_POWEROFF_CHARGE_MODE);
					mmi_set_update_flag(MMI_TASK_CTRL);
					mmi_set_wakeup_flag(TRUE);
				}
			}
		}
	}

	if (g_showMode == MMI_MODE_LED || g_showMode == MMI_MODE_ALL) {
		E_zMmi_Work_Mode led_mode = mmi_get_led_mode();
		if (led_mode == MMI_IDLE_LEDOFF_MODE || led_mode == MMI_IDLE_STANDBY_LEDOFF_MODE || led_mode == MMI_IDLE_CHG_LEDOFF_MODE) {
			mmi_set_wakeup_flag(TRUE);
		}
	}
	mmi_set_mode_active();
}


/***********************************************************************************
   ��������:down������Ϣ����
***********************************************************************************/
static VOID mmi_handle_keystroke_down()
{
	BOOL long_press = mmi_get_longpress_flag();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI  KEYDOWN  long_press=%d!!!\n ", long_press);
	if (FALSE == long_press) {
		// ���ö�ʱ��
		if (g_fast_poweron && (mmi_get_charge_state() == STATE_DISCHARGE) && ((mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_MODE) || (mmi_get_led_mode() == MMI_FAKE_POWEROFF_MODE))) {
			mmi_reset_fast_poweron_idle_timer();
		} else {
			mmi_reset_idle_timer();
		}

		mmi_set_active_mode_key();
		
		if (mmi_get_wifi_state()) {
			//mmi_send_message(MODULE_ID_WIFI, (USHORT)MSG_CMD_WIFI_RESET_TIMER, 0, NULL);

			ipc_send_message(MODULE_ID_MMI, MODULE_ID_WIFI, MSG_CMD_WIFI_RESET_TIMER, 0, NULL, 0);
		}
	}
}

/***********************************************************************************
   ��������:up������Ϣ����
***********************************************************************************/
static VOID mmi_handle_keystroke_up(int key_type)
{
	BOOL longPress = mmi_get_longpress_flag();
	BOOL key_wakeup = mmi_get_wakeup_flag();

	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI  KEYUP KEYUP !!! longPress=%d key_wakeup=%d\n", longPress, key_wakeup);
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI  KEYUP KEYUP !!! key_type=%d g_showMode=%d\n", key_type, g_showMode);
	mmi_wakeup_wifi(key_type);
#ifndef DISABLE_LCD
	if (FALSE == longPress && (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL)) {
#if defined(QRZL_UE) && defined(JCV_HW_MZ801_V1_2)
		if (key_type == KEY_TYPE_POWER) {
			if (mmi_get_lcd_mode() == MMI_ACTIVE_MODE && key_wakeup == FALSE && mmi_getSSIDFirstShowInfo() == FALSE) {
				mmi_handle_lcd_key_switch_page();
			}
		} 
#else
		if (key_type == KEY_TYPE_WPS) {
			if (mmi_get_lcd_mode() == MMI_ACTIVE_MODE && key_wakeup == FALSE && mmi_getSSIDFirstShowInfo() == FALSE) {
				mmi_handle_lcd_key_switch_page();
			}
		} else if (key_type == KEY_TYPE_POWER && mmi_getShowingPowerOnInfo() == FALSE) {
			if (mmi_get_lcd_mode() == MMI_ACTIVE_MODE && key_wakeup == FALSE) {
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI  mmi_handle_keystroke_up ssssss");
				mmi_idle_timer_stop();
				mmi_set_lcd_mode(MMI_BACKLIGHT_OFF_MODE);
				mmi_set_update_flag(MMI_TASK_CTRL);

			}
		}
#endif	
	}

#elif defined(QRZL_DIGITS_SLEEP_WAKEUP_FEATURE)
	//LED模式 数码管休眠
    if(FALSE == longPress && g_showMode == MMI_MODE_LED) {
		if (key_type == KEY_TYPE_POWER) {
			/* Power键松开事件 唤醒数码管显示电量和状态*/
			if (key_wakeup == FALSE) {
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI Power key wakeup digits display\n");
				//mmi_idle_timer_stop();
				// 保存LED旧模式
				//E_zMmi_Work_Mode old_led_mode = mmi_get_led_mode();
				//mmi_set_led_mode(MMI_DIGITS_WAKEUP_MODE);
				//mmi_set_update_flag(MMI_TASK_CTRL);
				// 恢复LED旧模式 不影响LED 显示逻辑
				//mmi_set_led_mode(old_led_mode);

				mmi_digits_wakeup();
			}
		}
	}
#endif
	if (TRUE == longPress) {
		mmi_set_longpress_flag(FALSE); //��ǳ�������
	}
	if (TRUE == key_wakeup) {
		mmi_set_wakeup_flag(FALSE);
	}
}

/* Started by AICoder, pid:y687138c18p93cd14e22084ab030f62a54f5c11a */
static VOID mmi_handle_keystroke_longup(int key_type)
{
	BOOL longPress = mmi_get_longpress_flag();
	BOOL key_wakeup = mmi_get_wakeup_flag();

	slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI KEYUP long !!! longPress=%d key_wakeup=%d\n", longPress, key_wakeup);
	mmi_wakeup_wifi(key_type);
#ifndef DISABLE_LCD
	if (FALSE == longPress && (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL)) {
		if (key_type == KEY_TYPE_WPS) {
			if (mmi_get_lcd_mode() == MMI_ACTIVE_MODE && key_wakeup == FALSE && mmi_getSSIDFirstShowInfo() == FALSE) {
				mmi_wifi_bandswitch();
			}
		}
	}
#endif
	if (TRUE == longPress) {
		mmi_set_longpress_flag(FALSE); //��ǳ�������
	}
	if (TRUE == key_wakeup) {
		mmi_set_wakeup_flag(FALSE);
	}
}
/* Ended by AICoder, pid:y687138c18p93cd14e22084ab030f62a54f5c11a */


/***********************************************************************************
   �������ó�������Ϣ����
***********************************************************************************/
static VOID mmi_handle_keystroke_longpress(int key_type)
{
	BOOL longPress = mmi_get_longpress_flag();
	BOOL key_wakeup = mmi_get_wakeup_flag();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI  mmi_handle_keystroke_longpress  longPress=%d key_wakeup=%d\n", longPress, key_wakeup);
	if ((FALSE == longPress) && (FALSE == key_wakeup)) {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI KEYLONGPRESS!KEYLONGPRESS!\n");
		mmi_set_longpress_flag(TRUE);
		if (key_type == KEY_TYPE_POWER) {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI handleKey ######KEY_POWER LONGPRESS######\n\n");

			if (g_fast_poweron) {
				if (mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_MODE || mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE
				    || mmi_get_led_mode() == MMI_FAKE_POWEROFF_MODE || mmi_get_led_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE) {
					g_mmi_key_longpress_flag = FALSE;
					mmi_handle_fast_poweron();
				} else {
					g_mmi_key_longpress_flag = TRUE;

					slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI poweroff,longpress!!!\n");
					mmi_set_poweroff();
				}
			} else {
				slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI poweroff,longpress!!!\n");
				mmi_set_poweroff();
			}
		} else if (key_type == KEY_TYPE_RESET) {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI handleKey ######KEY_RESET LONGPRESS######\n\n");
			if (mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE || mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_MODE
			    || mmi_get_led_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE || mmi_get_led_mode() == MMI_FAKE_POWEROFF_MODE) {
				return;//break;
			}
			mmi_set_factory_reset();
		} else if ((key_type == KEY_TYPE_WPS)) {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI handleKey ######KEY_WPS LONGPRESS######\n");
			if (mmi_get_wifi_state()) {
				mmi_wps_operate();
			}
		} else if ((key_type == KEY_TYPE_WIFI)) {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI handleKey ######KEY_WIFI LONGPRESS######\n");
			if (mmi_get_wifi_state()) {
				mmi_wifi_operate(WIFI_TURN_OFF);
			} else {
				mmi_wifi_operate(WIFI_TURN_ON);
			}
		} else {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI invalid operation!!\n");
		}
	}
}


/***********************************************************************************
   ��������:��������
***********************************************************************************/
VOID *mmi_handle_keystrokes_thread(VOID *arg)
{
	MMI_KP_INFO keyData = {0};


	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI handleKey handle_keystrokes_thread begin !!\n ");
	prctl(PR_SET_NAME, "mmihandlekey", 0, 0, 0);
	while (1) {
		mmi_get_keystrokes_data(&keyData);

		switch (keyData.status) {
		case KEY_STATUS_DOWN: {
			mmi_handle_keystroke_down();
			break;
		}
		
		case KEY_STATUS_LONGUP: {
			mmi_handle_keystroke_longup(keyData.type);
			break;
		}

		case KEY_STATUS_UP: {
			mmi_handle_keystroke_up(keyData.type);
			break;
		}
		case KEY_STATUS_LONGPRESS: {
			mmi_handle_keystroke_longpress(keyData.type);
			break;
		}
		default: {
			break;
		}
		}
	}

}
#if 0
/***********************************************************************************
   ��������:�Զ�������ģ�ⰴ������������
***********************************************************************************/
SINT32 zMMI_Handle_Msg_Atest_Key(VOID *data)
{
	//value:�����¼� code:������ֵ
	autotest_key_rspmsg *kp_event = (autotest_key_rspmsg *)data;
	MMI_KP_INFO keyData = {0};

	if (kp_event->value == 1) {
		set_wake_lock(MMI_MAIN_LOCK_ID);
		keyData.status = KEY_STATUS_DOWN;
		s_mmi_keypress_begin = mmi_get_keypress_time();
	} else if (kp_event->value == 0) {
		keyData.status = KEY_STATUS_UP;
		s_mmi_keypress_begin = 0;
	} else if (kp_event->value == 2) {
		long sec_end = mmi_get_keypress_time();
		int sec_lingpress = (int)(sec_end - s_mmi_keypress_begin);
		if (sec_lingpress >= g_mmi_kpd_longpress_time) {
			keyData.status = KEY_STATUS_LONGPRESS;
		}
	}
	
	if (kp_event->code == KEY_POWER_CODE) {
		keyData.type = KEY_TYPE_POWER;
	} else if (kp_event->code == KEY_RESET_CODE) {
		keyData.type = KEY_TYPE_RESET;
	} else if (kp_event->code == KEY_WPS_CODE) {
		keyData.type = KEY_TYPE_WPS;
	}

	switch (keyData.status) {
		case KEY_STATUS_DOWN: {
			mmi_handle_keystroke_down();
			break;
		}
		case KEY_STATUS_UP: {
			mmi_handle_keystroke_up(keyData.type);
			break;
		}
		case KEY_STATUS_LONGPRESS: {
			mmi_handle_keystroke_longpress(keyData.type);
			break;
		}
		default: {
			break;
		}
	}
	return 0;
}
#endif

/************************************************************************************
 ��������:������ʼ��
***********************************************************************************/
VOID mmi_keyStrokes_init(VOID)
{
	pthread_t  mmi_handlekey_thread;
	/*�����ƿ���flag�ź���*/
	g_mmi_kpd_handle = open(KPD_PATH, O_RDONLY);
	if (g_mmi_kpd_handle == -1) {
		slog(MMI_PRINT, SLOG_ERR, "handleKey Couldn't open kp /dev/event0\n");
		return;//MMI_ASSERT(0);
	}
	if(g_customer_type == CUSTOMER_GUODIAN || g_customer_type == CUSTOMER_NANDIAN){
		g_mmi_kpd_longpress_time = 1;//1s
	}
	if (pthread_create(&mmi_handlekey_thread, NULL, &mmi_handle_keystrokes_thread, NULL) == -1) {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI create handle_keystrokes_thread error\n");
		return ;
	}
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_keyStrokes_Init finish!!\n");
}

