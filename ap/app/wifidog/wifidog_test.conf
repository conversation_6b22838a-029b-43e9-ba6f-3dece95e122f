# WiFiDog Test Configuration for ZTE MiFi
# This is a minimal working configuration for testing WiFiDog functionality

# Gateway identification
GatewayID zte_mifi_test

# Network interfaces - adjust these based on your actual interface names
# For ZTE MiFi, common interfaces are: wlan0, br0, eth0
GatewayInterface wlan0
# ExternalInterface eth0  # Usually auto-detected

# Gateway network settings
GatewayAddress ***********
GatewayPort 2060

# Authentication server configuration
# For testing, you can use a simple HTTP server or WiFiDog-auth
AuthServer {
    Hostname *************
    HTTPPort 80
    Path /wifidog/
    LoginScriptPathFragment login/?
    PortalScriptPathFragment portal/?
    MsgScriptPathFragment gw_message.php?
    PingScriptPathFragment ping/?
    AuthScriptPathFragment auth/?
}

# Alternative auth server for testing with external service
# Uncomment if you have a public auth server
#AuthServer {
#    Hostname auth.example.com
#    HTTPPort 80
#    SSLAvailable no
#    Path /
#}

# Daemon settings
Daemon 1

# HTTP server settings
HTTPDName WiFiDog-ZTE
HTTPDMaxConn 10
HTTPDRealm WiFiDog

# Optional: HTTP authentication for status page
# HTTPDUserName admin
# HTTPDPassword wifidog123

# Client management
CheckInterval 60
ClientTimeout 5

# Popular servers for connectivity check
PopularServers www.google.com,www.baidu.com,www.qq.com

# Trusted MAC addresses (for testing - bypass authentication)
# Replace with actual MAC addresses for testing
# TrustedMACList 00:11:22:33:44:55,aa:bb:cc:dd:ee:ff

# Firewall rules
FirewallRuleSet global {
    # Allow DNS
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
    
    # Allow DHCP
    FirewallRule allow udp port 67
    FirewallRule allow tcp port 67
    
    # Block SMTP to prevent spam
    FirewallRule block tcp port 25
    
    # Allow common web ports
    FirewallRule allow tcp port 80
    FirewallRule allow tcp port 443
}

FirewallRuleSet validating-users {
    FirewallRule allow to 0.0.0.0/0
}

FirewallRuleSet known-users {
    FirewallRule allow to 0.0.0.0/0
}

FirewallRuleSet unknown-users {
    # Allow DNS for captive portal detection
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
    
    # Allow DHCP
    FirewallRule allow udp port 67
    FirewallRule allow tcp port 67
    
    # Allow access to auth server
    FirewallRule allow tcp port 80 to *************
    
    # Allow captive portal detection for various devices
    # Apple devices
    FirewallRule allow tcp port 80 to apple.com
    FirewallRule allow tcp port 443 to apple.com
    
    # Android devices
    FirewallRule allow tcp port 80 to google.com
    FirewallRule allow tcp port 443 to google.com
    
    # Microsoft devices
    FirewallRule allow tcp port 80 to microsoft.com
    FirewallRule allow tcp port 443 to microsoft.com
}

FirewallRuleSet locked-users {
    FirewallRule block to 0.0.0.0/0
}
