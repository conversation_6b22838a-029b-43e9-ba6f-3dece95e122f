#!/bin/sh

# 分析WiFiDog认证页面不弹出的问题
# 基于wifidog.log和配置文件进行诊断

echo "=== WiFiDog 认证页面问题分析 ==="
echo "时间: $(date)"
echo ""

# 1. 分析日志中的关键信息
echo "1. 分析日志中的关键信息:"
echo ""

# 检查认证服务器连接状态
echo "   认证服务器连接状态:"
grep -E "(Auth Server Says|Successfully connected to auth server|Failed to connect)" ap/app/wifidog/wifidog.log | tail -5 | sed 's/^/     /'
echo ""

# 检查客户端连接和重定向
echo "   客户端连接和重定向记录:"
grep -E "(Captured.*requesting|Redirecting client browser)" ap/app/wifidog/wifidog.log | tail -10 | sed 's/^/     /'
echo ""

# 检查客户端MAC地址获取
echo "   客户端MAC地址获取:"
grep -E "Got client MAC address" ap/app/wifidog/wifidog.log | tail -5 | sed 's/^/     /'
echo ""

# 2. 分析配置文件
echo "2. 分析配置文件 (wifidog_local_test.conf):"
echo ""

# 检查认证服务器配置
echo "   认证服务器配置:"
grep -A 10 "AuthServer" ap/app/wifidog/wifidog_local_test.conf | sed 's/^/     /'
echo ""

# 检查防火墙规则配置
echo "   防火墙规则配置 - global规则集:"
sed -n '/FirewallRuleSet global/,/^}/p' ap/app/wifidog/wifidog_local_test.conf | sed 's/^/     /'
echo ""

echo "   防火墙规则配置 - unknown-users规则集:"
sed -n '/FirewallRuleSet unknown-users/,/^}/p' ap/app/wifidog/wifidog_local_test.conf | sed 's/^/     /'
echo ""

# 3. 检查当前iptables规则
echo "3. 检查当前iptables规则:"
echo ""

echo "   NAT表中的WiFiDog规则:"
iptables -t nat -L -n | grep -E "(WiFiDog|REDIRECT)" | sed 's/^/     /'
echo ""

echo "   FILTER表中的WiFiDog规则:"
iptables -t filter -L -n | grep -E "(WiFiDog)" | head -10 | sed 's/^/     /'
echo ""

# 4. 检查认证服务器可达性
echo "4. 检查认证服务器可达性:"
echo ""

AUTH_SERVER="***************"
AUTH_PORT="9080"

echo "   测试认证服务器连接 ($AUTH_SERVER:$AUTH_PORT):"
if nc -z $AUTH_SERVER $AUTH_PORT 2>/dev/null; then
    echo "     ✓ 认证服务器端口可达"
    
    # 测试ping接口
    echo "   测试认证服务器ping接口:"
    ping_response=$(curl -s --connect-timeout 5 "http://$AUTH_SERVER:$AUTH_PORT/wifidog/ping/" 2>/dev/null)
    if [ "$ping_response" = "Pong" ]; then
        echo "     ✓ Ping接口正常响应: $ping_response"
    else
        echo "     ✗ Ping接口响应异常: $ping_response"
    fi
else
    echo "     ✗ 认证服务器端口不可达"
fi
echo ""

# 5. 问题分析和诊断
echo "5. 问题分析和诊断:"
echo ""

# 检查是否有重定向规则
redirect_rules=$(iptables -t nat -L -n | grep "REDIRECT.*2060" | wc -l)
if [ $redirect_rules -gt 0 ]; then
    echo "   ✓ 发现REDIRECT规则，WiFiDog应该能够拦截HTTP请求"
else
    echo "   ✗ 未发现REDIRECT规则，这可能是问题所在"
fi

# 检查是否有客户端连接记录
client_connections=$(grep -c "Captured.*requesting" ap/app/wifidog/wifidog.log)
if [ $client_connections -gt 0 ]; then
    echo "   ✓ 发现客户端连接记录 ($client_connections 次)，WiFiDog正在拦截请求"
    
    # 检查重定向URL
    echo "   最近的重定向URL:"
    grep "Redirecting client browser" ap/app/wifidog/wifidog.log | tail -1 | sed 's/.*to /     /'
else
    echo "   ✗ 未发现客户端连接记录，WiFiDog可能没有拦截到HTTP请求"
fi

# 检查认证服务器配置问题
auth_server_ip=$(grep "Hostname" ap/app/wifidog/wifidog_local_test.conf | awk '{print $2}')
if [ "$auth_server_ip" = "***************" ]; then
    echo "   ⚠ 认证服务器配置为***************，但配置文件注释说明认证服务器在***************"
    echo "     请确认认证服务器确实在此IP运行"
else
    echo "   ✓ 认证服务器配置: $auth_server_ip"
fi

echo ""

# 6. 可能的原因和解决方案
echo "6. 可能的原因和解决方案:"
echo ""

echo "   基于日志分析，发现以下情况:"
echo ""

# 分析日志中的具体问题
if grep -q "Captured.*requesting.*captive.apple.com" ap/app/wifidog/wifidog.log; then
    echo "   ✓ 检测到Apple设备的captive portal检测请求"
    echo "   ✓ WiFiDog正确拦截了HTTP请求并进行重定向"
    echo ""
    
    # 检查重定向是否成功
    if grep -q "Redirecting client browser" ap/app/wifidog/wifidog.log; then
        echo "   ✓ WiFiDog已生成重定向响应"
        echo ""
        
        # 可能的问题
        echo "   可能的问题:"
        echo "   1. 客户端设备可能无法访问认证服务器***************:9080"
        echo "   2. 认证服务器可能没有正确响应登录页面请求"
        echo "   3. 客户端设备的captive portal检测可能被其他因素干扰"
        echo ""
        
        echo "   建议的解决方案:"
        echo "   1. 确认simple_auth_server.py在***************:9080正常运行"
        echo "   2. 检查客户端设备是否能直接访问 http://***************:9080/wifidog/login/"
        echo "   3. 尝试在客户端设备上手动访问任意HTTP网站，观察是否弹出认证页面"
        echo "   4. 检查客户端设备的网络设置，确保没有使用代理或VPN"
        echo ""
    else
        echo "   ✗ WiFiDog没有生成重定向响应，这是问题所在"
    fi
else
    echo "   ✗ 没有检测到客户端的HTTP请求被拦截"
    echo "   这表明WiFiDog的iptables规则可能没有正确工作"
    echo ""
    
    echo "   建议的解决方案:"
    echo "   1. 检查iptables规则是否正确应用"
    echo "   2. 确认客户端设备确实连接到了br0网络"
    echo "   3. 尝试重启WiFiDog服务"
    echo "   4. 检查防火墙配置是否过于严格"
fi

echo ""
echo "=== 分析完成 ==="
