# $Id: wifidog.conf 1375 2008-09-30 10:20:06Z wichert $
# WiFiDog Configuration file

# Parameter: GatewayID
# Default: default
# Optional
#
# Set this to the node ID on the auth server
# This is used to give a customized login page to the clients and for
# monitoring/statistics purpose. If you run multiple gateways on the same
# machine each gateway needs to have a different gateway id.
# If none is supplied, the mac address of the GatewayInterface interface will be used,
# without the : separators

# GatewayID default

# Parameter: ExternalInterface
# Default: NONE
# Optional
#
# Set this to the external interface (the one going out to the Inernet or your larger LAN).  
# Typically vlan1 for OpenWrt, and eth0 or ppp0 otherwise,
# Normally autodetected

# ExternalInterface eth0

# Parameter: GatewayInterface
# Default: NONE
# Mandatory
#
# Set this to the internal interface (typically your wifi interface).    
# Typically br0 for whiterussian, br-lan for kamikaze (by default the wifi interface is bridged with wired lan in openwrt)
# and eth1, wlan0, ath0, etc. otherwise
# You can get this interface with the ifconfig command and finding your wifi interface

GatewayInterface br0

# Parameter: GatewayAddress
# Default: Find it from GatewayInterface
# Optional
#
# Set this to the internal IP address of the gateway.  Not normally required.

# GatewayAddress ***********

# Parameter: HtmlMessageFile
# Default: wifidog-msg.html
# Optional
#
# This allows you to specify a custome HTML file which will be used for
# system errors by the gateway. Any $title, $message and $node variables
# used inside the file will be replaced.
#
# HtmlMessageFile /opt/wifidog/etc/wifidog-.html

# Parameter: AuthServer
# Default: NONE
# Mandatory, repeatable
#
# This allows you to configure your auth server(s).  Each one will be tried in order, untill one responds.
# Set this to the hostname or IP of your auth server(s), the path where
# WiFiDog-auth resides in and the port it listens on.
#AuthServer {
#	Hostname                 (Mandatory; Default: NONE)
#	SSLAvailable             (Optional; Default: no; Possible values: yes, no)
#	SSLPort                  (Optional; Default: 443)
#	HTTPPort                 (Optional; Default: 80)
#	Path                     (Optional; Default: /wifidog/ Note:  The path must be both prefixed and suffixed by /.  Use a single / for server root.)
#   LoginScriptPathFragment  (Optional; Default: login/? Note:  This is the script the user will be sent to for login.)
#   PortalScriptPathFragment (Optional; Default: portal/? Note:  This is the script the user will be sent to after a successfull login.)
#   MsgScriptPathFragment    (Optional; Default: gw_message.php? Note:  This is the script the user will be sent to upon error to read a readable message.)
#   PingScriptPathFragment    (Optional; Default: ping/? Note:  This is the script the user will be sent to upon error to read a readable message.)
#   AuthScriptPathFragment    (Optional; Default: auth/? Note:  This is the script the user will be sent to upon error to read a readable message.)
#}

#AuthServer {
#    Hostname auth.ilesansfil.org
#    SSLAvailable yes
#    Path /
#}

#AuthServer {
#    Hostname auth2.ilesansfil.org
#    SSLAvailable yes
#    Path /
#}

# Parameter: Daemon
# Default: 1
# Optional
#
# Set this to true if you want to run as a daemon
# Daemon 1

# Parameter: GatewayPort
# Default: 2060
# Optional
#
# Listen on this port
# GatewayPort 2060

# Parameter: ProxyPort
# Default: 0 (disable)
# Optional
#
# Redirect http traffic of knowns & probations users
# to a local transparent proxy listening on ProxyPort port
# ProxyPort 0

# Parameter: HTTPDName
# Default: WiFiDog
# Optional
#
# Define what name the HTTPD server will respond
# HTTPDName WiFiDog

# Parameter: HTTPDMaxConn
# Default: 10
# Optional
#
# How many sockets to listen to
# HTTPDMaxConn 10

# Parameter: HTTPDRealm
# Default: WiFiDog
# Optional
#
# The name of the HTTP authentication realm. This only used when a user
# tries to access a protected WiFiDog internal page. See HTTPUserName.
# HTTPDRealm WiFiDog

# Parameter: HTTPDUserName / HTTPDPassword
# Default: unset
# Optional
#
# The gateway exposes some information such as the status page through its web
# interface. This information can be protected with a username and password,
# which can be set through the HTTPDUserName and HTTPDPassword parameters.
# HTTPDUserName admin
# HTTPDPassword secret

# Parameter: CheckInterval
# Default: 60
# Optional
#
# How many seconds should we wait between timeout checks.  This is also
# how often the gateway will ping the auth server and how often it will
# update the traffic counters on the auth server.  Setting this too low
# wastes bandwidth, setting this too high will cause the gateway to take 
# a long time to switch to it's backup auth server(s).

# CheckInterval 60

# Parameter: ClientTimeout
# Default: 5
# Optional
#
# Set this to the desired of number of CheckInterval of inactivity before a client is logged out
# The timeout will be INTERVAL * TIMEOUT
ClientTimeout 5

# Parameter: TrustedMACList
# Default: none
# Optional
#
# Comma separated list of MAC addresses who are allowed to pass
# through without authentication
#TrustedMACList 00:00:DE:AD:BE:AF,00:00:C0:1D:F0:0D

# Parameter: FirewallRuleSet
# Default: none
# Mandatory
#
# Groups a number of FirewallRule statements together.

# Parameter: FirewallRule
# Default: none
# 
# Define one firewall rule in a rule set.

# Rule Set: global
# 
# Used for rules to be applied to all other rulesets except locked.
FirewallRuleSet global {
    ## To block SMTP out, as it's a tech support nightmare, and a legal liability
    #FirewallRule block tcp port 25
    
    ## Use the following if you don't want clients to be able to access machines on 
    ## the private LAN that gives internet access to wifidog.  Note that this is not
    ## client isolation;  The laptops will still be able to talk to one another, as
    ## well as to any machine bridged to the wifi of the router.
    # FirewallRule block to ***********/16
    # FirewallRule block to **********/12
    # FirewallRule block to 10.0.0.0/8
    
    ## This is an example ruleset for the Teliphone service.
    #FirewallRule allow udp to ************/27
    #FirewallRule allow udp to **********/27
    #FirewallRule allow tcp port 80 to ************
    
    ## Use the following to log or ulog the traffic you want to allow or block.
    # For OPENWRT: use of these feature requires modules ipt_LOG or ipt_ULOG present in dependencies
    # iptables-mod-extra and iptables-mod-ulog (to adapt it to the linux distribution).
    # Note: the log or ulog rule must be passed before, the rule you want to match.
    # for openwrt: use of these feature requires modules ipt_LOG or ipt_ULOG present in dependencies
    # iptables-mod-extra and iptables-mod-ulog
    # For example, you want to log (ulog works the same way) the traffic allowed on port 80 to the ip ************:
    #FirewallRule log tcp port 80 to ************
    #FirewallRule allow tcp port 80 to ************
    # And you want to know, who matche your block rule:
    #FirewallRule log to 0.0.0.0/0
    #FirewallRule block to 0.0.0.0/0
}

# Rule Set: validating-users
#
# Used for new users validating their account
FirewallRuleSet validating-users {
    FirewallRule allow to 0.0.0.0/0
}

# Rule Set: known-users
#
# Used for normal validated users.
FirewallRuleSet known-users {
    FirewallRule allow to 0.0.0.0/0
}

# Rule Set: unknown-users
#
# Used for unvalidated users, this is the ruleset that gets redirected.
#
# XXX The redirect code adds the Default DROP clause.
FirewallRuleSet unknown-users {
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
    FirewallRule allow udp port 67
    FirewallRule allow tcp port 67
}

# Rule Set: locked-users
#
# Not currently used
FirewallRuleSet locked-users {
    FirewallRule block to 0.0.0.0/0
}
