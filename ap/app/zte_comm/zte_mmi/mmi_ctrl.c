/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_ctrl.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��MMIʡ�紦��
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
******************************************************************************/

/*****************************************************************************
                         ͷ�ļ�
******************************************************************************/
#include "mmi_common.h"
#include "mmi_lcd.h"

/*****************************************************************************
                         ȫ�ֱ�������
******************************************************************************/
//lcd/led�Ĺ���ģʽ����������������/�ơ�ʡ���
static E_zMmi_Work_Mode g_mmi_lcd_mode = MMI_ACTIVE_MODE;
static E_zMmi_Work_Mode g_mmi_led_mode = MMI_ACTIVE_MODE;
SINT32 g_mmi_softtimer_flag = 0;//��������softtimer�ص��Ƿ�ִ�е�debug����

/*****************************************************************************
                         ���ñ���
******************************************************************************/
extern pthread_mutex_t g_mmi_lcdmode_mutex;
extern pthread_mutex_t g_mmi_ledmode_mutex;
extern E_zMmiShowMode g_showMode;
extern UINT32 g_led_sleep_mode;
extern UINT32 g_led_standby_mode;


/*****************************************************************************
 ����˵��:ע��������ƿ���ģ��
******************************************************************************/
SINT32 mmi_get_ctrlinfo(UINT32 ctrlinfo)
{
	if (ctrlinfo != 0) {
		T_zMMICtrlInfo * pCtrlInfo = (T_zMMICtrlInfo *)ctrlinfo;
		pCtrlInfo->lcdmode = mmi_get_lcd_mode();
		pCtrlInfo->ledmode = mmi_get_led_mode();
	}
	return MMI_SUCCESS;
}


SINT32 mmi_RegisterCtrlTaskInfoItem()
{
	T_zMMITaskInfoItem ctrlInfoItem = {0};
	ctrlInfoItem.task = MMI_TASK_CTRL;
	ctrlInfoItem.taskinfo = (VOID*)malloc(sizeof(T_zMMICtrlInfo));
	ctrlInfoItem.get_taskinfo_fun = mmi_get_ctrlinfo;
	ctrlInfoItem.get_ledinfo_fun = mmi_getLedCtrlInfo;
#ifndef DISABLE_LCD
	ctrlInfoItem.get_lcdinfo_fun = mmi_getLcdCtrlInfo;
#endif
	ctrlInfoItem.ledinfo = (VOID*)malloc(sizeof(T_zMmi_Led_Info));
	//ctrlInfoItem.show_mode = 2;
	mmi_register_taskinfo_item(&ctrlInfoItem);
	return MMI_SUCCESS;
}

/**********************************************************************************
��������:����ʱ�ް����ر���,�ȱ䰵 5��
***********************************************************************************/
static VOID * mmi_idle_halfbright_timer_cb(VOID *arg)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_idle_halfbright_timer_cb !!\n");
	mmi_set_lcd_mode(MMI_BACKLIGHT_OFF_MODE);
	mmi_set_update_flag(MMI_TASK_CTRL);
	return NULL;
}

static VOID mmi_idle_halfbright_timer_start(VOID)
{
	int ret = -1;
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_idle_halfbright_timer_start !!\n");
#if MMI_SOFTTIMER_DEBUG
	g_mmi_softtimer_flag = 5;
#endif
	ret = CreateSoftTimer(SET_BACKLIGHT_HALFBRIGHT_TIMER, TIMER_FLAG_ONCE, SET_BACKLIGHT_HALFBRIGHT_TIME, &mmi_idle_halfbright_timer_cb, NULL);
	if (ret != 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_idle_halfbright_timer_start FAILED !!");
	}
}

static VOID mmi_idle_halfbright_timer_stop(VOID)
{

	DeleteSoftTimer(SET_BACKLIGHT_HALFBRIGHT_TIMER);
}

SINT32 zMMI_Handle_Msg_BacklightOff(VOID *data)
{
	//mmi_idle_halfbright_timer_start();
	mmi_set_lcd_mode(MMI_BACKLIGHT_OFF_MODE);
	mmi_set_update_flag(MMI_TASK_CTRL);
	return 0;
}
/**********************************************************************************
��������:MMI����ʱ��ʱ���Ļص�����
***********************************************************************************/
static VOID * mmi_idle_timer_cb(VOID *arg)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_idle_timer_cb !!\n");
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		if (mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE || mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_MODE) {
			mmi_set_lcd_mode(MMI_FAKE_POWEROFF_MODE);
			mmi_set_update_flag(MMI_TASK_CTRL);
		} else {
			mmi_set_lcd_mode(MMI_BACKLIGHT_HALFBRIGHT_MODE);
			mmi_set_update_flag(MMI_TASK_CTRL);
			//mmi_send_message(MODULE_ID_MMI, (USHORT)MSG_CMD_MMISTART_BACKLIGHTOFF_TIMER,  0, NULL);
			ipc_send_message(MODULE_ID_MMI, MODULE_ID_MMI, MSG_CMD_MMISTART_BACKLIGHTOFF_TIMER, 0, NULL, 0);
		}
	}

	if (g_led_sleep_mode) {
		if (g_showMode == MMI_MODE_LED || g_showMode == MMI_MODE_ALL) {
			E_zMmi_Work_Mode led_mode = mmi_get_led_mode();
			if (led_mode == MMI_FAKE_POWEROFF_MODE || led_mode == MMI_IDLE_STANDBY_LEDOFF_MODE) {
				mmi_set_update_flag(MMI_TASK_CTRL);
				return NULL;
			}
			if (led_mode == MMI_FAKE_POWEROFF_CHARGE_MODE) {
				return NULL;
			}
			if (mmi_get_charge_state() == STATE_CHARGING || mmi_get_charge_state() == STATE_FULL) {
				mmi_set_led_mode(MMI_IDLE_CHG_LEDOFF_MODE);
			} else if (g_led_standby_mode && mmi_get_wifi_state() == FALSE) {
				mmi_set_led_mode(MMI_IDLE_STANDBY_LEDOFF_MODE);
			} else {
				mmi_set_led_mode(MMI_IDLE_LEDOFF_MODE);
			}
			mmi_set_update_flag(MMI_TASK_CTRL);
		}
	}
	return NULL;
}

/**********************************************************************************
��������:����ʱ�ް����ر��ⶨʱ����ʱ��20�룬һ���Զ�ʱ��
***********************************************************************************/
static VOID mmi_idle_timer_create(VOID)
{
	int ret = -1;
	ret = CreateSoftTimer(SET_MMI_IDLE_TIMER, TIMER_FLAG_ONCE, SET_MMI_IDLE_TIME, &mmi_idle_timer_cb, NULL);
	if (ret != 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_idle_timer_create FAILED !!");
	}
}

/**********************************************************************************
��������:STOP ����ʱ�ް�����ƶ�ʱ��
***********************************************************************************/
VOID mmi_idle_timer_stop(VOID)
{

	DeleteSoftTimer(SET_MMI_IDLE_TIMER);
}

/**********************************************************************************
��������:MMI���ٹػ�ʱ��ʱ���Ļص�����
***********************************************************************************/
static VOID * mmi_fast_poweron_idle_timer_cb(VOID *arg)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_fast_poweron_idle_timer_cb !!\n");
	if (mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_MODE && (mmi_get_charge_state() == STATE_DISCHARGE)) {
		mmi_set_update_flag(MMI_TASK_CTRL);
	}
	return NULL;
}

/**********************************************************************************
��������:���ٹػ�ʱ�̰�������˯�߶�ʱ����ʱ��4�룬һ���Զ�ʱ��
***********************************************************************************/
static VOID mmi_fast_poweron_idle_timer_create(VOID)
{
	int ret = -1;
	ret = CreateSoftTimer(SET_MMI_FAST_POWERON_IDLE_TIMER, TIMER_FLAG_ONCE, SET_MMI_FAST_POWERON_IDLE_TIME, &mmi_fast_poweron_idle_timer_cb, NULL);
	if (ret != 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_fast_poweron_idle_timer_create FAILED\n");
	}
}

/**********************************************************************************
��������:KILL���ٹػ�ʱʡ�綨ʱ��
***********************************************************************************/
VOID mmi_fast_poweron_idle_timer_stop(VOID)
{

	DeleteSoftTimer(SET_MMI_FAST_POWERON_IDLE_TIMER);
}

/**********************************************************************************
��������:����/��ȡ��ǰLCDģʽ
***********************************************************************************/
E_zMmi_Work_Mode mmi_get_lcd_mode(VOID)
{
	E_zMmi_Work_Mode tmpMode = MMI_ACTIVE_MODE;
	mmi_getMutex(&g_mmi_lcdmode_mutex);
	tmpMode = g_mmi_lcd_mode;
	mmi_putMutex(&g_mmi_lcdmode_mutex);
	return tmpMode;
}

VOID mmi_set_lcd_mode(E_zMmi_Work_Mode tmpMode)
{
	mmi_getMutex(&g_mmi_lcdmode_mutex);
	g_mmi_lcd_mode = tmpMode;
	mmi_putMutex(&g_mmi_lcdmode_mutex);
}

/**********************************************************************************
��������:����/��ȡ��ǰLEDģʽ
***********************************************************************************/
E_zMmi_Work_Mode mmi_get_led_mode(VOID)
{
	E_zMmi_Work_Mode tmpMode = MMI_ACTIVE_MODE;
	mmi_getMutex(&g_mmi_ledmode_mutex);
	tmpMode = g_mmi_led_mode;
	mmi_putMutex(&g_mmi_ledmode_mutex);
	return tmpMode;
}

VOID mmi_set_led_mode(E_zMmi_Work_Mode tmpMode)
{
	mmi_getMutex(&g_mmi_ledmode_mutex);
	g_mmi_led_mode = tmpMode;
	mmi_putMutex(&g_mmi_ledmode_mutex);
}

/**********************************************************************************
��������:��MMI��Ϊæµ״̬
***********************************************************************************/
VOID mmi_set_mode_active(VOID)
{
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		E_zMmi_Work_Mode lcd_mode = mmi_get_lcd_mode();
		if (lcd_mode == MMI_BACKLIGHT_OFF_MODE || lcd_mode == MMI_BACKLIGHT_HALFBRIGHT_MODE) {
			mmi_set_lcd_mode(MMI_ACTIVE_MODE);
			mmi_set_update_flag(MMI_TASK_CTRL);
		}
	}
	if (g_showMode == MMI_MODE_LED || g_showMode == MMI_MODE_ALL) {
		E_zMmi_Work_Mode led_mode = mmi_get_led_mode();
		if (led_mode == MMI_IDLE_LEDOFF_MODE || led_mode == MMI_IDLE_STANDBY_LEDOFF_MODE || led_mode == MMI_IDLE_CHG_LEDOFF_MODE) {
			mmi_set_led_mode(MMI_ACTIVE_MODE);
			mmi_set_update_flag(MMI_TASK_CTRL);
		}
	}
}

/**********************************************************************************
��������:MMIʡ��ʱ����ض�ʱ��reset��������USB���ʱ����
***********************************************************************************/
VOID mmi_reset_idle_timer(VOID)
{
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		mmi_idle_halfbright_timer_stop();
	}
	mmi_idle_timer_stop();
	mmi_idle_timer_create();
}

/**********************************************************************************
��������:���ٹػ�ʱ���а���������MMIʡ��ʱ����ض�ʱ��reset������ʱ����
***********************************************************************************/
VOID mmi_reset_fast_poweron_idle_timer(VOID)
{
	mmi_fast_poweron_idle_timer_stop();
	mmi_fast_poweron_idle_timer_create();
}

/**********************************************************************************
��������:MMIʡ��ʱ����ض�ʱ��KILL��  �ػ�������ʱ����
***********************************************************************************/
VOID mmi_kill_idle_timer(VOID)
{
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		mmi_idle_halfbright_timer_stop();
	}
	mmi_idle_timer_stop();
	mmi_fast_poweron_idle_timer_stop();
}

/**********************************************************************************
��������:��ʼ��MMIʡ�����
***********************************************************************************/
VOID mmi_init_idle_control(VOID)
{
	mmi_idle_timer_create();
}

#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
/**********************************************************************************
函数名称:数码管休眠定时器回调函数
***********************************************************************************/
static VOID * mmi_digits_sleep_timer_cb(VOID *arg)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_digits_sleep_timer_cb: digits entering sleep mode\n");
	mmi_digits_sleep();
	return NULL;
}

/**********************************************************************************
函数名称:启动数码管休眠定时器，定时2分钟后让数码管进入休眠状态
***********************************************************************************/
VOID mmi_digits_sleep_timer_start(VOID)
{
	int ret = -1;
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_digits_sleep_timer_start\n");
	ret = CreateSoftTimer(SET_DIGITS_SLEEP_TIMER, TIMER_FLAG_ONCE, SET_DIGITS_SLEEP_TIME, &mmi_digits_sleep_timer_cb, NULL);
	if (ret != 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_digits_sleep_timer_start FAILED\n");
	} 
}

/**********************************************************************************
函数名称:停止数码管休眠定时器
***********************************************************************************/
VOID mmi_digits_sleep_timer_stop(VOID)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_digits_sleep_timer_stop\n");
	DeleteSoftTimer(SET_DIGITS_SLEEP_TIMER);
}
#endif

