#!/bin/sh

# Simple WiFiDog Authentication Server for Testing
# 在 ZTE MiFi 设备上运行的简单认证服务器

PORT=8080
PID_FILE="/tmp/simple_auth_server.pid"

# 检查是否已经在运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        echo "Authentication server is already running (PID: $PID)"
        exit 1
    else
        rm -f "$PID_FILE"
    fi
fi

# 创建临时目录
mkdir -p /tmp/wifidog_auth

# 创建认证页面
cat > /tmp/wifidog_auth/login.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WiFiDog 测试认证</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; text-align: center; }
        .container { max-width: 400px; margin: 0 auto; }
        .button { background: #4CAF50; color: white; padding: 15px 32px; 
                 text-decoration: none; display: inline-block; font-size: 16px; 
                 margin: 4px 2px; cursor: pointer; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WiFiDog 测试认证</h1>
        <p>欢迎使用 WiFiDog 测试系统</p>
        <p>点击下方按钮完成认证</p>
        <a href="/wifidog/auth?token=test123" class="button">立即认证</a>
    </div>
</body>
</html>
EOF

# 创建成功页面
cat > /tmp/wifidog_auth/success.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证成功</title>
    <meta http-equiv="refresh" content="3;url=http://www.baidu.com">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; text-align: center; }
        .container { max-width: 400px; margin: 0 auto; }
        .success { color: #4CAF50; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="success">认证成功！</h1>
        <p>您已成功通过认证</p>
        <p>3秒后自动跳转到百度...</p>
    </div>
</body>
</html>
EOF

# 启动简单的 HTTP 服务器
echo "Starting simple authentication server on port $PORT..."

# 使用 netcat 创建简单的 HTTP 服务器
while true; do
    {
        # 读取 HTTP 请求
        read -r request_line
        echo "Request: $request_line" >&2
        
        # 解析请求路径
        path=$(echo "$request_line" | cut -d' ' -f2)
        
        case "$path" in
            "/wifidog/ping"*)
                # WiFiDog ping 请求
                echo "HTTP/1.1 200 OK"
                echo "Content-Type: text/plain"
                echo "Content-Length: 4"
                echo ""
                echo "Pong"
                ;;
            "/wifidog/auth"*)
                # WiFiDog 认证请求
                echo "HTTP/1.1 200 OK"
                echo "Content-Type: text/plain"
                echo "Content-Length: 7"
                echo ""
                echo "Auth: 1"
                ;;
            "/wifidog/login"*)
                # 登录页面
                content=$(cat /tmp/wifidog_auth/login.html)
                content_length=$(echo -n "$content" | wc -c)
                echo "HTTP/1.1 200 OK"
                echo "Content-Type: text/html; charset=UTF-8"
                echo "Content-Length: $content_length"
                echo ""
                echo "$content"
                ;;
            "/wifidog/portal"*)
                # 成功页面
                content=$(cat /tmp/wifidog_auth/success.html)
                content_length=$(echo -n "$content" | wc -c)
                echo "HTTP/1.1 200 OK"
                echo "Content-Type: text/html; charset=UTF-8"
                echo "Content-Length: $content_length"
                echo ""
                echo "$content"
                ;;
            *)
                # 默认响应
                echo "HTTP/1.1 404 Not Found"
                echo "Content-Type: text/plain"
                echo "Content-Length: 9"
                echo ""
                echo "Not Found"
                ;;
        esac
        
        # 读取并丢弃剩余的请求头
        while read -r line; do
            [ -z "$line" ] && break
        done
        
    } | nc -l -p $PORT
    
    # 检查是否需要停止
    [ -f "/tmp/stop_auth_server" ] && break
done &

# 保存 PID
echo $! > "$PID_FILE"

echo "Simple authentication server started (PID: $!)"
echo "Listening on port $PORT"
echo "To stop: rm /tmp/stop_auth_server && kill \$(cat $PID_FILE)"
echo ""
echo "Test URLs:"
echo "  http://192.168.100.1:$PORT/wifidog/ping"
echo "  http://192.168.100.1:$PORT/wifidog/login"
echo "  http://192.168.100.1:$PORT/wifidog/auth"
