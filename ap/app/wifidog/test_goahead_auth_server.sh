#!/bin/sh

# 测试goahead作为WiFiDog认证服务器的功能

echo "=== 测试goahead WiFiDog认证服务器接口 ==="
echo "时间: $(date)"
echo ""

# 配置参数
GATEWAY_IP="*************"
GATEWAY_PORT="80"
TEST_MAC="aa:bb:cc:dd:ee:ff"
TEST_IP="***************"
TEST_TOKEN="test_token_12345"

echo "测试参数:"
echo "  Gateway IP: $GATEWAY_IP"
echo "  Gateway Port: $GATEWAY_PORT"
echo "  Test MAC: $TEST_MAC"
echo "  Test IP: $TEST_IP"
echo "  Test Token: $TEST_TOKEN"
echo ""

# 1. 测试Ping接口
echo "1. 测试Ping接口:"
ping_url="http://$GATEWAY_IP:$GATEWAY_PORT/goform/goform_set_cmd_process?goformId=ONELINK_PING&gw_id=zte_mifi_onelink_test&sys_uptime=12345&sys_memfree=50000&sys_load=0.5&wifidog_uptime=3600"

echo "   URL: $ping_url"
echo "   响应:"
curl -s "$ping_url" | sed 's/^/     /'
echo ""
echo ""

# 2. 测试登录重定向接口
echo "2. 测试登录重定向接口:"
login_url="http://$GATEWAY_IP:$GATEWAY_PORT/goform/goform_set_cmd_process?goformId=ONELINK_LOGIN&gw_address=$GATEWAY_IP&gw_port=2060&gw_id=zte_mifi_onelink_test&mac=$TEST_MAC&ip=$TEST_IP&url=http://www.baidu.com"

echo "   URL: $login_url"
echo "   响应 (应该是重定向到OneLink认证页面):"
curl -s -I "$login_url" | sed 's/^/     /'
echo ""

# 3. 测试认证验证接口 - 登录阶段
echo "3. 测试认证验证接口 - 登录阶段:"
auth_login_url="http://$GATEWAY_IP:$GATEWAY_PORT/goform/goform_set_cmd_process?goformId=ONELINK_AUTH&stage=login&ip=$TEST_IP&mac=$TEST_MAC&token=$TEST_TOKEN&gw_id=zte_mifi_onelink_test"

echo "   URL: $auth_login_url"
echo "   响应:"
curl -s "$auth_login_url" | sed 's/^/     /'
echo ""
echo ""

# 4. 测试认证验证接口 - 计数器更新阶段
echo "4. 测试认证验证接口 - 计数器更新阶段:"
auth_counters_url="http://$GATEWAY_IP:$GATEWAY_PORT/goform/goform_set_cmd_process?goformId=ONELINK_AUTH&stage=counters&ip=$TEST_IP&mac=$TEST_MAC&token=$TEST_TOKEN&incoming=1024&outgoing=2048&gw_id=zte_mifi_onelink_test"

echo "   URL: $auth_counters_url"
echo "   响应:"
curl -s "$auth_counters_url" | sed 's/^/     /'
echo ""
echo ""

# 5. 测试Portal页面接口
echo "5. 测试Portal页面接口:"
portal_url="http://$GATEWAY_IP:$GATEWAY_PORT/goform/goform_set_cmd_process?goformId=ONELINK_PORTAL&gw_id=zte_mifi_onelink_test&mac=$TEST_MAC&token=$TEST_TOKEN"

echo "   URL: $portal_url"
echo "   响应:"
curl -s "$portal_url" | sed 's/^/     /'
echo ""
echo ""

# 6. 测试消息页面接口
echo "6. 测试消息页面接口:"
msg_url="http://$GATEWAY_IP:$GATEWAY_PORT/goform/goform_set_cmd_process?goformId=ONELINK_MSG&message=Test%20error%20message"

echo "   URL: $msg_url"
echo "   响应:"
curl -s "$msg_url" | sed 's/^/     /'
echo ""
echo ""

# 7. 测试认证验证接口 - 登出阶段
echo "7. 测试认证验证接口 - 登出阶段:"
auth_logout_url="http://$GATEWAY_IP:$GATEWAY_PORT/goform/goform_set_cmd_process?goformId=ONELINK_AUTH&stage=logout&ip=$TEST_IP&mac=$TEST_MAC&token=$TEST_TOKEN&gw_id=zte_mifi_onelink_test"

echo "   URL: $auth_logout_url"
echo "   响应:"
curl -s "$auth_logout_url" | sed 's/^/     /'
echo ""
echo ""

# 8. 检查iptables规则变化
echo "8. 检查iptables规则变化:"
echo "   当前iptables规则中包含测试MAC的规则:"
iptables -L -n | grep -i "$TEST_MAC" | sed 's/^/     /'
echo ""

echo "=== 测试完成 ==="
echo ""
echo "说明："
echo "1. Ping接口应该返回 'Pong'"
echo "2. 登录接口应该返回302重定向到OneLink认证页面"
echo "3. 认证接口在有token时应该返回 'Auth: 1'"
echo "4. Portal和消息接口应该返回HTML页面"
echo "5. 登出接口应该返回 'Auth: 0' 并阻止MAC访问"
echo ""
echo "如果goahead没有运行，请先启动goahead服务器"
