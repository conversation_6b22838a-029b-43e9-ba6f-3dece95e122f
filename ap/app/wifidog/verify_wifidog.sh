#!/bin/bash

# WiFiDog Functionality Verification Script
# This script helps verify that WiFiDog is working correctly

set -e

# Check if we're in development environment
if [ -f "wifidog-gateway-1.3.0/src/wifidog" ]; then
    WIFIDOG_BIN="./wifidog-gateway-1.3.0/src/wifidog"
    WDCTL_BIN="./wifidog-gateway-1.3.0/src/wdctl"
else
    WIFIDOG_BIN="/bin/wifidog"
    WDCTL_BIN="/bin/wdctl"
fi

CONFIG_FILE="/etc/wifidog.conf"
TEST_CONFIG="./wifidog_test.conf"
LOG_FILE="/tmp/wifidog_test.log"

echo "=== WiFiDog Functionality Verification ==="
echo "Date: $(date)"
echo ""

# Function to check if command exists
check_command() {
    if command -v "$1" >/dev/null 2>&1; then
        echo "✓ $1 is available"
        return 0
    else
        echo "✗ $1 is not available"
        return 1
    fi
}

# Function to check network interface
check_interface() {
    local interface="$1"
    if ip link show "$interface" >/dev/null 2>&1; then
        echo "✓ Interface $interface exists"
        ip addr show "$interface" | grep -E "inet|state"
        return 0
    else
        echo "✗ Interface $interface does not exist"
        return 1
    fi
}

# Function to check port availability
check_port() {
    local port="$1"
    if netstat -ln | grep -q ":$port "; then
        echo "⚠ Port $port is already in use"
        netstat -ln | grep ":$port "
        return 1
    else
        echo "✓ Port $port is available"
        return 0
    fi
}

# 1. Check binaries
echo "1. Checking WiFiDog binaries..."
if [ -f "$WIFIDOG_BIN" ]; then
    echo "✓ wifidog binary exists: $WIFIDOG_BIN"
    file "$WIFIDOG_BIN"
    ls -la "$WIFIDOG_BIN"
else
    echo "✗ wifidog binary not found: $WIFIDOG_BIN"
    exit 1
fi

if [ -f "$WDCTL_BIN" ]; then
    echo "✓ wdctl binary exists: $WDCTL_BIN"
    file "$WDCTL_BIN"
    ls -la "$WDCTL_BIN"
else
    echo "✗ wdctl binary not found: $WDCTL_BIN"
    exit 1
fi

echo ""

# 2. Check dependencies
echo "2. Checking system dependencies..."
check_command "iptables"
check_command "ip"
check_command "netstat"

echo ""

# 3. Check network interfaces
echo "3. Checking network interfaces..."
echo "Available interfaces:"
ip link show | grep -E "^[0-9]+:" | cut -d: -f2 | sed 's/^ *//'

echo ""
echo "Checking common interface names:"
for iface in wlan0 br0 br-lan eth0 eth1; do
    check_interface "$iface" || true
done

echo ""

# 4. Check configuration
echo "4. Testing configuration parsing..."
if [ -f "$TEST_CONFIG" ]; then
    echo "✓ Test configuration file exists: $TEST_CONFIG"
    
    # Test configuration syntax
    echo "Testing configuration syntax..."
    if "$WIFIDOG_BIN" -c "$TEST_CONFIG" -t 2>&1; then
        echo "✓ Configuration syntax is valid"
    else
        echo "✗ Configuration syntax error"
        echo "Trying to parse configuration manually..."
        "$WIFIDOG_BIN" -c "$TEST_CONFIG" -t
    fi
else
    echo "✗ Test configuration file not found: $TEST_CONFIG"
fi

echo ""

# 5. Check ports
echo "5. Checking port availability..."
check_port "2060"  # Default WiFiDog port
check_port "80"    # HTTP port
check_port "53"    # DNS port

echo ""

# 6. Check iptables
echo "6. Checking iptables functionality..."
if iptables -L >/dev/null 2>&1; then
    echo "✓ iptables is working"
    echo "Current iptables rules count:"
    iptables -L | grep -c "Chain"
else
    echo "✗ iptables is not working or not accessible"
fi

echo ""

# 7. Test basic functionality (if running as root)
if [ "$EUID" -eq 0 ]; then
    echo "7. Testing basic WiFiDog functionality..."
    
    # Create a minimal test config
    cat > /tmp/wifidog_minimal.conf << EOF
GatewayInterface lo
AuthServer {
    Hostname 127.0.0.1
    HTTPPort 8080
    Path /
}
PopularServers www.google.com
FirewallRuleSet global {
    FirewallRule allow to 0.0.0.0/0
}
FirewallRuleSet validating-users {
    FirewallRule allow to 0.0.0.0/0
}
FirewallRuleSet known-users {
    FirewallRule allow to 0.0.0.0/0
}
FirewallRuleSet unknown-users {
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
}
FirewallRuleSet locked-users {
    FirewallRule block to 0.0.0.0/0
}
EOF

    echo "Testing WiFiDog startup (dry run)..."
    if "$WIFIDOG_BIN" -c /tmp/wifidog_minimal.conf -f -d 7 &
    then
        WIFIDOG_PID=$!
        sleep 2
        
        if kill -0 "$WIFIDOG_PID" 2>/dev/null; then
            echo "✓ WiFiDog started successfully"
            
            # Test wdctl
            sleep 1
            if "$WDCTL_BIN" status; then
                echo "✓ wdctl communication working"
            else
                echo "⚠ wdctl communication failed"
            fi
            
            # Stop WiFiDog
            kill "$WIFIDOG_PID" 2>/dev/null || true
            wait "$WIFIDOG_PID" 2>/dev/null || true
            echo "✓ WiFiDog stopped"
        else
            echo "✗ WiFiDog failed to start"
        fi
    else
        echo "✗ Failed to start WiFiDog"
    fi
    
    rm -f /tmp/wifidog_minimal.conf
else
    echo "7. Skipping functionality test (not running as root)"
    echo "   Run as root to test actual WiFiDog functionality"
fi

echo ""

# 8. Generate recommendations
echo "8. Recommendations for deployment:"
echo ""

echo "Network Interface Configuration:"
echo "- Check available interfaces with: ip link show"
echo "- For WiFi AP mode, typically use: wlan0 or br0"
echo "- For bridge mode, typically use: br-lan or br0"
echo ""

echo "Authentication Server Setup:"
echo "- Set up WiFiDog-auth server or use existing captive portal"
echo "- Ensure auth server is reachable from gateway"
echo "- Test auth server endpoints manually"
echo ""

echo "Firewall Configuration:"
echo "- Ensure iptables is properly configured"
echo "- Check that required kernel modules are loaded"
echo "- Verify that WiFiDog can modify iptables rules"
echo ""

echo "Testing Steps:"
echo "1. Configure correct network interface in wifidog.conf"
echo "2. Set up authentication server"
echo "3. Start WiFiDog: wifidog -c /path/to/config -f (foreground mode)"
echo "4. Connect a client device to the WiFi network"
echo "5. Try to access a website - should be redirected to captive portal"
echo "6. Check status with: wdctl status"
echo ""

echo "=== Verification Complete ==="
