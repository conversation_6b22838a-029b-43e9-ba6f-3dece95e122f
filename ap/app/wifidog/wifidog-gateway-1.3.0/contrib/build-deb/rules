#!/usr/bin/make -f

# Uncomment this to turn on verbose mode.
#export DH_VERBOSE=1

build: build-stamp
build-stamp:
	dh_testdir

	./configure --prefix=/usr
	$(MAKE)

	touch build-stamp

clean:
	dh_testdir
	dh_testroot
	rm -f build-stamp

	-$(MAKE) clean
	-$(MAKE) distclean

	dh_clean

install: build
	dh_testdir
	dh_testroot
	dh_clean -k
	dh_installdirs

	$(MAKE) DESTDIR=$(CURDIR)/debian/tmp install
	mkdir -p $(CURDIR)/debian/tmp/etc
	cp wifidog.conf $(CURDIR)/debian/tmp/etc
	cp scripts/init.d/wifidog debian/wifidog.init

# Build architecture-independent files here.
binary-indep: build install
# We have nothing to do by default.

# Build architecture-dependent files here.
binary-arch: build install
	dh_testdir
	dh_testroot
	dh_installchangelogs
	dh_installdocs
#	dh_installexamples
#	dh_install
#	dh_installmenu
#	dh_installdebconf	
#	dh_installlogrotate
#	dh_installemacsen
#	dh_installcatalogs
#	dh_installpam
#	dh_installmime
	dh_installinit
#	dh_installcron
#	dh_installinfo
#	dh_undocumented
	dh_installman
	dh_link
	dh_strip
	dh_compress
	dh_fixperms
#	dh_perl
#	dh_python
	dh_makeshlibs
	dh_installdeb
#	dh_shlibdeps
	dh_gencontrol
	dh_md5sums
	dh_builddeb

binary: binary-indep binary-arch
.PHONY: build clean binary-indep binary-arch binary install
