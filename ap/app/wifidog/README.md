# WiFiDog Gateway Integration

This directory contains the WiFiDog captive portal gateway integrated into the ZTE MiFi build system.

## Overview

WiFiDog is an open-source captive portal solution that provides authentication and access control for wireless networks. This integration allows the ZTE MiFi device to act as a captive portal gateway.

## Components

- **wifidog**: The main captive portal gateway daemon
- **wdctl**: WiFiDog control utility for managing the gateway
- **wifidog.conf**: Configuration file (located in source directory)

## Build Integration

### Source Structure
```
wifidog/
├── Makefile                    # Build integration with ZTE build system
├── wifidog-gateway-1.3.0/     # WiFiDog source code
├── test_wifidog.sh            # Test script
└── README.md                  # This file
```

### Build Process

The build system automatically:
1. Runs `autogen.sh` to generate configure script
2. Cross-compiles for ARM architecture using buildroot toolchain
3. Links against uClibc
4. Installs binaries to rootfs

### Build Commands

```bash
# Build only wifidog
make -C ap/app/wifidog all

# Build entire application suite (includes wifidog)
cd ap/project/zx297520v3/prj_mifi_mz804_my/build
make app RF_TYPE=230A DCXO=yes

# Clean wifidog build
make -C ap/app/wifidog clean

# Complete clean (removes configure script)
make -C ap/app/wifidog distclean
```

## Configuration

WiFiDog requires configuration before use. Key configuration parameters include:

- **GatewayInterface**: Network interface to monitor (e.g., wlan0)
- **AuthServer**: Authentication server details
- **HTTPDPort**: Port for the built-in HTTP server
- **CheckInterval**: Client check interval

Example configuration can be found in `wifidog-gateway-1.3.0/wifidog.conf`.

## Installation

The build system automatically installs:
- `/bin/wifidog` - Main gateway daemon
- `/bin/wdctl` - Control utility

## Usage

### Starting WiFiDog
```bash
# Start with default config
wifidog

# Start with custom config
wifidog -c /path/to/wifidog.conf

# Start in foreground (for debugging)
wifidog -f
```

### Control Commands
```bash
# Check status
wdctl status

# Stop the gateway
wdctl stop

# Restart the gateway
wdctl restart
```

## Technical Details

### Cross-Compilation
- **Target**: ARM EABI5 (armv7-a with Thumb)
- **Toolchain**: arm-buildroot-linux-uclibcgnueabi-gcc 4.9.4
- **C Library**: uClibc
- **Linking**: Static libraries preferred, dynamic linking for system libs

### Dependencies
- pthread (for threading)
- libnsl (for network services)
- iptables (for firewall rules - must be available on target)

### Build Flags
- Optimized for size (-Os)
- Thumb instruction set for reduced code size
- Function sections for better dead code elimination
- Soft float for compatibility

## Testing

Run the test script to verify successful compilation and installation:

```bash
cd ap/app/wifidog
./test_wifidog.sh
```

## Troubleshooting

### Common Issues

1. **Configure fails**: Ensure autotools are installed and autogen.sh runs successfully
2. **Cross-compilation errors**: Verify toolchain paths and environment variables
3. **Missing dependencies**: Check that pthread and libnsl are available in staging area

### Debug Build

For debugging, modify Makefile to remove optimization and add debug symbols:
```makefile
CFLAGS="-g -O0 ..."  # Change -Os to -O0
```

## Integration Notes

This integration was designed to work with the ZTE MiFi build system and includes:
- Proper cross-compilation setup
- Integration with romfs installation
- Compatibility with existing build infrastructure
- ARM optimization for embedded deployment

## Version Information

- **WiFiDog Version**: 1.3.0
- **Integration Date**: September 2024
- **Target Platform**: ZTE MiFi MZ804 (ARM-based)
- **Build System**: ZTE proprietary build system with buildroot toolchain
