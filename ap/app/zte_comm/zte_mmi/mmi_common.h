/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_led.h
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��MMIͷ�ļ�
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <fcntl.h>
#include <errno.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include <pthread.h>
#include <semaphore.h>
#include <time.h>
#include <sys/prctl.h>
#include "softap_api.h"
#include "soft_timer.h"
#include "mmi_cfg.h"

#ifndef DISABLE_LCD
#include "os_type.h"
#include "gui_common.h"
#endif

#include "cfg_api.h"
#include "cfg_nv_def.h"

#ifndef __MMI_COMMON_H
#define __MMI_COMMON_H
#define MMI_SOFTTIMER_DEBUG     1
/*******************************************************************************
  ��ģ�鶨���NV ͳһ����
*******************************************************************************/
#define MMI_BAT_VOLTAGE_LEN				21

/*******************************************************************************
  MMI �����ַ���ͳһ����
*******************************************************************************/
#define INVALID_STR ""

/*******************************************************************************
  MMI ����ֵ����
*******************************************************************************/
#define MMI_SUCCESS						0x0
#define MMI_ERROR						-1

#define MMI_WAIT_FOREVER 				0XFFFFFFFF
#define MMI_THREAD_INVALID_ID  			0XFFFFFFFF
#define MMI_SEMA_INVALID_ID  			0xFFFFFFFF
#define MMI_MUTEX_INVALID_ID            0xFFFFFFFF


/*******************************************************************************
  MMI LOG���ء��洢·��������
*******************************************************************************/
#define MMI_FILE_DEBUG 				0
#define MMI_SERIAL_DEBUG 			1
#define MMI_DEBUG 					1

#define MMI_LOG_FILE_PATH         "/etc_rw/config/mmi.log"
#define MMI_LOG_OLD_FILE_PATH     "/etc_rw/config/oldmmi.log"

#define MMI_MAX_PRINT_LEN	      256
#define MMI_MAX_LOG_LENGTH 	      524288

/*******************************************************************************
  MMI ʹ���豸����
*******************************************************************************/

#define  KPD_DEV    					"kpd"
#define  RTC_DEV    					"rtc"
#define  LED_DEV    			  		"led"
#define  CHG_DEV                        "charger"

#define LCD_PATH 						"/dev/fb0"
#define LCD_MAGIC 						 'L'
#define LEADT15DS26_SET_BACKLIGHT		_IOW(LCD_MAGIC, 2, unsigned int)


/*******************************************************************************
 MMI ��ʱ������
*******************************************************************************/
#define SET_BACKLIGHT_HALFBRIGHT_TIMER	80
#define SET_TIME_CHECK_TIMER			81
#define SET_WIFI_STANUM_CHECK_TIMER		82
#define SET_WPS_TIP_CHECK_TIMER         83
#define SET_TIP_CHECK_TIMER				84
#define SET_MMI_IDLE_TIMER				85
#define SET_SSID_SHOW_TIMER				86
#define SET_SMS_TIP_TIMER				87
#define SET_TRAFFIC_INFO_WAIT_TIMER     88
#define SET_MMI_FAST_POWERON_IDLE_TIMER	89
#define SET_WIFI_DATA_CHECK_TIMER	    90
#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
#define SET_DIGITS_SLEEP_TIMER			91
#endif


#define SET_TIME_CHECK_TIME         	1000
#define SET_WIFI_STANUM_CHECK_TIME      3000

#ifdef WIFI_UNCOEXIST_5G
#define SET_WPS_TIP_CHECK_TIME         	4000
#else
#define SET_WPS_TIP_CHECK_TIME         	2000
#endif

#define SET_TIP_CHECK_TIME         		1000
#define SET_MMI_IDLE_TIME				20000
#define SET_BACKLIGHT_HALFBRIGHT_TIME	5000
#if defined(QRZL_UE) && defined(JCV_HW_MZ801_V1_2)
#define SET_SSID_SHOW_TIME				2000
#else
#define SET_SSID_SHOW_TIME				20000
#endif
#define SET_SMS_TIP_TIME				10000
#define SET_TRAFFIC_INFO_WAIT_TIME      10000
#define SET_MMI_FAST_POWERON_IDLE_TIME	4000
#define SET_WIFI_DATA_CHECK_TIME	    3000

/* 数码管休眠唤醒功能控制宏 */
#ifdef JCV_CHARLIEPLEX_LED_DIGITS
#define SET_DIGITS_SLEEP_TIME			20000  /* 20秒 = 20000ms */
#endif

/*******************************************************************************
MMI ���ҵ������
*******************************************************************************/
#define CHARGE_STATUS_PATH			  	"/sys/class/power_supply/charger/status"
#define CHARGING_TYPE_PATH				"/sys/class/power_supply/charger/pc1_ac2"
#define USB_INSERT_STATUS_PATH			"/sys/class/power_supply/charger/online"
#define CHARGE_VOLTAGE_PATH				"/sys/class/power_supply/battery/voltage_now"
#define CHARGE_BAT_TEMP_PATH 			"/sys/class/power_supply/battery/temp"
#define CHARGE_BAT_HEALTH_PATH 			"/sys/class/power_supply/battery/health"

#define MMI_SET_FAKEPOWEROFF_CHARGER    "echo 1 > /sys/class/power_supply/charger/quick_power_on"
#define MMI_CAN_FAKEPOWEROFF_CHARGER    "echo 0 > /sys/class/power_supply/charger/quick_power_on"

#define MMI_TURN_OFF_CHG_FULL  		    "echo 2 > /sys/class/power_supply/charger/charge_enabled"
#define MMI_TURN_OFF_CHG  		        "echo 0 > /sys/class/power_supply/charger/charge_enabled"
#define MMI_TURN_ON_CHG	  		        "echo 1 > /sys/class/power_supply/charger/charge_enabled"

#define MMI_TURN_OFF_DISCHG  	        "echo 0 > /sys/class/power_supply/boost/boost_enabled"
#define MMI_TURN_ON_DISCHG	  	        "echo 1 > /sys/class/power_supply/boost/boost_enabled"

#define MMI_DISCHG_LOW_CURRENT  	    "echo 5 > /sys/class/power_supply/boost/current_now"
#define MMI_DISCHG_HIGH_CURRENT	        "echo 15 > /sys/class/power_supply/boost/current_now"

#define CHARGE_STATUS_CHARGING		 	"Charging"
#define CHARGE_STATUS_FULL		 	 	"Full"
#define CHARGE_STATUS_NOTCHARGING		"Not charging"
#define CHARGE_STATUS_DISCHARGING		"Discharging"

#define CHARGING_TYPE_PC				"USB"
#define CHARGING_TYPE_ADAPTER			"Mains"
#define CHARGING_TYPE_UNKNOWN			"Unknown"

#define TEMP_STATUS_GOOD				"Good"
#define TEMP_STATUS_OVERHEAT			"Overheat"
#define TEMP_STATUS_DEAD				"Dead"
#define TEMP_STATUS_OVER_VOLTAGE		"Over voltage"
#define TEMP_STATUS_FAIL				"Unspecified failure"
#define TEMP_STATUS_COLD				"Cold"
#define TEMP_STATUS_WARM				"Warm"
#define TEMP_STATUS_COOL				"Cool"
#define TEMP_STATUS_UNKNOWN			    "Unknown"

#define USB_STATUS_IN					"1"
#define USB_STATUS_OUT					"0"

#define CHARGE_STATUS_LENGTH				16
#define CHARGING_TYPE_LENGTH				8
#define CHARGE_VOLTAGE_LENGTH				8

#define CHARGE_STATUS_CHARGING_LENGTH		8
#define CHARGE_STATUS_FULL_LENGTH		 	4
#define CHARGE_STATUS_NOTCHARGING_LENGTH	12
#define CHARGE_STATUS_DISCHARGING_LENGTH	11

/*******************************************************************************
 MMI ������
*******************************************************************************/


typedef enum {
	MMI_TASK_CTRL = 0,//��������ʱ��һλ��ȷ�����������Ȼ���
	MMI_TASK_BATTERY,
	MMI_TASK_NET,
	MMI_TASK_WIFI,
	MMI_TASK_SMS,
	MMI_TASK_VOIP,
	MMI_TASK_TRAFFIC,
	MMI_TASK_POWEROFF_CHARGER,
	MMI_TASK_TIP_NET_CONNECT,
	MMI_TASK_TIP_WIFISTATION,
	MMI_TASK_TIP_WPS,
	MMI_TASK_TIP,
	MMI_TASK_SSID,
	MMI_TASK_TIP_FOTA,
	MMI_TASK_POWER,
	MMI_TASK_WIFICODE,
	MMI_TASK_NETSIGNAL, //yaoyuan cpe
	MMI_TASK_LED_WPS, //yaoyuan cpe MMI_TASK_TIP_WPS
	MMI_TASK_RJ11, //yaoyuan cpe
	MMI_TASK_TIME,
	MMI_TASK_KEY,
	MMI_TASK_USB_MODE,
	MMI_TASK_MAX
} E_zMmi_Task;

#define MMI_TASK_INVALID  -1
//���庯��ָ������
typedef SINT32(*MMI_TASK_REGISTER_FUNC)(void);
typedef void (*MMI_TASK_INIT_FUNC)(void);

/*******************************************************************************
 MMI ��ʾģʽ����
*******************************************************************************/
typedef enum {
	MMI_MODE_LED = 1,
	MMI_MODE_LCD,
	MMI_MODE_ALL
} E_zMmiShowMode;


/*******************************************************************************
 MMI ��Ϣ����
*******************************************************************************/

#define NV_CONTENT_LEN 						1024


/*******************************************************************************
  LED��ʡ����ض���
*******************************************************************************/
typedef enum {
	//public
	MMI_ACTIVE_MODE = 0,
	MMI_IDLE_LEDOFF_MODE,
	MMI_IDLE_CHG_LEDOFF_MODE,
	MMI_IDLE_STANDBY_LEDOFF_MODE,
	MMI_FAKE_POWEROFF_MODE,
	MMI_FAKE_POWEROFF_CHARGE_MODE,

	//LED
	MMI_ALL_LED_ON_MODE,
	MMI_ALL_LED_OFF_MODE,
	MMI_POWEROFF_ON_MODE,
	MMI_POWEROFF_OFF_MODE,


	//LCD
	MMI_BACKLIGHT_OFF_MODE,
	MMI_BACKLIGHT_HALFBRIGHT_MODE,
	MMI_POWERON_MODE,
	MMI_POWEROFF_MODE,
	MMI_RESET_MODE,
	MMI_RESTART_MODE,
	MMI_FAST_POWERON_MODE,
} E_zMmi_Work_Mode;

/*
������Ϣ�ṹ
*/
typedef struct {
	E_zMmi_Work_Mode lcdmode;
	E_zMmi_Work_Mode ledmode;
} T_zMMICtrlInfo;

#define MMI_MAIN_LOCK_ID				"mmi_main_lock"
#define MMI_POWEROFF_LOCK_ID			"mmi_powerdown_lock"//�ػ����ָ��������á��������ػ���翪��
#define MMI_GET_POWER_LOCK_ID			"mmi_get_power_lock"//20S�¶ȼ��/�͵���
#define MMI_POWEROFF_LOCK_LCD_ID		"mmi_poweroff_charger_lock"//��ʼ��������--  --����


/*******************************************************************************
  LCD��ʾ���� �������ӡ�SIM��״̬��
*******************************************************************************/
typedef enum {
	INSERT_SIM = 0,
	PIN_LOCK,
	PUK_LOCK,
	SIM_BUSY,
	INVALID_SIM,
	SIM_LOCK,
	SIM_READY,

	SIM_MAX
}
E_zMmi_Sim_Tip;

typedef enum {
	NET_DISCONNECTED = 0,
	NET_CONNECTED,
	NET_CONNECTING,
	NET_DISCONNECTING,
	NET_NOSERVICE,
	NET_LIMITSERVICE,
	NET_SEARCHING,
	NET_MAX
} E_zMmi_NetCon_Tip;

typedef enum {
	WPS_ACTIVING = 0,
	WPS_ACTIVED,
	WPS_DEACTIVING,
	WPS_DEACTIVED,
	WPS_FAIL,

	WPS_KEY_BAND,
	WPS_ACTIVE_MAX
} E_zMmi_WpsAct_Tip;


typedef struct {
	E_zMmi_Sim_Tip sim_tip;
	E_zMmi_NetCon_Tip net_tip;
	CHAR* net_pro;
	//CHAR* update_result;//zk add for fota update result
	//SINT32 update_tip;
} T_zMMITipInfo;

typedef enum {
	FOTA_DOWNLOADING = 0,
	FOTA_DOWNLOAD_FAILED,
	FOTA_DOWNLOAD_OK,
	FOTA_DOWNLOAD_LOWBATTERY,
	FOTA_UPDATE_SUCCESS,
	FOTA_UPDATE_FAILED,
	FOTA_SHOW_FINISH,
	FOTA_MAX
} E_zMMI_Fota_Tip;

typedef enum {
	FOTA_UPDATE = 0,
	FOTA_CANCEL
} E_zMmi_Fota_Oper;

typedef struct {
	SINT32 fota_update;
	E_zMMI_Fota_Tip fota_tip;
	E_zMmi_Fota_Oper fota_oper;
} T_zMMIFotaInfo;

typedef struct {
	CHAR *ssid;
	CHAR *wifi_key;
	CHAR *ssid2;
	CHAR *wifi_key2;
} T_zMMISSIDInfo;

typedef struct {
	CHAR *msisdn;
	CHAR *pci;
	CHAR *rapr_dBm;
	CHAR *sinr_dB;
} T_zMMINetSignalInfo;

/*
ʱ����Ϣ�ṹ
*/
typedef struct {
	BOOL mode;//ʱ����ʽ true--24Сʱ�� false--12Сʱ��
	CHAR* hour;
	CHAR* minute;
	//CHAR* quota;
} T_zMMITimeInfo;

/*******************************************************************************
�����Ϣ����
*******************************************************************************/
//webui���״̬NV
#define WEBUICHARGESTATTUS      		"battery_charging"        					///1:���״̬ 0:�ǳ��״̬
#define WEBUICHARGING           		"1"
#define WEBUIUNCHARGING        			"0"

//webui�ĵ�ظ���NV
#define WEBUIBATTERYLEVEL       		"battery_pers"
#define WEBUINOLEVEL            		"0"
#define WEBUIONELEVEL           		"1"
#define WEBUITWOLEVEL           		"2"
#define WEBUITHRLEVEL           		"3"
#define WEBUIFOURLEVEL         	 		"4"
#define WEBUIFULLLEVEL         	 		"100"

//wifi Monitor��ذٷֱ�
#define BATTERYPERS_NV					"battery_vol_percent"

typedef enum {
	STATE_FULL = 0,
	STATE_CHARGING,
	STATE_DISCHARGE,
	STATE_CHARGERROR,
	STATE_CHARGE_MAX
} E_zMmi_Charge_State;

typedef enum {
	CHARGING_PC = 0,
	CHARGING_ADAPTER,
	CHARGING_TYPE_MAX,
} E_zMmi_Charge_Type;

typedef enum {
	VOLT_5PERCENTLEVEL = 0,
	VOLT_10PERCENTLEVEL,
	VOLT_20PERCENTLEVEL,
	VOLT_25PERCENTLEVEL,
	VOLT_50PERCENTLEVEL,
	VOLT_75PERCENTLEVEL,
	VOLT_NORMALLEVEL,
#if defined(QRZL_UE) && defined(JCV_HW_MZ901_V1_0)
	VOLT_FULLLEVEL,
#endif
	VOLT_MAX,
} E_zMmi_Voltage_level;

typedef struct {
	CHAR* devString;
	E_zMmi_Charge_State chg_sta;
} T_zMmiChgStateStringItem;


typedef enum {
	MODE_STANDBY = 0,
	MODE_CHARGING,
	MODE_LOWBATTERY_20,
	MODE_LOWBATTERY_10,
	MODE_OFF
} E_zMMI_BatLed_Mode;

/*�����Ϣ�ṹ*/
typedef struct {
	E_zMmi_Charge_State chg_state;
	E_zMmi_Voltage_level bat_level;
	SINT32 bat_pers;
	SINT32 bat_grid;
} T_zMMIBatteryInfo;


//�¶ȼ��
typedef enum {
	MMI_TEMP_DETECT_ERROR = 0,
	MMI_TEMP_DETECT_ABNORMAL,
	MMI_TEMP_DETECT_NORMAL,
	MMI_TEMP_DETECT_MAX
} MMI_TEMP_DETECT;

//�ػ����
typedef enum {
	POC_STATE_CHARGING = 0,
	POC_STATE_FULL,
	POC_STATE_LOWBATTERY,
	POC_STATE_NOBATTERY,
	POC_STATE_TEMP_ERROR,
	POC_STATE_MAX
} E_zMmi_Poc_State;

typedef enum {
	POC_CHARGING_PC = 0,
	POC_CHARGING_ADAPTER,
	POC_CHARGING_TYPE_MAX
} E_zMmi_Poc_Type;

typedef struct {
	SINT32 backlight_sta;
	E_zMmi_Poc_State poc_sta;
	BOOL overvoltage_mode;
} T_zMmi_Poc_Info;

/*******************************************************************************
  ������Ϣ����
*******************************************************************************/
typedef enum {
	NET_MODE_DEFAULT = 0,
	NET_MODE_2G,
	NET_MODE_3G,
	NET_MODE_4G,
	NET_MODE_NOSERVICE,
	NET_MODE_LIMITSERVICE,
	NET_MODE_NOTREADY //yaoyuan
} E_zMmi_Net_Mode;

typedef enum {
	NET_STATE_DISCONNECTED = 0,
	NET_STATE_CONNECTED,
	NET_STATE_CONNECTING,
	NET_STATE_DISCONNECTING,
	NET_STATE_NOSERVICE,
	NET_STATE_LIMITSERVICE,
	NET_STATE_SEARCHING,
#if defined(JCV_HW_MZ803_V3_2) || defined(JCV_HW_UZ901_V1_4)
	NET_STATE_WITHOUT_BALANCE,
#endif
#if defined(QRZL_CUSTOM_TIANMU_LOGIC)
	NET_STATE_LIMIT_SPEED,
#endif 
	NET_STATE_MAX
} E_zMmi_Net_State;

typedef enum {
	NET_SIGNAL_DEFAULT = 0,
	NET_SIGNAL_NORMAL,
	NET_SIGNAL_WEAK
} E_zMmi_Net_Signal;

typedef enum {
	NET_SOCKET_INACTIVE = 0,
	NET_SOCKET_ACTIVE
} E_zMmi_Net_Socket;


/*������Ϣ�ṹ*/

typedef struct {
	E_zMmi_Net_Mode net_mode;
	E_zMmi_Net_State connect_status;
	SINT32 signal_num;
	SINT32 signal_weak;
	SINT32 roam_mode;
	E_zMmi_Net_Socket socket_state;
} T_zMMINetInfo;


#define WD_NUM 						    5    //wifi ����ļ�����
#define WIFI_TURN_ON 					"1"  //wifi operate type
#define WIFI_TURN_OFF 					"0"  //wifi operate type
#define WIFI_TURN_OFF_FAKE_POWEROFF		"2"  //wifi operate type


struct st_files {
	char *filename;
	int wd;
};

typedef struct {
	BOOL wifiState;
	BOOL wpsState;
	BOOL mIsConnectUser;
} T_zMmi_LanLed_Info;

typedef enum {
	WIFI_OFF = 0,
	WIFI_ON
} E_zMmi_Wifi_State;

typedef enum {
	WPS_OFF = 0,
	WPS_ON
} E_zMmi_Wps_State;

typedef enum {
	NO_USER_CONNECTED = 0,
	USER_CONNECTED
} E_zMmi_Wifi_ConState;
typedef enum {
	WIFISTATION_OFF = 0,
	WIFISTATION_ON
} E_zMmi_WifiStation_State;
typedef enum {
	WIFICODE_MAIN = 0,
	WIFICODE_GUST1
} E_zMmi_WifiCode_State;


/*
wifi��Ϣ�ṹ
*/
typedef struct {
	BOOL wifi_state;
	BOOL wifidata_state;
	SINT32 Isconnected_user;
	SINT32 connected_userNum;
	E_zMmi_WpsAct_Tip wps_state;
} T_zMMIWifiInfo;
/*
wifistation��Ϣ�ṹ
*/
typedef struct {
	BOOL wifistation_connect_state;
	SINT32 signal_num;
	CHAR *wifista_ssid;
} T_zMMIWifiStationInfo;
/*
wificode��Ϣ�ṹ
*/
typedef struct {
	BOOL multi_ssid_switch;
	BOOL is_wificode_exist;
} T_zMMIWifiCodeInfo;

/*******************************************************************************
 ������Ϣ����
*******************************************************************************/
typedef enum {
	TRAFFIC_UNIT_DATA = 0,
	TRAFFIC_UNIT_TIME,

	TRAFFIC_UNIT_MAX
} MMI_TRAFFIC_UNIT;

typedef enum {
	TRAFFIC_LIMIT_SWITCH_OFF = 0,
	TRAFFIC_LIMIT_SWITCH_ON,
} MMI_TRAFFIC_SWITCH;

typedef enum {
	TRAFFIC_DATA_UNIT_MAX = 0,
	TRAFFIC_DATA_UNIT_MB,
	TRAFFIC_DATA_UNIT_GB,
	TRAFFIC_DATA_UNIT_TB
} E_ZMmi_Traffic_Data_Unit;

typedef enum {
	TRAFFIC_LED_1 = 0,
	TRAFFIC_LED_2,
	TRAFFIC_LED_3,
	TRAFFIC_LED_4,
	TRAFFIC_LED_5,
	TRAFFIC_LED_6,
	TRAFFIC_LED_7,
	TRAFFIC_LED_8,
	TRAFFIC_LED_9,
	TRAFFIC_LED_10,
	TRAFFIC_LED_MAX
} MMI_TRAFFIC_LED;

/*
traffic��Ϣ�ṹ
*/
typedef struct {
	MMI_TRAFFIC_SWITCH traffic_switch;
	MMI_TRAFFIC_UNIT traffic_unit;
	E_ZMmi_Traffic_Data_Unit data_unit;
	E_ZMmi_Traffic_Data_Unit useddata_unit;
	MMI_TRAFFIC_LED led_status;
	SINT32 warning_tip_flag ;
	UINT32 warning_tip_level;
	float total_traffic;
	float uesd_traffic;
} T_zMMITrafficInfo;
/*
�ƶ���Ӫ��traffic��Ϣ�ṹ
*/
typedef struct {
	BOOL main_used_valid;//˵��main used�����Ƿ���Ч
	SINT32 main_left_traffic;
	SINT32 main_total_traffic;
	//BOOL bonus_used_valid;//˵��bonus used�����Ƿ���Ч
	//FLOAT bonus_uesd_traffic;
	//FLOAT bonus_total_traffic;
} T_zMMIQuotaTrafficInfo;


/*******************************************************************************
 ������Ϣ��ض���
*******************************************************************************/
typedef enum {
	SMS_RECVBOX_STATUS_UNREAD = 0,
	SMS_RECVBOX_STATUS_NEW,
	SMS_RECVBOX_STATUS_FULL,
	SMS_RECVBOX_STATUS_NOR
} E_zMmi_Sms_Recvbox_Status;

typedef struct {
	SINT32 mSmsNum;
	E_zMmi_Sms_Recvbox_Status recvBox_sta;
} T_zMmi_Sms_Info;

/*******************************************************************************
 ������ʾ��Ϣ��ض���
*******************************************************************************/
typedef enum {
	VOIP_STATUS_IN_CALL = 0,
	VOIP_STATUS_IN_CONNECTION,
	VOIP_STATUS_HANG_UP,
	VOIP_STATUS_NOR
} E_zMmi_Voip_Status;

typedef struct {
	E_zMmi_Voip_Status voip_sta;
} T_zMmi_Voip_Info;

/*******************************************************************************
 rj11�����Ϣ��ض���
*******************************************************************************/
typedef enum {
	RJ11_STATUS_OUT = 0,
	RJ11_STATUS_IN,
	RJ11_STATUS_NOR
} E_zMmi_Rj11_Status;

typedef struct {
	E_zMmi_Rj11_Status rj11_sta;
} T_zMmi_Rj11_Info;

/*******************************************************************************
  LED�ƹ�������
*******************************************************************************/

typedef enum {
	LED_STATE_ON = 0,
	LED_STATE_OFF,
	LED_STATE_BLINK,
	LED_STATE_MAX
} MMI_LED_STATE;

typedef enum {
	LED_STATE_DEFAULT_BLINK,
	LED_STATE_FAST_BLINK,
	LED_STATE_SLOW_BLINK,
	LED_STATE_BAT_BLINK,
	LED_STATE_SMS_BLINK,
	LED_STATE_VOIP_BLINK,
	LED_STATE_LAN_BLINK,
	LED_STATE_WAN_BLINK,
	LED_STATE_WAN_FAST_BLINK,
	LED_STATE_WAN_SLOW_BLINK,
	LED_STATE_WAN_CPE_FAST_BLINK,
	LED_STATE_WAN_CPE_SLOW_BLINK
} MMI_LED_BLINK_SPEED;


typedef enum {
	LED_WAN = 0,
	LED_LAN,
	LED_BATTERY,
	LED_SMS,
	LED_VOIP,
	LED_TRAFFIC,
	LED_SIGNAL,
	LED_WPS,
	LED_RJ11,
	LED_ALL
} MMI_LED_NAME;

typedef enum {
	LED_COLOR_RED = 0,
	LED_COLOR_GREEN,
	LED_COLOR_YELLOW,
	LED_COLOR_BLUE,

	MAX_LED_COLOR //kw 3
} MMI_LED_COLOR;

typedef struct {
	UINT32 uBlinkOnTime;
	UINT32 uBlinkOffTime;
} T_zMmi_Led_Blink_Time;

typedef struct {
	MMI_LED_NAME led_name;
	MMI_LED_STATE led_state;
	MMI_LED_BLINK_SPEED ledBlink_speed;
	T_zMmi_Led_Blink_Time ledBlink_time;
	MMI_LED_COLOR led_color;
	MMI_TRAFFIC_LED traffic;
} T_zMmi_Led_Info;

typedef struct {
	char *fileblinkSwitch;
	char *fileblinktimeon;
	char *fileblinktimeoff;
	char *timeon;
	char *timoff;
} T_zMmi_LedBlink_Info;

typedef enum {
	LED_RED_ON,
	LED_RED_BLINK,

	LED_GREEN_ON,
	LED_GREEN_BLINK,

	LED_YELLOW_ON,
	LED_YELLOW_BLINK,

	LED_BLUE_ON,
	LED_BLUE_BLINK,

	LED_BLUE1_ON,
	LED_BLUE2_ON,
	LED_BLUE3_ON,
	LED_BLUE4_ON,
	LED_BLUE5_ON,

	LED_ALL_OFF,
} MMI_LED_LASTSTATE;

/**************************LED ���ñ��ṹ��*****************************************************/

typedef struct {
	T_zMmi_Sms_Info sms_info;
	T_zMmi_Led_Info led_info;
} T_zMmiSmsLedConfig;

typedef struct {
	T_zMmi_Voip_Info voip_info;
	T_zMmi_Led_Info led_info;
} T_zMmiVoipLedConfig;

typedef struct {
	T_zMmi_Rj11_Info rj11_info;
	T_zMmi_Led_Info led_info;
} T_zMmiRj11LedConfig;


typedef struct {
	int custom_type;
	T_zMMINetInfo net_info;
	T_zMmi_Led_Info led_info;
} T_zMmiNetLedConfig;

typedef struct {
	T_zMMIBatteryInfo bat_info;
	T_zMmi_Led_Info led_info;
} T_zMmiBatteryLedConfig;

typedef struct {
	int custom_type;
	T_zMMIWifiInfo wifi_info;
	T_zMmi_Led_Info led_info;
} T_zMmiWifiLedConfig;


/*******************************************************************************
 ������ض���
*******************************************************************************/
#define KPD_PATH 						"/dev/event0"
#define KEY_POWER_CODE              116
#define KEY_WPS_CODE                117
#define KEY_RESET_CODE              118
#define KEY_WIFI_CODE               119 //ҢԶcpe



typedef enum {
	KEY_STATUS_MAX = 0,				/*��Ч״̬*/
	KEY_STATUS_UP,                  /*�����ɿ�*/
	KEY_STATUS_DOWN,                /*��������*/
	KEY_STATUS_LONGPRESS,           /*��������*/
	KEY_STATUS_LONGUP,              /*demo�Զ��幦��*/
} KEY_STATUS;

typedef enum {
	KEY_TYPE_POWER = 0,					/*power ��*/
	KEY_TYPE_RESET, 					/*reset ��*/
	KEY_TYPE_WPS,				        /*wps ��*/
	KEY_TYPE_WIFI,				        /*wifi ��, ���� ҢԶcpe*/
} KEY_TYPE;

typedef struct {
	KEY_TYPE    type;               /*��������*/
	KEY_STATUS  status;             /*����״̬*/
} MMI_KP_INFO;


/*******************************************************************************
 �����ض���
*******************************************************************************/


/*��ȡҵ����Ϣ����ָ��
appInfo:���Σ�ҵ����Ϣ����������ݽṹ���ֵָ�����ݽṹ��ַ
������ȷ�����
*/
typedef SINT32(*MMI_GET_TASKINFO_FUNC)(UINT32 taskInfo);

/*��ȡled��ʾ��Ϣ����ָ��
appInfo:��Σ�ҵ����Ϣ����������ݽṹ���ֵָ�����ݽṹ��ַ
outLedInfo:���Σ�led��ʾ��Ϣ
������ȷ�����
*/
typedef SINT32(*MMI_GET_LEDINFO_FUNC)(UINT32 taskInfo, UINT32 outLedInfo);

/*��ȡlcd��ʾ��Ϣ����ָ��
appInfo:��Σ�ҵ����Ϣ����������ݽṹ���ֵָ�����ݽṹ��ַ
outLcdInfo:���Σ�lcd��ʾ��Ϣ
������ȷ�����
*/
typedef SINT32(*MMI_GET_LCDINFO_FUNC)(UINT32 taskInfo);
/*��ʾLED
ledInfo:��Σ�ҵ����Ϣ����������ݽṹ���ֵָ�����ݽṹ��ַ
������ȷ�����
*/
typedef SINT32(*MMI_SHOW_LED_FUNC)(UINT32 ledInfo);
/*��ʾLCD
lcdInfo:��Σ�ҵ����Ϣ����������ݽṹ���ֵָ�����ݽṹ��ַ
������ȷ�����
*/
typedef SINT32(*MMI_SHOW_LCD_FUNC)(UINT32 lcdInfo);

/*�ýṹ�����ڱ���ÿ��ҵ���������ṩ������Ϣ*/
typedef struct {
	E_zMmi_Task task;//ҵ��
	SINT32 is_update;//�Ƿ��и���
	//SINT32 show_mode;//led,1;lcd,2;lcd+lcd,3
	VOID* taskinfo;
	VOID* ledinfo;
	MMI_GET_TASKINFO_FUNC   get_taskinfo_fun;
	MMI_GET_LEDINFO_FUNC get_ledinfo_fun;
	MMI_GET_LCDINFO_FUNC get_lcdinfo_fun;
} T_zMMITaskInfoItem;

//lcd ��ʾҳ
typedef enum {
	MMI_SHOW_PAGE_FIRST = 0,//traffic
	MMI_SHOW_PAGE_SECOND, //SSID WIFI KEY; IMEI MSISDN
	MMI_SHOW_PAGE_THIRD,//CODE
	MMI_SHOW_PAGE_FOUR,//SSID2 WIFI KEY
	MMI_SHOW_PAGE_FIVE,//CODE2
	MMI_SHOW_PAGE_MAX
} E_zMMI_Lcd_Page_Index;


/**********************************************************************************
��������
***********************************************************************************/
/*���ػ��ص�������lcd��ʾ��*/
typedef VOID (*POWER_ON_OFF_CALLBACK_FUN)();
VOID mmi_registerLcdPowerOnOff(POWER_ON_OFF_CALLBACK_FUN fun);
//���ô������±�־
VOID mmi_set_update_flag(E_zMmi_Task task);
//������±�־
VOID mmi_clean_update_flag(E_zMmi_Task task);
//��ȡ���±�־
SINT32 mmi_get_update_flag(E_zMmi_Task task);

/**
 * <mmi_register_appinfo_item>
 * ע�����ҵ����Ϣ
 **/
VOID mmi_register_taskInfo_item(T_zMMITaskInfoItem* taskInfoItem);
SINT32 mmi_showLed(UINT32 ledInfo);

#define MMI_ASSERT(a)		assert(a);
#define itoa(i,a,b) (((b) == 16) ? sprintf((a), "%x", (i)) : sprintf((a), "%d", (i)));
/**********************************************************************************
*��������:���ػ���Ϣ
***********************************************************************************/
SINT32 mmi_getLcdCtrlInfo(UINT32 taskInfo);
SINT32 mmi_getLedCtrlInfo(UINT32 taskInfo, UINT32 outLedInfo);
/**********************************************************************************
*��������:��ȡ���ŵ���Ϣ
***********************************************************************************/
SINT32 mmi_getLedSmsInfo(UINT32 taskInfo, UINT32 outLedInfo);
SINT32 mmi_getLcdSmsInfo(UINT32 taskInfo);
/**********************************************************************************
*��������:��ȡ������ʾ����Ϣ
***********************************************************************************/
SINT32 mmi_getLedVoipInfo(UINT32 taskInfo, UINT32 outLedInfo);

SINT32 mmi_getLedRj11Info(UINT32 taskInfo, UINT32 outLedInfo);

/**********************************************************************************
*��������:��ȡ���ŵ���Ϣ������
***********************************************************************************/
SINT32 mmi_get_batteryinfo(UINT32 batteryinfo);
/**********************************************************************************
*��������:��ȡ�������Ϣ
***********************************************************************************/
SINT32 mmi_getLedNetInfo(UINT32 taskInfo, UINT32 outLedInfo);
SINT32 mmi_getLcdNetInfo(UINT32 taskInfo);
/**********************************************************************************
*��������:��ȡ�����źŸ���Ϣ��ҢԶcpe��
***********************************************************************************/
SINT32 mmi_getLedNetSigInfo(UINT32 taskInfo, UINT32 outLedInfo);
/**********************************************************************************
*��������:��ȡWIFI����Ϣ
***********************************************************************************/
SINT32 mmi_getLedWifiInfo(UINT32 taskInfo, UINT32 outLedInfo);
SINT32 mmi_getLcdWifiInfo(UINT32 taskInfo);
SINT32 mmi_getLedWpsInfo(UINT32 taskInfo, UINT32 outLedInfo);

/**********************************************************************************
*��������:��ȡ��������Ϣ
***********************************************************************************/
SINT32 mmi_getLedTrafficInfo(UINT32 taskInfo, UINT32 outLedInfo);
SINT32 mmi_getLcdTrafficInfo(UINT32 taskInfo);
/**********************************************************************************
*��������:��ȡ�ƶ���Ӫ��������Ϣ
***********************************************************************************/
SINT32 mmi_getLcdQuotaTrafficInfo(UINT32 taskInfo);
/**********************************************************************************
*��������:��ȡ��ǰ����ģʽ
***********************************************************************************/
/**********************************************************************************
*��������:��ȡ��ص���Ϣ
***********************************************************************************/
SINT32 mmi_getLedBatteryInfo(UINT32 taskInfo, UINT32 outLedInfo);
SINT32 mmi_getLcdBatteryInfo(UINT32 taskInfo);
SINT32 mmi_getLcdPowerInfo(UINT32 taskInfo);
SINT32 mmi_getLcdTipInfo(UINT32 taskInfo);
SINT32 mmi_getLcdPowerOffChagerInfo(UINT32 taskInfo);
SINT32 mmi_getLedPowerOffChagerInfo(UINT32 taskInfo, UINT32 outLedInfo);


SINT32 mmi_getFotaUpdateStateInfo(VOID);//��ȡFOTA����״̬��Ϣ
SINT32 mmi_getLcdTipFotaInfo(UINT32 taskInfo);//FOTA
SINT32 mmi_getLcdWifiStationInfo(UINT32 taskInfo);//wifistation
SINT32 mmi_getLcdTipWpsInfo(UINT32 taskInfo);//wps
SINT32 mmi_getLcdTipNetConnInfo(UINT32 taskInfo);//net connect

SINT32 mmi_getLcdWifiCodeInfo(UINT32 taskInfo);//wifi��ά��

SINT32 mmi_getLcdSSIDKeyInfo(UINT32 taskInfo);
SINT32 mmi_getLcdBasicDevInfo(UINT32 taskInfo);//SSID/WIFIKEY/IMSI/MSISDN
SINT32 mmi_getLcdCurConInfo(UINT32 taskInfo);//��������ʱ�䣬��������
SINT32 mmi_getLcdTimeInfo(UINT32 taskInfo);//time

/**********************************************************************************
*��������:��ȡ����20s��ʱ����Ϣ
***********************************************************************************/
BOOL mmi_getSSIDFirstShowInfo(VOID);
/**********************************************************************************
*��������:��ȡ���������Ƿ������Ϣ
***********************************************************************************/
BOOL mmi_getShowingPowerOnInfo(VOID);
/**********************************************************************************
*��������:��ȡfota���������ʾ�Ƿ������Ϣ
***********************************************************************************/
BOOL mmi_getShowingFotaResultInfo(VOID);
/**********************************************************************************
*��������:��ȡfota�°汾��Ϣ
***********************************************************************************/
BOOL mmi_getFotaNewVersionInfo(VOID);

E_zMmi_Work_Mode mmi_get_lcd_mode(VOID);
E_zMmi_Work_Mode mmi_get_led_mode(VOID);

/**
 * <mmi_sms_Led_Init>
 * ���ŵƳ�ʼ��
 **/
VOID mmi_sms_led_init(VOID);


/**
 * <mmi_wifi_Led_Init>
 * Wifi �Ƴ�ʼ��
 **/
VOID mmi_wifi_led_init(VOID);


/**
 * <mmi_net_Led_Init>
 * ����Ƴ�ʼ��
 **/
VOID mmi_net_led_init(VOID);


/**
 * <mmi_battery_Led_Init>
 * ��صƳ�ʼ��
 **/
VOID mmi_battery_led_init(VOID);



/**
 * <mmi_traffic_Led_Init>
 * �����Ƴ�ʼ��
 **/
VOID mmi_traffic_led_init(VOID);



/**
 * <mmi_keyStrokes_Init>
 * ������ʼ��
 **/
VOID mmi_keyStrokes_init(VOID);

/**
 * <mmi_recover_allleds>
 *����˯��״̬ʱ�������е�
 **/
VOID mmi_set_allleds_on(VOID);

/**
 * <mmi_init_idle_control>
 *MMIʡ����Ƴ�ʼ��
 **/

VOID mmi_init_idle_control(VOID);


/**
 * <mmi_reset_idle_timer>
 *ʡ����ض�ʱ������
 **/

VOID mmi_reset_idle_timer(VOID);

/**
 * <mmi_reset_idle_timer>
 *���ٿ��ػ�ʱʡ����ض�ʱ������
 **/

VOID mmi_reset_fast_poweron_idle_timer(VOID);

#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
/**
 * <mmi_digits_sleep_timer_start>
 * 启动数码管休眠定时器
 **/
VOID mmi_digits_sleep_timer_start(VOID);

/**
 * <mmi_digits_sleep_timer_stop>
 * 停止数码管休眠定时器
 **/
VOID mmi_digits_sleep_timer_stop(VOID);

/**
 * <mmi_digits_wakeup>
 * 唤醒数码管显示
 **/
VOID mmi_digits_wakeup(VOID);

/**
 * <mmi_digits_sleep>
 * 数码管进入休眠状态
 **/
VOID mmi_digits_sleep(VOID);

/**
 * <mmi_is_digits_sleeping>
 * 判断数码管是否处于休眠状态
 **/
BOOL mmi_is_digits_sleeping(VOID);
#endif

/**
 * <mmi_close_chg_handle>
 * �رճ���豸���
 **/
VOID mmi_close_chg_handle(VOID);

/**
 * <mmi_close_chg_handle>
 * �رհ����豸���
 **/
VOID mmi_close_kpd_handle(VOID);

/**
 * <mmi_close_chg_handle>
 * �ر�LED �豸���
 **/
VOID mmi_close_led_handle(VOID);

/**********************************************************************************
��������:��ʼ��LED�豸
***********************************************************************************/
VOID mmi_led_dev_init(VOID);

/**
 * <zMMI_Handle_Msg_Idle_ledon>
 * ����ʡ�翪����Ϣ
 **/

VOID zMMI_Handle_Msg_Idle_ledon(VOID);


/**
 * <mmi_set_allleds_blink>
 * ������ػ�ʱ���е��ȿ����
 **/

VOID mmi_set_allleds_blink(VOID);

VOID mmi_traffictip_timer_stop(VOID);

/**
 * <mmi_kill_idle_timer>
 *killʡ����ض�ʱ��
 **/
VOID mmi_kill_idle_timer(VOID);

/**
 * <mmi_kill_tip_timer>
 *kill sim��ѭ����ⶨʱ��
 **/
VOID mmi_kill_tip_timer(VOID);

/**
 * <mmi_kill_traffic_timer>
 *kill ����ѭ����ⶨʱ��
 **/
VOID mmi_kill_traffic_timer(VOID);

/**
 * <mmi_kill_wifi_data_timer>
 *kill wifi���ݴ���ѭ����ⶨʱ��
 **/
VOID mmi_kill_wifi_data_timer(VOID);

/**
 * <mmi_kill_get_voltage_timer>
 *kill��ȡ��ѹ��ʱ��
 **/

VOID mmi_kill_get_voltage_timer(VOID);



/**
 * <mmi_create_get_batlev_thread>
 * ������ȡ��ص����ٷֱȵ��߳�
 **/
VOID mmi_create_get_batlev_thread(VOID);
/**
 * <get_voltage_level_from_table>
 * �ӵ�ذٷֱȱ��л�ȡ��ص����ٷֱ�
 **/
SINT32 get_voltage_level_from_table(SINT32 voltagepower);
/**
 * <mmi_voltage_state_read>
 * ��ȡ��ѹ
 **/
SINT32 mmi_voltage_state_read(VOID);

SINT32 zMMI_Handle_Msg_Network_Mode(VOID *data);
SINT32 zMMI_Handle_Msg_Connect_Status(VOID *data);
SINT32 zMMI_Handle_Msg_MultiConnect_Status(VOID *data, int src_id);
SINT32 zMMI_Handle_Msg_Signal_Num(VOID *data);
SINT32 zMMI_Handle_Msg_Simcard_Info(VOID *data);
SINT32 zMMI_Handle_Msg_Get_Wifi_StaNum(VOID *data);
SINT32 zMMI_Handle_Msg_SmsBox_Sattus(VOID *data);
SINT32 zMMI_Handle_Msg_Voip_Status(VOID *data);
SINT32 zMMI_Handle_Msg_Rj11_Status(VOID * data);

SINT32 zMMI_Handle_Msg_Get_SSID_Key(VOID *data);
SINT32 zMMI_Handle_Msg_BacklightOff(VOID *data);
SINT32 zMMI_Handle_Msg_Factory_Reset(VOID *data);
SINT32 zMMI_Handle_Msg_Restart(VOID *data);
SINT32 zMMI_Handle_Msg_Poweroff(VOID *data);
SINT32 zMMI_Handle_Msg_Get_Provider(VOID *data);
SINT32 zMMI_Handle_Msg_Get_TafficInfo_Start(VOID* data);
SINT32 zMMI_Handle_Msg_Get_TafficInfo_End(VOID* data);
SINT32 zMMI_Handle_Msg_TafficInfo_Reset(VOID* data);
SINT32 zMMI_Handle_Msg_Check_Tip_Info(VOID *data);
SINT32 zMMI_Handle_Msg_SSID_Show_Info(VOID *data);
SINT32 zMMI_Handle_Msg_Get_Wifi_Data(VOID *data);
SINT32 zMMI_Handle_Msg_Socket_Status(VOID *data, int src_id);
SINT32 zMMI_Handle_Msg_MCUSocket_Status(VOID *data);

SINT32	mmi_RegisterNetTaskInfoItem();
SINT32	mmi_RegisterSmsTaskInfoItem();
SINT32  mmi_RegisterVoipTaskInfoItem();
SINT32 mmi_RegisterRj11TaskInfoItem();

SINT32 	mmi_RegisterBatteryTaskInfoItem();
SINT32 	mmi_RegisterPowerTaskInfoItem();
SINT32	mmi_RegisterCtrlTaskInfoItem();
SINT32 	mmi_RegisterTimeTaskInfoItem();
SINT32 	mmi_RegisterSSIDInfoTaskInfoItem();
SINT32 	mmi_RegisterWifiTaskInfoItem();
SINT32 mmi_RegisterWpsTaskInfoItem();

SINT32 	mmi_RegisterWifiCodeTaskInfoItem();
SINT32 	mmi_RegisterTrafficTaskInfoItem();
SINT32 	mmi_RegisterQuotaTrafficInfoItem();
SINT32 	mmi_RegisterTipTaskInfoItem();
SINT32 	mmi_RegisterTipWifiStationConnectTaskInfoItem();
SINT32 	mmi_RegisterTipWpsTaskInfoItem();
SINT32 mmi_RegisterNetSigTaskInfoItem();
SINT32 	mmi_RegisterTipFotaTaskInfoItem();
SINT32  mmi_RegisterTipNetConnectTaskInfoItem();
SINT32 	mmi_RegisterTipWifiStationConnectTaskInfoItem();
void	mmi_net_init();
void	mmi_sms_init();
void    mmi_voip_init();
void	mmi_init_idle_control();
void	mmi_battery_init();
void	mmi_init_lcd_tip();
void	mmi_init_time();
void	mmi_wifi_init();
void	mmi_traffic_init();
void	mmi_init_quota_traffic();
VOID    mmi_play_poweroff_cartoon(E_zMmi_Work_Mode mode);
VOID    mmi_set_fake_poweroff_charger(int fake_poweroff_flag);
VOID    mmi_lowbattery_discharge_protect(UINT32 voltagepower);
VOID    mmi_set_discharge_low_current(BOOL discharge_low_current_flag);
VOID    mmi_handle_fast_poweroff(VOID);
VOID    mmi_wifi_operate(char* mode);
void mmi_wifi_bandswitch(void);
BOOL    mmi_get_usb_insert_state();
E_zMmi_Charge_State     mmi_get_charge_state();
VOID   mmi_handle_fast_poweron(VOID);
VOID   mmi_set_discharge_switch(BOOL discharge_protect_flag);
void mmi_log_save(const char *fmt, ...);
VOID mmi_sleep(UINT32 time_in_ms);
void mmi_add_list(struct list_head *head, int src_id);
void mmi_del_list(struct list_head *head, int src_id);

VOID mmi_clean_net_state();
VOID mmi_set_wificode_show_flag(BOOL flag);
E_zMMI_Lcd_Page_Index mmi_get_lcd_page_index(VOID);
E_zMmi_Sim_Tip mmi_getSIMStateInfo(VOID);
E_zMmi_NetCon_Tip mmi_getNetConInfo(VOID);
E_zMmi_WpsAct_Tip mmi_get_wps_state();
VOID mmi_set_poweroff_charge_show(BOOL show);
#ifndef DISABLE_LCD
SINT32 mmi_showLcd(HDC hdc);
#endif
VOID mmi_initLcdShowInfoTab();
VOID mmi_handle_lcd_key_switch_page();
VOID mmi_idle_timer_stop(VOID);
VOID mmi_wps_operate(VOID);
VOID mmi_changePowerOnOffFrame(VOID);
VOID mmi_startLedTrafficWarnTimer(SINT32 time, SINT32 flag);
VOID mmi_stopLedTrafficWarnTimer();
VOID mmi_poweroffcharger_init();
UINT32 mmi_get_net_state(VOID);
/**��������:�Զ�������ģ�ⰴ��**/
SINT32 zMMI_Handle_Msg_Atest_Key(VOID *data);
SINT32 zMMI_Handle_Msg_Atest_Chg(VOID *data);





#endif //__MMI_COMMON_H
