# WiFiDog Configuration for ZXIC_ONELINK_TEST
# 适配 ZTE MiFi ONELINK 测试环境
# 网关地址：br0 IP *************
# 使用goahead作为认证服务器

# Gateway identification
GatewayID zte_mifi_onelink_test

# Network interfaces
GatewayInterface br0
GatewayAddress *************
GatewayPort 2060

# External interface (auto-detected if not specified)
# ExternalInterface eth0

# Authentication server configuration
# 使用网关自身作为认证服务器，处理OneLink认证流程
AuthServer {
    Hostname *************
    HTTPPort 80
    SSLAvailable no
    Path /goform/
    LoginScriptPathFragment goform_set_cmd_process?goformId=ONELINK_LOGIN&
    PortalScriptPathFragment goform_set_cmd_process?goformId=ONELINK_PORTAL&
    MsgScriptPathFragment goform_set_cmd_process?goformId=ONELINK_MSG&
    PingScriptPathFragment goform_set_cmd_process?goformId=ONELINK_PING&
    AuthScriptPathFragment goform_set_cmd_process?goformId=ONELINK_AUTH&
}

# Backup authentication server (optional)
# AuthServer {
#     Hostname backup.cmonelink.com
#     HTTPPort 80
#     SSLAvailable no
#     Path /
# }

# Daemon settings
Daemon 1

# HTTP server settings
HTTPDName WiFiDog-OneLink
HTTPDMaxConn 20
HTTPDRealm WiFiDog-OneLink

# Optional: HTTP authentication for status page
# HTTPDUserName admin
# HTTPDPassword onelink123

# Client management
CheckInterval 60
ClientTimeout 5

# Popular servers for connectivity check
PopularServers www.baidu.com,www.qq.com,wireless.cmonelink.com

# SSL settings - 已禁用，因为 WiFiDog 没有编译 SSL 支持
# SSLPeerVerification no
# SSLCertPath /etc/ssl/certs/

# Trusted MAC addresses for testing (bypass authentication)
# 可以添加测试设备的 MAC 地址来绕过认证
# TrustedMACList 00:11:22:33:44:55,aa:bb:cc:dd:ee:ff

# Firewall rules
FirewallRuleSet global {
    # Allow DNS
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
    
    # Allow DHCP
    FirewallRule allow udp port 67
    FirewallRule allow tcp port 67
    
    # Allow NTP
    FirewallRule allow udp port 123
    
    # 注意：不在global中允许HTTP/HTTPS，强制用户进行认证
    # 移除了通用的HTTP/HTTPS允许规则，确保未认证用户无法直接上网
    
    # Block SMTP to prevent spam
    FirewallRule block tcp port 25
}

FirewallRuleSet validating-users {
    # Users in validation process - allow all
    FirewallRule allow to 0.0.0.0/0
}

FirewallRuleSet known-users {
    # Authenticated users - allow all
    FirewallRule allow to 0.0.0.0/0
}

FirewallRuleSet unknown-users {
    # Unauthenticated users - limited access
    
    # Allow DNS for domain resolution
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
    
    # Allow DHCP
    FirewallRule allow udp port 67
    FirewallRule allow tcp port 67
    
    # Allow access to authentication server
    FirewallRule allow tcp port 443 to wireless.cmonelink.com
    FirewallRule allow tcp port 80 to wireless.cmonelink.com
    
    # Allow captive portal detection for various devices
    # 使用 IP 地址范围而不是域名，避免 iptables 域名解析问题

    # Apple captive portal detection (********/8)
    FirewallRule allow tcp port 80 to ********/8
    FirewallRule allow tcp port 443 to ********/8

    # Google/Android captive portal (*******, *******, ***********/16)
    FirewallRule allow tcp port 80 to *******
    FirewallRule allow tcp port 443 to *******
    FirewallRule allow tcp port 80 to ***********/16
    FirewallRule allow tcp port 443 to ***********/16

    # Microsoft captive portal (************, *********/22)
    FirewallRule allow tcp port 80 to ************
    FirewallRule allow tcp port 443 to ************

    # 百度 (**************, ************)
    FirewallRule allow tcp port 80 to **************
    FirewallRule allow tcp port 443 to **************
    FirewallRule allow tcp port 80 to ************
    FirewallRule allow tcp port 443 to ************
    
    # Allow access to gateway itself for status/management
    FirewallRule allow tcp port 2060 to *************
    FirewallRule allow tcp port 80 to *************
    
    # Block everything else by default
    # (WiFiDog automatically adds DROP rule for unknown users)
}

FirewallRuleSet locked-users {
    # Locked users - block all traffic
    FirewallRule block to 0.0.0.0/0
}

# Auth server down fallback (optional)
# FirewallRuleSet auth-is-down {
#     # When auth server is unreachable, allow limited access
#     FirewallRule allow tcp port 80
#     FirewallRule allow tcp port 443
# }
