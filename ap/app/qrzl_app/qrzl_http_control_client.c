#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <stdbool.h>

#include "qrzl_http_control_client.h"
#include "common_utils/cjson.h"
#include "qrzl_utils.h"
#include "curl/curl.h"
#include "softap_api.h"

#define QRZL_HTTP_RESPONSE_MAX 5120

/* 请求间隔时间 */
static int request_interval_time = 300;
/* http请求的路径 */
static char http_request_path[256] = {0};

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

static uint64_t pre_report_flux_total_bytes = 0; // 上次上报的总流量

// Function declarations
int xunji_only_reset_tcp();

/**
 * 初始化一些配置信息 
 * */
static int init_config_data() 
{
    int cfg_ret;
    cfg_ret = cfg_get_item(NV_QRZL_CLOUD_HTTP_PATH, http_request_path, 256);
    if (cfg_ret != 0 || http_request_path == NULL || strcmp(http_request_path, "") == 0)
    {
        qrzl_log("http_request_path is NULL");
        return -1;
    }
    
    // strcpy(http_request_path, "192.168.0.111:8080/api/devicesev/statusUpdate");
    char cloud_request_interval_time[10] = {0};
    cfg_get_item(NV_QRZL_CLOUD_REQUEST_INTERVAL_TIME, cloud_request_interval_time, 10);
    request_interval_time = atoi(cloud_request_interval_time);
    if (request_interval_time == 0) {
        request_interval_time = 300;
    }
    return 0;
}


// HTTP回调函数，用于处理HTTP响应
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    if (totalSize >= QRZL_HTTP_RESPONSE_MAX)
    {
        qrzl_err("http返回值大小: %d,大于已定义的最大长度", totalSize);
        return 0;
    }
    strncat((char *)userp, (char *)contents, totalSize);
    return totalSize;
}

// 发送GET请求
static int http_send_get_request(const char *url, char *response) {
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);
    curl = curl_easy_init();
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        curl_easy_setopt(curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_WHATEVER); // 自动选择IPv4v6
        // curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_err("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        curl_easy_cleanup(curl);
        if (res != CURLE_OK) {
            return -1;
        }
        return 0;
    }
    return -1;
}

static int https_send_get_request(const char *url, char *response) {
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);

    curl = curl_easy_init();
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
        // curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_err("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        curl_easy_cleanup(curl);
        if (res != CURLE_OK) {
            return -1;
        }
        return 0;
    }
    return -1;
}

static int http_send_post_request(const char *url, const char *body, char *response)
{
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);

    curl = curl_easy_init();
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        curl_easy_setopt(curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_WHATEVER); // 自动选择IPv4v6
        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        // 设置POST请求的内容
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);

        // 设置HTTP头（告诉服务器这是JSON数据）
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_err("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        // 清理
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        if (res != CURLE_OK)
        {
            return -1;
        }
        return 0;
    }
    return -1;
}


/***===================== 以下都是奇成对接相关处理逻辑  ===========================***/
#ifdef QRZL_HTTP_CLIENT_QICHENG

static int qc_first_report_flag = 1;

/** 
 * 转换时间戳为字符串yyyymmddhhmmss
 */
static void timestamp_to_string(time_t timestamp, char *buffer, size_t buffer_size)
{
    // 将时间戳转换为 tm 结构
    struct tm *time_info = localtime(&timestamp);

    // 格式化时间为 yyyymmddhhmmss
    strftime(buffer, buffer_size, "%Y%m%d%H%M%S", time_info);
}

// 拼接请求URL和参数
static void qicheng_build_request_url(char *url_buffer, size_t buffer_size)
{
    snprintf(url_buffer, buffer_size, "%s", http_request_path);

    snprintf(url_buffer, buffer_size, "%s?imei=%s", url_buffer, g_qrzl_device_static_data.imei);

    if(qc_first_report_flag==1)
    {
        snprintf(url_buffer,buffer_size,"%s&first_report=TRUE", url_buffer);
    }else
    {
        snprintf(url_buffer,buffer_size,"%s&first_report=FALSE", url_buffer);
    }

#if defined(QRZL_AUTH_ONE_LINK_HTTP) || defined(QRZL_AUTH_CMP_HTTP)
    // 二次认证开关状态上报 TRUE/FALSE
    char authentic_switch[10] = {0};
    cfg_get_item("qrzl_cloud_authentic_switch", authentic_switch, sizeof(authentic_switch));
    if(strlen(authentic_switch)==0)
    {
        snprintf(url_buffer, buffer_size, "%s&authentic_switch=0", url_buffer);
    }
    else{
        if (strcmp(authentic_switch, "1") == 0)
        {
            snprintf(url_buffer, buffer_size, "%s&authentic_switch=1", url_buffer);
        }
        else
        {
            snprintf(url_buffer, buffer_size, "%s&authentic_switch=0", url_buffer);
        }
    }
#endif

    snprintf(url_buffer, buffer_size, "%s&signal_strength=%d", url_buffer, (get_rsrp_percentage() / 20) - 1);

    snprintf(url_buffer, buffer_size, "%s&iccid=%s", url_buffer, g_qrzl_device_dynamic_data.iccid);

    snprintf(url_buffer, buffer_size, "%s&mac=%s", url_buffer, g_qrzl_device_static_data.mac);

    // 奇成的单位是Kb
    snprintf(url_buffer, buffer_size, "%s&dayflow=%llu", url_buffer, g_qrzl_device_dynamic_data.flux_day_total_bytes * 8 / 1024);

#ifdef QRZL_CUSTOM_TIANMU_LOGIC
    snprintf(url_buffer, buffer_size, "%s&monthflow=%llu", url_buffer, g_qrzl_device_dynamic_data.flux_month_total_bytes * 8 / 1024);
    if(pre_report_flux_total_bytes==0)
    snprintf(url_buffer, buffer_size, "%s&amount_end=%llu", url_buffer, 0);
    else
    snprintf(url_buffer, buffer_size, "%s&amount_end=%llu", url_buffer, (g_qrzl_device_dynamic_data.flux_total_bytes-pre_report_flux_total_bytes )* 8 / 1024);
    
#else
    snprintf(url_buffer, buffer_size, "%s&amount_end=%llu", url_buffer, g_qrzl_device_dynamic_data.flux_month_total_bytes * 8 / 1024);
#endif

    char time_string[15] = {0}; // 长度需要能存储 "yyyymmddhhmmss" 和一个 '\0'
    time_t current_time = time(NULL);
    timestamp_to_string(current_time, time_string, sizeof(time_string));
    snprintf(url_buffer, buffer_size, "%s&current_time=%s", url_buffer, time_string);

    snprintf(url_buffer, buffer_size, "%s&ssid=%s", url_buffer, g_qrzl_device_dynamic_data.wifi_ssid);

    snprintf(url_buffer, buffer_size, "%s&key=%s", url_buffer, g_qrzl_device_dynamic_data.wifi_key_base64);

    snprintf(url_buffer, buffer_size, "%s&remain_pwr=%s", url_buffer, g_qrzl_device_dynamic_data.remain_power);

    snprintf(url_buffer, buffer_size, "%s&conn_cnt=%d", url_buffer, g_qrzl_device_dynamic_data.conn_num);

    snprintf(url_buffer, buffer_size, "%s&max_conn=%d", url_buffer, g_qrzl_device_dynamic_data.max_access_num);

    snprintf(url_buffer, buffer_size, "%s&mcc=%s", url_buffer, g_qrzl_device_dynamic_data.mcc);

    snprintf(url_buffer, buffer_size, "%s&mnc=%s", url_buffer, g_qrzl_device_dynamic_data.mnc);

    int tac = atoi(g_qrzl_device_dynamic_data.tac);
    snprintf(url_buffer, buffer_size, "%s&lac=%x", url_buffer, tac);

    int cid = atoi(g_qrzl_device_dynamic_data.cid);
    snprintf(url_buffer, buffer_size, "%s&cid=%x", url_buffer, cid);

    snprintf(url_buffer, buffer_size, "%s&soft_version=%s", url_buffer, g_qrzl_device_static_data.soft_version);

    //设备运行时间
    double qc_time = get_device_uptime();
    int qc_run_time_seconds = (int)qc_time;
    snprintf(url_buffer, buffer_size, "%s&run_time=%d", url_buffer, qc_run_time_seconds);

    //上报间隔
    snprintf(url_buffer, buffer_size, "%s&nextRptTime=%d", url_buffer, request_interval_time);

    /* 下面都是WiFi相关的 */
    int wifistatus = 1;
    if (g_qrzl_device_dynamic_data.wifi_enable == 0) {
        wifistatus = 0;
    } else if (g_qrzl_device_dynamic_data.wifi_enable == 1 && g_qrzl_device_dynamic_data.wifi_hide == 1)
    {
        wifistatus = 2;
    }
    snprintf(url_buffer, buffer_size, "%s&wifistatus=%d", url_buffer, wifistatus);

    snprintf(url_buffer, buffer_size, "%s&wifiband=2.4G", url_buffer);
    snprintf(url_buffer, buffer_size, "%s&hidden=%d", url_buffer, g_qrzl_device_dynamic_data.wifi_hide);

    snprintf(url_buffer, buffer_size, "%s&sn=%s", url_buffer, g_qrzl_device_static_data.sn);

    // 奇成的单位是 bps
    snprintf(url_buffer, buffer_size, "%s&upspeed=%llu", url_buffer, get_up_limit_net_speed() * 1000);

    // 奇成的单位是 bps
    snprintf(url_buffer, buffer_size, "%s&downspeed=%llu", url_buffer, get_down_limit_net_speed() * 1000);

    snprintf(url_buffer, buffer_size, "%s&RSSI=%s", url_buffer, g_qrzl_device_dynamic_data.rssi);

    snprintf(url_buffer, buffer_size, "%s&webPassword=%s", url_buffer, g_qrzl_device_dynamic_data.web_password);

    snprintf(url_buffer, buffer_size, "%s&RSRQ=%s", url_buffer, g_qrzl_device_dynamic_data.rsrq);

    snprintf(url_buffer, buffer_size, "%s&SINR=%s", url_buffer, g_qrzl_device_dynamic_data.sinr);

    snprintf(url_buffer, buffer_size, "%s&IMSI=%s", url_buffer, g_qrzl_device_dynamic_data.imsi);

    snprintf(url_buffer, buffer_size, "%s&Devicetype=%s", url_buffer, g_qrzl_device_static_data.device_type);

    char apn_config_name[65] = {0};
    cfg_get_item("m_profile_name", apn_config_name, sizeof(apn_config_name));
    remove_spaces(apn_config_name);
    char apn_username[65] = {0};
    cfg_get_item("ppp_username", apn_username, sizeof(apn_username));
    char apn_password[65] = {0};
    cfg_get_item("ppp_passtmp", apn_password, sizeof(apn_password));
    char apn_login_number[32] = {0};
    cfg_get_item("wan_dial", apn_login_number, sizeof(apn_login_number));
    char apn_apn[32] = {0};
    cfg_get_item("wan_apn", apn_apn, sizeof(apn_apn));
    char apn_pdp_type[32] = {0};
    cfg_get_item("pdp_type", apn_pdp_type, sizeof(apn_pdp_type));
    char apn_auth_typee[32] = {0};
    cfg_get_item("ppp_auth_mode", apn_auth_typee, sizeof(apn_auth_typee));

    char apn[512] = {0};
    snprintf(apn, sizeof(apn), 
    "MCCMNC:%s%s;ConfigFileName:%s;UserName:%sPassword:%s;LoginNumber:%s;APN:%s;PDPType:%s;AuthType:%s;", 
    g_qrzl_device_dynamic_data.mcc, g_qrzl_device_dynamic_data.mnc, apn_config_name, apn_username, apn_password, apn_login_number, apn_apn, apn_pdp_type, apn_auth_typee);
    remove_spaces(apn);

    char new_apn[512]={0};
    url_encode(apn,new_apn);

    snprintf(url_buffer, buffer_size, "%s&apn=%s", url_buffer, new_apn);

    snprintf(url_buffer, buffer_size, "%s&apEncrypttype=%s", url_buffer, g_qrzl_device_dynamic_data.wifi_auth_mode);

    snprintf(url_buffer, buffer_size, "%s&Wifi_filter_type=%d", url_buffer, g_qrzl_device_dynamic_data.wifi_filter_type);

    snprintf(url_buffer, buffer_size, "%s&Blacklist=%s", url_buffer, g_qrzl_device_dynamic_data.mac_black_list);

    snprintf(url_buffer, buffer_size, "%s&Whitelist=%s", url_buffer, g_qrzl_device_dynamic_data.mac_white_list);

    snprintf(url_buffer, buffer_size, "%s&CurrentIp=%s", url_buffer, g_qrzl_device_dynamic_data.current_wan_ip);

    snprintf(url_buffer, buffer_size, "%s&DualSIM=%d", url_buffer, 1);

    int main_sim = 0;
#ifdef QRZL_QICHENG_SWITCHCARD_ZERO_IS_OUTCARD
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0) {
        main_sim = 1;
    } else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0) {
        main_sim = 2;
    } else {
        main_sim = 0;
    }
#else
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0) {
        main_sim = 0;
    } else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0) {
        main_sim = 1;
    } else {
        main_sim = 2;
    }
#endif
    snprintf(url_buffer, buffer_size, "%s&mainSIM=%d", url_buffer, main_sim);

    snprintf(url_buffer, buffer_size, "%s&adjust_band=%s", url_buffer, g_qrzl_device_static_data.adjust_band);

    snprintf(url_buffer, buffer_size, "%s&band=%s", url_buffer, g_qrzl_device_dynamic_data.net_band);

}

static void qicheng_resp_handler(cJSON *value)
{
    
    int update_wifi_flag = 0;
    int save_nv_flag = 0;
    struct wifi_config_t wifi_config = {};
    init_wifi_config_value(&wifi_config);

    struct mac_filter_config_t mac_filter_config = {};
    init_mac_filter_config_value(&mac_filter_config);

    int restart_flag = 0;
    int reset_flag = 0;
    int update_mac_filter_flag = 0;
    int shutdown_flag = 0;
    // 遍历对象的键值对
    /* 限制速度，0 表 示不限速，限制 4G，任意速度， 单位为 Kbps，整数。 例如：256 */
    cJSON* j_limitSpeed = cJSON_GetObjectItem(value, "limitSpeed");
    if(j_limitSpeed != NULL && cJSON_IsString(j_limitSpeed))
    {
        int limit_speed;
        limit_speed = atoi(j_limitSpeed->valuestring);
        if (limit_speed >= 0)
        {   
            save_nv_flag |= 1;
#ifdef QRZL_CUSTOM_TIANMU_LOGIC
            if(limit_speed == 32)
            {
                //特殊禁网值
                set_network_br0_disconnect(1);
            }else if(limit_speed == 0)
            {
                //恢复网络,同时和限速一块解
                set_network_br0_disconnect(0);
                limit_net_speed(limit_speed, limit_speed);
            }else
            {
                //其余值当限速处理
                limit_net_speed(limit_speed, limit_speed);
            }
            //此处连接状态发1是因为能接收到消息，肯定是连接成功的
            int connect_status=1;
            ipc_send_message(MODULE_ID_QRZL_APP, MODULE_ID_MMI, MSG_CMD_CHANNEL_CONNECT_STATUS, sizeof(connect_status), (UCHAR *)&connect_status,0);
#else
            limit_net_speed(limit_speed, limit_speed);
#endif
            }
    }
    cJSON* j_disconnect = cJSON_GetObjectItem(value, "disconnect");
    if(j_disconnect != NULL && cJSON_IsString(j_disconnect))
    {
        if(strcmp(j_disconnect->valuestring, "1") == 0)
        {
            set_network_br0_disconnect(1);
        }
        else if(strcmp(j_disconnect->valuestring, "0") == 0)
        {
            set_network_br0_disconnect(0);
        }
    }
#if defined(QRZL_AUTH_ONE_LINK_HTTP) || defined(QRZL_AUTH_CMP_HTTP)
    cJSON* j_authentic_switch = cJSON_GetObjectItem(value, "authentic_switch");
    if(j_authentic_switch != NULL && cJSON_IsString(j_authentic_switch))
    {      
        if (strcmp(j_authentic_switch->valuestring, "1") == 0)
        {
            save_nv_flag |= 1;
            cfg_set("qrzl_cloud_authentic_switch", "1");
        }
        else if (strcmp(j_authentic_switch->valuestring, "0")) 
        {
            save_nv_flag |= 1;
            cfg_set("qrzl_cloud_authentic_switch", "0");
        }
    }
#endif
    /* 设备下次上报时间间隔，单位 秒 */
    cJSON* j_nextRptTime = cJSON_GetObjectItem(value, "nextRptTime");
    if(j_nextRptTime != NULL && cJSON_IsString(j_nextRptTime))
    {
        int next_rpt_time;
        next_rpt_time = atoi(j_nextRptTime->valuestring);
        if (next_rpt_time != request_interval_time)
        {
            request_interval_time = next_rpt_time;
        }
    }
    /* 设备清算时间，datetime 格式，返回时间小于该设置时间时，设备清理缓存数据 */
    cJSON* j_clrStaticsTime = cJSON_GetObjectItem(value, "clrStaticsTime");
    /* 服务器当前时间，datetime格式，用于设备时间校正 */
    cJSON* j_srvCurrTime = cJSON_GetObjectItem(value, "srvCurrTime");
    /* 设备下次上报流量间隔，单位 kb，暂时这个参 数没有使用，但 是需要预留 */
    cJSON* j_trafficRptThreshold = cJSON_GetObjectItem(value, "trafficRptThreshold");
    cJSON* j_ssidName = cJSON_GetObjectItem(value, "ssidName");
    if(j_ssidName != NULL && cJSON_IsString(j_ssidName))
    {
        update_wifi_flag |= 1;
        snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_ssidName->valuestring);
    }
    cJSON* j_ssidPass = cJSON_GetObjectItem(value, "ssidPass");
    if(j_ssidPass != NULL && cJSON_IsString(j_ssidPass))
    {
        if (strlen(j_ssidPass->valuestring) >= 8 && strlen(j_ssidPass->valuestring) < sizeof(wifi_config.key) - 1)
        {
            update_wifi_flag |= 1;
            snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_ssidPass->valuestring);
        }
    }
    /* 网络类型 4G/3G/2G/AUTO */
    cJSON* j_wan_type = cJSON_GetObjectItem(value, "wan_type");
    /* 强制重置设备 1：强制重置 0：不重置 */
    cJSON* j_force_reset = cJSON_GetObjectItem(value, "force_reset");
    if(j_force_reset != NULL && cJSON_IsString(j_force_reset))
    {
        if (strcmp(j_force_reset->valuestring, "1") == 0)
        {
            reset_flag |= 1;
        }
    }
    cJSON* j_wifistatus = cJSON_GetObjectItem(value, "wifistatus");
    if(j_wifistatus != NULL && cJSON_IsString(j_wifistatus))
    {
        if (strcmp(j_wifistatus->valuestring, "0") == 0)
        {
            wifi_config.enable = 0;
        }
        else if (strcmp(j_wifistatus->valuestring, "1") == 0)
        {
            wifi_config.enable = 1;
            wifi_config.hide = 0;
        }
        else if (strcmp(j_wifistatus->valuestring, "2") == 0)
        {
            wifi_config.enable = 1;
            wifi_config.hide = 1;
        }
        update_wifi_flag |= 1;
    }
    /* WIFI 状态 0 表示关闭，1 表示 打开，2 表示隐藏  SSID */
    cJSON* j_hiddenSsid = cJSON_GetObjectItem(value, "hiddenSsid");
    if(j_hiddenSsid != NULL && cJSON_IsString(j_hiddenSsid))
    {
        if(strcmp(j_hiddenSsid->valuestring, "0") == 0)
        {
            wifi_config.hide = 0;
        }
        else if(strcmp(j_hiddenSsid->valuestring, "1") == 0)
        {
            wifi_config.hide = 1;
        }
        update_wifi_flag |= 1;
    }
    /* 强制重启设备 1 重启，0 不重启 */
    cJSON* j_Force_restart = cJSON_GetObjectItem(value, "Force_restart");
    if(j_Force_restart != NULL && cJSON_IsString(j_Force_restart))
    {
        if(strcmp(j_Force_restart->valuestring, "1") == 0)
        {
            restart_flag |= 1;
        }
    }
    /*控制设备关机 1 关机,0 不关机*/
    cJSON* j_shutdown = cJSON_GetObjectItem(value, "shutdown");
    if(j_shutdown != NULL && cJSON_IsString(j_shutdown))
    {
        if(strcmp(j_shutdown->valuestring, "1") == 0)
        {
            qrzl_log("收到关机指令");
            shutdown_flag |= 1;
        }
    }
    /* 设备检查新版本升级, 0 不检查，1 检查 */
    cJSON* j_DeviceUpgrade = cJSON_GetObjectItem(value, "DeviceUpgrade");
    /* 修改设备管理 端密码 */
    cJSON* j_webPassword = cJSON_GetObjectItem(value, "webPassword");
    if(j_webPassword != NULL && cJSON_IsString(j_webPassword))
    {
        save_nv_flag |= 1;
        update_web_password(j_webPassword->valuestring);
    }
    /* 黑白名单模式 设置开启黑名单还 是白名单或都不开 启
    0：正常模式
    1： 白名单模式
    2：黑名单模式
    黑白名单模式与对  应列表需组合设置， 否则不生效。 */
    cJSON* j_Wifi_filter_type = cJSON_GetObjectItem(value, "Wifi_filter_type");
    if(j_Wifi_filter_type != NULL && cJSON_IsString(j_Wifi_filter_type))
    {
        int wifi_filter_type;
        wifi_filter_type = atoi(j_Wifi_filter_type->valuestring);
        if (wifi_filter_type >= 0 && wifi_filter_type < 3)
        {
             update_mac_filter_flag++;
            mac_filter_config.wifi_filter_type = wifi_filter_type;
        }
    }
    /* 设置黑名单 设置接入设备黑名 单
    内容为 mac 地址列 表， 以分号相隔
    22:06:B0:CA:33:CC ;22:06:B0:CA:33:CD */
    cJSON* j_Blacklist = cJSON_GetObjectItem(value, "Blacklist");
    if(j_Blacklist != NULL && cJSON_IsString(j_Blacklist))
    {
        update_mac_filter_flag++;
        strlcpy(mac_filter_config.mac_black_list, j_Blacklist->valuestring, sizeof(mac_filter_config.mac_black_list));
    }
    /* 设置白名单 设置接入设备白名 单
    内容为 mac 地址列 表， 以分号相隔
    22:06:B0:CA:33:CC ;22:06:B0:CA:33:CD*/
    cJSON* j_Whitelist = cJSON_GetObjectItem(value, "Whitelist");
    if(j_Whitelist != NULL && cJSON_IsString(j_Whitelist))
    {
        update_mac_filter_flag++;
        strlcpy(mac_filter_config.mac_white_list, j_Whitelist->valuestring, sizeof(mac_filter_config.mac_white_list));
    }
    /* 设置最大连接数, 最小1, 最大10 */
    cJSON* j_deviceCountSet = cJSON_GetObjectItem(value, "deviceCountSet");
    if(j_deviceCountSet != NULL && cJSON_IsString(j_deviceCountSet))
    {
        update_wifi_flag |= 1;
        wifi_config.max_access_num = atoi(j_deviceCountSet->valuestring);
    }
    /* Wifi 加密方式 0：OPEN 1：WPA2(AES)-PSK  2：WPA-PSK/WPA2-PSK*/
    cJSON* j_apEncrypttype = cJSON_GetObjectItem(value, "apEncrypttype");
    if(j_apEncrypttype != NULL && cJSON_IsString(j_apEncrypttype))
    {
        update_wifi_flag |= 1;
        if(strcmp(j_apEncrypttype->valuestring, "0") == 0)
        {
            strcpy(wifi_config.auth_mode, "OPEN");
        }
        else if(strcmp(j_apEncrypttype->valuestring, "1") == 0)
        {
            strcpy(wifi_config.auth_mode, "WPA2PSK");
        }
        else if(strcmp(j_apEncrypttype->valuestring, "2") == 0)
        {
            strcpy(wifi_config.auth_mode, "WPAPSKWPA2PSK");
        }
    }
    cJSON* j_switchSIM = cJSON_GetObjectItem(value, "switchSIM");
    cJSON* j_SwitchSim = cJSON_GetObjectItem(value, "SwitchSim");
    if ((j_switchSIM != NULL && cJSON_IsString(j_switchSIM)) || (j_SwitchSim != NULL && cJSON_IsString(j_SwitchSim)))
    {   
        const char* sim_value = NULL;
        // 优先使用 switchSIM
        if (j_switchSIM != NULL && cJSON_IsString(j_switchSIM)) {
            sim_value = j_switchSIM->valuestring;
        } 
        // 如果 switchSIM 不存在或不是字符串，使用 SwitchSim
        else if (j_SwitchSim != NULL && cJSON_IsString(j_SwitchSim)) {
            sim_value = j_SwitchSim->valuestring;
        }
        if (sim_value != NULL) {
            int switch_crad_count = 0;
            while(g_qrzl_device_dynamic_data.is_test_net == 1 && switch_crad_count < 30)
            {
                qrzl_log("正在测网，不能切卡");
                sleep(5);
                switch_crad_count++;
            }
#ifdef QRZL_QICHENG_SWITCHCARD_ZERO_IS_OUTCARD
            if (0 == strcmp(sim_value, "0"))
            {
                save_nv_flag |= 1;
                switch_sim_card_not_restart(0);
            }
            else if (0 == strcmp(sim_value, "1"))
            {
                save_nv_flag |= 1;
                switch_sim_card_not_restart(1);
            }
            else if (0 == strcmp(sim_value, "2"))
            {
                save_nv_flag |= 1;
                switch_sim_card_not_restart(2);
            }
#else
            if (0 == strcmp(sim_value, "0"))
            {
                save_nv_flag |= 1;
                switch_sim_card_not_restart(1);
            }
            else if (0 == strcmp(sim_value, "1"))
            {
                save_nv_flag |= 1;
                switch_sim_card_not_restart(2);
            }
            else if (0 == strcmp(sim_value, "2"))
            {
                save_nv_flag |= 1;
                switch_sim_card_not_restart(0);
            }
#endif
        }
    }
    /* 切换频段 例如：band8 则下发字符串 8；不支持组合频段，只能下发单个频段的值，设备未校准频段下发无效 */
    cJSON* j_band = cJSON_GetObjectItem(value, "band");
    if(j_band != NULL && cJSON_IsString(j_band))
    {
        // 暂时只支持设置为自动
        if (strcmp(j_band->valuestring, "0") == 0)
        {
            set_lte_net_band(0);
        }
    }

    if (update_wifi_flag != 0)
    {
        save_nv_flag |= 1;
        update_wifi_by_config(&wifi_config);
    }
    if (update_mac_filter_flag > 0)
    {
        save_nv_flag |= 1;
        update_mac_filter_by_config(&mac_filter_config);
    }
    if (save_nv_flag != 0)
    {
        cfg_save();
    }
    if (restart_flag != 0)
    {
        restart_device();
    }
    if (reset_flag != 0)
    {
        reset_device();
    }
    if (shutdown_flag != 0)
    {
        shutdown_device();
    }
}

/**
    一次处理的主函数，发送请求，并根据请求做出相应的处理
 */
static void qicheng_start_process()
{
    int ret;
    char http_response[QRZL_HTTP_RESPONSE_MAX] = {0}; // 存储响应体
    char http_url[4096] = {0};

    update_device_dynamic_data();

    // 构建请求URL
    qicheng_build_request_url(http_url, sizeof(http_url));
    if(strncmp(http_url,"https",5)==0){
        ret = https_send_get_request(http_url, http_response);
    }
    else
    {
        ret =http_send_get_request(http_url, http_response);
    }
 
    if (ret != 0 || strcmp(http_response, "") == 0)
    {
        return;
    }else
    {
        //成功上报后把 qc_first_report_flag 置为0
        qc_first_report_flag = 0;
#ifdef QRZL_CUSTOM_TIANMU_LOGIC
        pre_report_flux_total_bytes = g_qrzl_device_dynamic_data.flux_total_bytes; // 更新上次上报的总流量
#endif
    }
    cJSON *value = cJSON_Parse(http_response);
    if (value == NULL)
    {
        qrzl_err("json value is NULL");
        return;
    }
    // 确保顶层是一个对象
    if (!cJSON_IsObject(value))
    {
        qrzl_err("JSON is not an object.\n");
        cJSON_Delete(value);
        return;
    }

    qicheng_resp_handler(value);

    // 释放 JSON 解析结果
    cJSON_Delete(value);
}
void qicheng_cloud_client_start()
{
    int ret;
    ret = init_config_data();
    if (ret != 0)
    {
        qrzl_err("初始化数据失败，请检查配置是否正确");
        return;
    }
    update_device_static_data();
    while (1) 
    {
        qicheng_start_process();
        sleep(request_interval_time);
    }
    
}

/***===================== 以上都是奇成对接相关处理逻辑  ===========================***/
#endif /* QRZL_HTTP_CLIENT_QICHENG */



/***===================== 以下都是讯技对接相关处理逻辑  ===========================***/
#ifdef QRZL_HTTP_CLIENT_XUNJI

static int xunji_init_config = 0; // 0: 需要获取初始化信息; 1: 已初始化
static char xunji_server_ip[16] = {0};
static int xunji_server_port = 0;
static char xunji_tcp_server_domain[254] = {0};
static int xunji_tcp_server_port = 0;
static int xunji_flow_upload_times = 1800;
#define QRZL_XUNJI_TCP_MAX_MSG_SIZE 1024
static int xunji_heart_beat_times = 180;
#define QRZL_XUNJI_HEART_BEAT_MAX_TIME_OUT 1
static int xunji_heart_beat_flag = 0; // 每发送一个心跳包，此flag加1，当此flag超过QRZL_XUNJI_HEART_BEAT_MAX_TIME_OUT时，说明断网，断开tcp连接
static int xunji_tcp_sockfd;
static int xunji_tcp_socket_connectd = 0;
static short xunji_tcp_seq_num = 0;
static uint64_t xunji_last_send_total_flow = 0L; // 上一次上报设备已使用的流量，初始为0，由当前设备在线后使用的流量获得
static pthread_t xunji_tcp_thread_process_tid;

static int xunji_find_config()
{
    qrzl_log("开始获取远程配置");
    int ret;
    char http_url[256] = {0};
    char http_response[QRZL_HTTP_RESPONSE_MAX] = {0}; // 存储响应体

    snprintf(http_url, sizeof(http_url), "%s%s?sn=%s", http_request_path, "/iotHub/api/device/v1/config/findConfig", g_qrzl_device_static_data.sn);
    ret = http_send_get_request(http_url, http_response);
    if (ret != 0 || strcmp(http_response, "") == 0)
    {
        xunji_init_config = 0;
        return -1;
    }
    cJSON *value = cJSON_Parse(http_response);
    if (value == NULL)
    {
        qrzl_err("json value is NULL");
        return -1;
    }
    // 确保顶层是一个对象
    if (!cJSON_IsObject(value))
    {
        qrzl_err("JSON is not an object.");
        goto free_err;
    }
    cJSON *success = cJSON_GetObjectItem(value, "success");
    if (success)
    {   if (success->type == cJSON_True)
        {
            // success 为 true
        }
        else if (success->type == cJSON_False)
        {
            qrzl_log("success为false");
            goto free_err;
        }
    }

    cJSON *data = cJSON_GetObjectItem(value, "data");
    if (data == NULL || !cJSON_IsObject(data)) {
        qrzl_log("data为空或者错误");
        goto free_err;
    }

    cJSON *http_ip = cJSON_GetObjectItem(data, "httpIp");
    if (http_ip != NULL && cJSON_IsString(http_ip))
    {
        snprintf(xunji_server_ip, sizeof(xunji_server_ip), "%s", http_ip->valuestring);
    }
    cJSON *http_port = cJSON_GetObjectItem(data, "httpPort");
    if (http_port != NULL && cJSON_IsNumber(http_port))
    {
        xunji_server_port = http_port->valueint;
    }

    cJSON *tcp_ip = cJSON_GetObjectItem(data, "tcpIp");
    if (tcp_ip != NULL && cJSON_IsString(tcp_ip))
    {
        // 表面上是IP，其实是域名
        snprintf(xunji_tcp_server_domain, sizeof(xunji_tcp_server_domain), "%s", tcp_ip->valuestring);
    }
    cJSON *tcp_port = cJSON_GetObjectItem(data, "tcpPort");
    if (tcp_port != NULL && cJSON_IsNumber(tcp_port))
    {
        xunji_tcp_server_port = tcp_port->valueint;
    }

    cJSON *heart_beat_times = cJSON_GetObjectItem(data, "heartBeatTimes");
    if (heart_beat_times != NULL && cJSON_IsNumber(heart_beat_times))
    {
        request_interval_time = heart_beat_times->valueint;
        xunji_heart_beat_times = request_interval_time;
    }
    cJSON *flow_upload_times = cJSON_GetObjectItem(data, "flowUploadTimes");
    if (flow_upload_times != NULL && cJSON_IsNumber(flow_upload_times))
    {
        xunji_flow_upload_times = flow_upload_times->valueint;
    }
    xunji_init_config = 1;
    // 释放 JSON 解析结果
    cJSON_Delete(value);
    return 0;

free_err:
    cJSON_Delete(value);
    return -1;
}

static int xunji_upload_device_info()
{
    qrzl_log("开始上报设备状态信息");
    int ret;
    char http_url[256] = {0};
    char http_json_body_str[2048] = {0};
    char http_response[QRZL_HTTP_RESPONSE_MAX] = {0}; // 存储响应体

    snprintf(http_url, sizeof(http_url)-1, "%s:%d%s", xunji_server_ip, xunji_server_port, "/iotHub/api/device/v1/uploadDeviceInfo");
    
    snprintf(http_json_body_str, sizeof(http_json_body_str), "{");
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s\"sn\":\"%s\"", http_json_body_str, g_qrzl_device_static_data.sn);

    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"electric\":\"%s\"", http_json_body_str, g_qrzl_device_dynamic_data.remain_power);
    
    // seedCard start 需要包含所有的卡信息
    snprintf(http_json_body_str, sizeof(http_json_body_str), "%s,\"seedCard\":[", http_json_body_str);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s{\"iccid\":\"%s\"", http_json_body_str, g_qrzl_device_static_data.nvro_esim1_iccid);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"imsi\":\"%s\"", http_json_body_str, g_qrzl_device_static_data.nvro_esim1_imsi);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"networkStatus\":%d", http_json_body_str, g_qrzl_device_dynamic_data.esim1_net_status);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"csq\":\"%d\"", http_json_body_str, get_csq());
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"sequence\":%d}", http_json_body_str, 1);

    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,{\"iccid\":\"%s\"", http_json_body_str, g_qrzl_device_static_data.nvro_esim2_iccid);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"imsi\":\"%s\"", http_json_body_str, g_qrzl_device_static_data.nvro_esim2_imsi);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"networkStatus\":%d", http_json_body_str, g_qrzl_device_dynamic_data.esim2_net_status);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"csq\":\"%d\"", http_json_body_str, get_csq());
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"sequence\":%d}", http_json_body_str, 2);

#ifdef QRZL_HAVE_3_ESIM_CARD
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,{\"iccid\":\"%s\"", http_json_body_str, g_qrzl_device_static_data.nvro_esim3_iccid);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"imsi\":\"%s\"", http_json_body_str, g_qrzl_device_static_data.nvro_esim3_imsi);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"networkStatus\":%d", http_json_body_str, g_qrzl_device_dynamic_data.esim3_net_status);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"csq\":\"%d\"", http_json_body_str, get_csq());
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"sequence\":%d}", http_json_body_str, 3);
#endif

    snprintf(http_json_body_str, sizeof(http_json_body_str), "%s]", http_json_body_str);
    // seedCard end

    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"gsm\":\"%s,%s,%s,%s,%s\"", http_json_body_str,
             g_qrzl_device_dynamic_data.mcc, g_qrzl_device_dynamic_data.mnc, g_qrzl_device_dynamic_data.tac, g_qrzl_device_dynamic_data.cid,
             g_qrzl_device_dynamic_data.rssi);

    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"ssid\":\"%s\"", http_json_body_str, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"wifipwd\":\"%s\"", http_json_body_str, g_qrzl_device_dynamic_data.wifi_key);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"systemVersion\":\"%s\"", http_json_body_str, g_qrzl_device_static_data.soft_version);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"versionCode\":\"%s\"", http_json_body_str, g_qrzl_device_static_data.soft_version);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"wifiMac\":\"%s\"", http_json_body_str, g_qrzl_device_static_data.mac);

    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"seed_card_iccid\":\"%s\"", http_json_body_str, g_qrzl_device_dynamic_data.iccid);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"seed_card_imei\":\"%s\"", http_json_body_str, g_qrzl_device_static_data.imei);
    snprintf(http_json_body_str, sizeof(http_json_body_str),
            "%s,\"seed_card_imsi\":\"%s\"", http_json_body_str, g_qrzl_device_dynamic_data.imsi);

    snprintf(http_json_body_str, sizeof(http_json_body_str), "%s}", http_json_body_str);

    qrzl_log("json_body: %s", http_json_body_str);
    ret = http_send_post_request(http_url, http_json_body_str, http_response);

    if (ret != 0)
    {
        xunji_init_config = 0;
        return -1;
    }
    return 0;
}

static int xunji_update_device_status()
{
    qrzl_log("开始获取要更新设备状态的信息");
    int ret;
    char http_url[256] = {0};
    char http_response[QRZL_HTTP_RESPONSE_MAX] = {0}; // 存储响应体

    snprintf(http_url, sizeof(http_url), "%s:%d%s?sn=%s", xunji_server_ip, xunji_server_port, "/iotHub/api/device/v1/deviceState", g_qrzl_device_static_data.sn);
    ret = http_send_get_request(http_url, http_response);
    if (ret != 0)
    {
        xunji_init_config = 0;
        return -1;
    }
    cJSON *value = cJSON_Parse(http_response);
    if (value == NULL)
    {
        qrzl_err("json value is NULL");
        return -1;
    }
    // 确保顶层是一个对象
    if (!cJSON_IsObject(value))
    {
        qrzl_err("JSON is not an object.");
        goto free_err;
    }

    cJSON *success = cJSON_GetObjectItem(value, "success");
    if (success)
    {   if (success->type == cJSON_True)
        {
            // success 为 true
        }
        else if (success->type == cJSON_False)
        {
            qrzl_log("success为false");
            goto free_err;
        }
    }

    cJSON *data = cJSON_GetObjectItem(value, "data");
    if (data == NULL || !cJSON_IsObject(data)) {
        qrzl_log("data为空或者错误");
        goto free_err;
    }
    
    struct wifi_config_t wifi_config = {};
    init_wifi_config_value(&wifi_config);

    cJSON *open_wifi =  cJSON_GetObjectItem(data, "openWifi");
    if (open_wifi != NULL && cJSON_IsNumber(open_wifi))
    {
#ifndef QRZL_APP_CUSTOMIZATION_MY
        qrzl_log("非MY版本，支持关闭WiFi");
        wifi_config.enable = open_wifi->valueint;
#endif
        // 小半的远程，关闭WiFi的时候，rndis也会关闭
        if (open_wifi->valueint == 0)
        {
            set_network_br0_disconnect(1);
        }
        else if (open_wifi->valueint == 1)
        {
            set_network_br0_disconnect(0);
        }
    }
    cJSON *wifi_name = cJSON_GetObjectItem(data, "wifiName");
    cJSON *wifi_pwd = cJSON_GetObjectItem(data, "wifiPwd");
    if (wifi_name != NULL && wifi_name && strlen(wifi_name->valuestring) > 0)
    {
        // 这里的wifi_name就真的是完整的ssid
        snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", wifi_name->valuestring);
    }
    if (wifi_pwd != NULL && wifi_pwd && strlen(wifi_pwd->valuestring) > 7)
    {
        snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", wifi_pwd->valuestring);
    }
    cJSON *hide_wifi =  cJSON_GetObjectItem(data, "hideWifi");
    if (hide_wifi != NULL && cJSON_IsString(hide_wifi))
    {
        wifi_config.hide = hide_wifi->valueint;
    }
    update_wifi_by_config(&wifi_config);

    cJSON *limit_speed = cJSON_GetObjectItem(data, "limitSpeed");
    uint64_t up_limit = 0L;
    uint64_t down_limit = 0L;
    if (limit_speed != NULL && cJSON_IsNumber(limit_speed))
    {
        cJSON *speed_limit_switch = cJSON_GetObjectItem(limit_speed, "speedLimitSwitch");
        if (speed_limit_switch != NULL && cJSON_IsNumber(speed_limit_switch))
        {
            if (speed_limit_switch->valueint == 1)
            {
                // 讯技的单位是kbps
                cJSON *speed_limit_up_val = cJSON_GetObjectItem(limit_speed, "speedLimitUpVal");
                cJSON *speed_limit_down_val = cJSON_GetObjectItem(limit_speed, "speedLimitDownVal");
                if (speed_limit_up_val != NULL && cJSON_IsNumber(speed_limit_up_val))
                {
                    up_limit = speed_limit_up_val->valueint;
                }
                if (speed_limit_down_val != NULL && cJSON_IsNumber(speed_limit_down_val))
                {
                    down_limit = speed_limit_down_val->valueint;
                }
            }
        }
    }
    limit_net_speed(up_limit, down_limit);
    
#ifndef QRZL_DISABLE_SEEDCARD_MY
    cJSON *j_seed_card_array = cJSON_GetObjectItem(data, "seedCard");
    if (j_seed_card_array != NULL && cJSON_IsArray(j_seed_card_array))
    {
        int array_size = cJSON_GetArraySize(j_seed_card_array);
        qrzl_log("有seedCard，长度为: %d", array_size);
        int i;
        for (i = 0; i < array_size; i++)
        {
            cJSON *j_seed_card = cJSON_GetArrayItem(j_seed_card_array, i);
            if (j_seed_card == NULL)
            {
                break;
            }
            cJSON *j_def_used = cJSON_GetObjectItem(j_seed_card, "defUsed");
            cJSON *j_iccid = cJSON_GetObjectItem(j_seed_card, "iccid");
            if (j_def_used != NULL && j_iccid != NULL && j_iccid->type == cJSON_String)
            {
                if (j_def_used->type == cJSON_False) // 如果这个卡没有设置为默认卡，则跳过这次循环
                {
                    continue;
                }
                cJSON *j_sequence = cJSON_GetObjectItem(j_seed_card, "sequence");
                if (j_sequence != NULL && j_sequence->type == cJSON_Number)
                {
                    // xunji 切卡的序号没有指定的时候0是默认值，并且他的卡序号是1、2、3，没有0
                    if (j_sequence->valueint == 1 && strncmp(g_qrzl_device_static_data.nvro_esim1_iccid, j_iccid->valuestring, strlen(g_qrzl_device_static_data.nvro_esim1_iccid)) == 0)
                    {
                        if (g_qrzl_device_dynamic_data.esim1_net_status == 1)
                        {
                            qrzl_log("esim1 可以上网，准备切卡esim1");
                            switch_sim_card_not_restart(1);
                            sleep(30);
                            xunji_only_reset_tcp();
                        }
                        else
                        {
                            qrzl_log("esim2 不可以上网，不切卡");
                        }
                    }
                    else if (j_sequence->valueint == 2 && strncmp(g_qrzl_device_static_data.nvro_esim2_iccid, j_iccid->valuestring, strlen(g_qrzl_device_static_data.nvro_esim2_iccid)) == 0)
                    {
                        if (g_qrzl_device_dynamic_data.esim2_net_status == 1)
                        {
                            qrzl_log("esim2 可以上网，准备切卡esim2");
                            switch_sim_card_not_restart(2);
                            sleep(30);
                            xunji_only_reset_tcp();
                        }
                        else
                        {
                            qrzl_log("esim2 不可以上网，不切卡");
                        }
                    }
#ifdef QRZL_HAVE_3_ESIM_CARD
                    else if (j_sequence->valueint == 3 && strncmp(g_qrzl_device_static_data.nvro_esim3_iccid, j_iccid->valuestring, strlen(g_qrzl_device_static_data.nvro_esim2_iccid)) == 0) {
                        if (g_qrzl_device_dynamic_data.esim3_net_status == 1)
                        {
                            qrzl_log("esim3 (rsim) 可以上网，准备切卡esim3(rsim)");
                            switch_sim_card_not_restart(0);
                            sleep(30);
                            xunji_only_reset_tcp();
                        }
                        else
                        {
                            qrzl_log("esim3(rsim) 不可以上网，不切卡");
                        }
                    }
#endif
                }
            }
        }
    }
#endif 
    // isActive 和 openWifi是同一个值，不需要再处理
    // cJSON *j_is_active = cJSON_GetObjectItem(data, "isActive");
    // if (j_is_active != NULL && cJSON_IsString(j_is_active))
    // {
    //     if (j_is_active->valueint == 0)
    //     {
    //         set_network_br0_disconnect(1);
    //     }
    //     else if (j_is_active->valueint == 1)
    //     {
    //         set_network_br0_disconnect(0);
    //     }
    // }
    
    cJSON_Delete(value);
    return 0;

free_err:
    cJSON_Delete(value);
    return -1;
}

static int xunji_upload_flow()
{
    qrzl_log("开始流量上报");
    int ret;
    char http_url[2048] = {0};
    char http_response[QRZL_HTTP_RESPONSE_MAX] = {0}; // 存储响应体
    
    snprintf(http_url, sizeof(http_url)-1, "%s:%d%s", xunji_server_ip, xunji_server_port, "/iotHub/api/device/v1/flowUpload");

    snprintf(http_url, sizeof(http_url),
            "%s?device_total_flow=%llu", http_url, g_qrzl_device_dynamic_data.realtime_total_bytes - xunji_last_send_total_flow);
    snprintf(http_url, sizeof(http_url),
            "%s&seed_total_flow=%llu", http_url, g_qrzl_device_dynamic_data.realtime_total_bytes - xunji_last_send_total_flow);
    snprintf(http_url, sizeof(http_url),
            "%s&signal=%s", http_url, g_qrzl_device_dynamic_data.rssi);
    snprintf(http_url, sizeof(http_url),
            "%s&devcie_con_num=%d", http_url, g_qrzl_device_dynamic_data.conn_num);
    snprintf(http_url, sizeof(http_url),
            "%s&isp=%d", http_url, get_isp_by_imsi(g_qrzl_device_dynamic_data.imsi));
    snprintf(http_url, sizeof(http_url),
            "%s&sn=%s", http_url, g_qrzl_device_static_data.sn);
    snprintf(http_url, sizeof(http_url),
            "%s&using_iccid=%s", http_url, g_qrzl_device_dynamic_data.iccid);
    snprintf(http_url, sizeof(http_url),
            "%s&seed_card_iccid=%s", http_url, g_qrzl_device_dynamic_data.iccid);
    snprintf(http_url, sizeof(http_url),
            "%s&electric=%s", http_url, g_qrzl_device_dynamic_data.remain_power);
    snprintf(http_url, sizeof(http_url),
            "%s&gsm=%s,%s,%s,%s,%s,%s", http_url, g_qrzl_device_dynamic_data.mcc, g_qrzl_device_dynamic_data.mnc,
            g_qrzl_device_dynamic_data.tac, g_qrzl_device_dynamic_data.cid, g_qrzl_device_dynamic_data.rssi,g_qrzl_device_dynamic_data.sinr);

    ret = http_send_get_request(http_url, http_response);

    if (ret != 0)
    {
        xunji_init_config = 0;
        return -1;
    }

    // 上报成功才更新上次上报的流量
    xunji_last_send_total_flow = g_qrzl_device_dynamic_data.realtime_total_bytes;
    return 0;
}


// 定义协议的标识位
#define XUNJI_FLAG_START_END 0x7e
#define XUNJI_FLAG_ESCAPE 0x7d

// 将字符转换为8421码并存储在unsigned char数组中 以大端序列存储
void string_to_8421(const char *str, unsigned char *bcd)
{
    int i = 0;  // 字符串的索引
    int j = 0;  // BCD数组的索引

    // 遍历字符串中的每个字符
    while (str[i] != '\0') {
        if (str[i] >= '0' && str[i] <= '9') {
            char digit = str[i] - '0';  // 获取当前字符对应的数字
            if (i % 2 == 0) {
                // 存储偶数位的数字到高4位
                bcd[j] |= (digit << 4);  // 将4位数字存储到高4位
            } else {
                // 存储奇数位的数字到高4位
                bcd[j] |= digit;  // 将4位数字存储到低4位
                j++;  // 如果是第二个数字，移动到下一个字节
            }
        } else {
            qrzl_log("Invalid character in the string: %c", str[i]);
            return;
        }
        i++;
    }
}

// 转义规则处理
void xunji_escape_data(unsigned char *data, size_t *length)
{
    unsigned char temp_data[QRZL_XUNJI_TCP_MAX_MSG_SIZE];
    size_t temp_len = 0;
    int i;
    for (i = 0; i < *length; i++) {
        if (data[i] == XUNJI_FLAG_START_END || data[i] == XUNJI_FLAG_ESCAPE) {
            temp_data[temp_len++] = XUNJI_FLAG_ESCAPE;
            temp_data[temp_len++] = (data[i] == XUNJI_FLAG_START_END) ? 0x02 : 0x01;
        } else {
            temp_data[temp_len++] = data[i];
        }
    }

    memcpy(data, temp_data, temp_len);
    *length = temp_len;
}

// 反转义规则处理
void xunji_unescape_data(unsigned char *data, size_t *length)
{
    unsigned char temp_data[QRZL_XUNJI_TCP_MAX_MSG_SIZE];
    size_t temp_len = 0;
    size_t i;
    for (i = 0; i < *length; i++) {
        if (data[i] == XUNJI_FLAG_ESCAPE) {
            if (i + 1 < *length) {
                // 处理转义后的字节
                if (data[i + 1] == 0x02) {
                    temp_data[temp_len++] = XUNJI_FLAG_START_END; // 还原为 XUNJI_FLAG_START_END
                } else if (data[i + 1] == 0x01) {
                    temp_data[temp_len++] = XUNJI_FLAG_ESCAPE;   // 还原为 XUNJI_FLAG_ESCAPE
                }
                i++;  // 跳过下一个字节
            }
        } else {
            temp_data[temp_len++] = data[i];
        }
    }

    memcpy(data, temp_data, temp_len);
    *length = temp_len;
}

// 校验码计算
unsigned char xunji_calculate_checksum(unsigned char *data, int length)
{
    unsigned char checksum = data[0];
    int i;
    for (i = 1; i < length; i++) {
        checksum ^= data[i];
    }
    return checksum;
}

int xunji_tcp_err_reset();

int xunji_only_reset_tcp();

// 发送消息函数
int xunji_send_message(int sockfd, unsigned short msg_id, const unsigned char *msg_body, unsigned short msg_body_len) 
{
    unsigned char checksum = 0;

    unsigned short msg_body_attr = msg_body_len;
    unsigned char sn_bcd[7] = {0};
    memset(sn_bcd, 0, sizeof(sn_bcd));
    string_to_8421(g_qrzl_device_static_data.sn, sn_bcd);
    unsigned short seq_num = xunji_tcp_seq_num++;
    
    unsigned char not_flag_msg[QRZL_XUNJI_TCP_MAX_MSG_SIZE] = {0};
    size_t msg_len = 0;

    unsigned short net_msg_id = htons(msg_id);
    unsigned short net_msg_body_attr = htons(msg_body_attr);
    unsigned char msg_header_str[13] = {0};
    unsigned char *msg_header_str_p = msg_header_str;

    memcpy(msg_header_str_p, &net_msg_id, sizeof(net_msg_id));
    msg_header_str_p += sizeof(net_msg_id);
    memcpy(msg_header_str_p, &net_msg_body_attr, sizeof(net_msg_body_attr));
    msg_header_str_p += sizeof(net_msg_body_attr);
    memcpy(msg_header_str_p, sn_bcd, sizeof(sn_bcd));
    msg_header_str_p += sizeof(sn_bcd);
    memcpy(msg_header_str_p, &seq_num, sizeof(seq_num));

    if (msg_body_len != 0)
    {
        qrzl_log("消息体不为空\n");
        unsigned char header_body_msg[QRZL_XUNJI_TCP_MAX_MSG_SIZE] = {0};
        u_int16_t header_body_msg_len = sizeof(msg_header_str)+msg_body_len;
        memcpy(header_body_msg, msg_header_str, sizeof(msg_header_str));
        memcpy(header_body_msg+sizeof(msg_header_str), msg_body, msg_body_len);
        checksum = xunji_calculate_checksum(header_body_msg, header_body_msg_len);  // 计算校验码

        memcpy(not_flag_msg, header_body_msg, header_body_msg_len);
        memcpy(not_flag_msg+header_body_msg_len, &checksum, sizeof(checksum));
        msg_len = header_body_msg_len + sizeof(checksum);
    }
    else
    {
        qrzl_log("消息体为空，开始构造消息头\n");
        checksum = xunji_calculate_checksum(msg_header_str, sizeof(msg_header_str)+msg_body_len);  // 计算校验码

        memcpy(not_flag_msg, msg_header_str, sizeof(msg_header_str));
        memcpy(not_flag_msg+sizeof(msg_header_str), &checksum, sizeof(checksum));
        msg_len = sizeof(msg_header_str) + sizeof(checksum);
    }
    
    qrzl_log("消息头和消息体构造完成，准备转义处理");
    xunji_escape_data(not_flag_msg, &msg_len);  // 转义处理
    unsigned char msg[QRZL_XUNJI_TCP_MAX_MSG_SIZE] = {0};
    msg[0] = XUNJI_FLAG_START_END;
    memcpy(msg+1, not_flag_msg, msg_len);
    msg[msg_len+1] = XUNJI_FLAG_START_END;
    msg_len += 2;
    qrzl_log("消息构造完成，准备发送, 长度为: %d", msg_len);
    
    ssize_t sent_bytes = send(sockfd, msg, msg_len, 0);
    qrzl_log("已发送 %ld bytes", sent_bytes);
    if (sent_bytes == -1) 
    {
        qrzl_err("发送失败，错误码: %d", errno);
        qrzl_err("错误描述: %s", strerror(errno));
        xunji_tcp_err_reset();
    }
    return sent_bytes;
}

int xunji_terminal_general_response(u_int16_t platform_msg_seq_num, u_int16_t platform_msg_id, u_int8_t result)
{
    u_int8_t msg_body[5] = {0};
    u_int16_t net_platform_msg_seq_num = htons(platform_msg_seq_num);
    u_int16_t net_platform_msg_id = htons(net_platform_msg_id);
    memcpy(msg_body, &net_platform_msg_seq_num, sizeof(net_platform_msg_seq_num));
    memcpy(msg_body+sizeof(net_platform_msg_seq_num), &net_platform_msg_id, sizeof(net_platform_msg_id));
    memcpy(msg_body+sizeof(net_platform_msg_seq_num)+sizeof(net_platform_msg_id), &result, sizeof(result));
    return xunji_send_message(xunji_tcp_sockfd, 0x0001, msg_body, sizeof(msg_body));
}

void xunji_device_control_handler(unsigned char *body_msg, u_int16_t body_msg_len, u_int16_t platform_msg_seq_num)
{
    u_int16_t msg_control_id;
    u_int8_t *body_msg_now_ptr = body_msg;

    memcpy(&msg_control_id, body_msg_now_ptr, sizeof(msg_control_id));
    msg_control_id = ntohs(msg_control_id);
    body_msg_now_ptr += sizeof(msg_control_id);

    unsigned char control_data[512] = {0};
    body_msg_now_ptr+=2; // 还隔了0x0001 跳过这两个
    u_int16_t control_data_len = body_msg_len-sizeof(msg_control_id)-2;
    memcpy(control_data, body_msg_now_ptr,control_data_len);
    control_data[control_data_len] = '\0';

    qrzl_log("msg_control_id = 0x%04x", msg_control_id);
    qrzl_log("control_data: %s", control_data);

    cJSON *j_value = cJSON_Parse((char*)control_data);
    if (j_value == NULL)
    {
        qrzl_err("json value is NULL");
        return;
    }
    // 确保顶层是一个对象
    if (!cJSON_IsObject(j_value))
    {
        qrzl_err("JSON is not an object.");
        cJSON_Delete(j_value);
        return;
    }
    cJSON *j_cmdid = cJSON_GetObjectItem(j_value, "cmdId");
    if (j_cmdid != NULL && cJSON_IsNumber(j_cmdid))
    {
        if (j_cmdid->valueint == 701)
        {
            qrzl_log("限速命令");
            uint64_t up_limit = 0L; uint64_t down_limit = 0L;
            cJSON *speed_limit_switch = cJSON_GetObjectItem(j_value, "speedLimitSwitch");
            if (speed_limit_switch != NULL && cJSON_IsNumber(speed_limit_switch))
            {
                if (speed_limit_switch->valueint == 1)
                {
                    // 讯技的单位是kbps
                    cJSON *speed_limit_up_val = cJSON_GetObjectItem(j_value, "speedLimitUpVal");
                    cJSON *speed_limit_down_val = cJSON_GetObjectItem(j_value, "speedLimitDownVal");
                    if (speed_limit_up_val != NULL && cJSON_IsNumber(speed_limit_up_val))
                    {
                        up_limit = speed_limit_up_val->valueint;
                    }
                    if (speed_limit_down_val != NULL && cJSON_IsNumber(speed_limit_down_val))
                    {
                        down_limit = speed_limit_down_val->valueint;
                    }
                    
                }
            }
            limit_net_speed(up_limit, down_limit);
        }
        else if (j_cmdid->valueint == 702)
        {
            qrzl_log("关机命令");
            shutdown_device();
        }
        else if (j_cmdid->valueint == 703)
        {
            qrzl_log("重启命令");
            restart_device();
        }
        else if (j_cmdid->valueint == 704)
        {
            qrzl_log("三网切换命令");
        }
        else if (j_cmdid->valueint == 705)
        {
            qrzl_log("WiFi信息修改命令");
            struct wifi_config_t wifi_config = {};
            init_wifi_config_value(&wifi_config);

            cJSON *wifi_name = cJSON_GetObjectItem(j_value, "ssid");
            cJSON *wifi_pwd = cJSON_GetObjectItem(j_value, "pwd");
            if (wifi_name != NULL && wifi_name && strlen(wifi_name->valuestring) > 0)
            {
                // 这里的wifi_name就真的是完整的ssid
                snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", wifi_name->valuestring);
            }
            if (wifi_pwd != NULL && wifi_pwd && strlen(wifi_pwd->valuestring) > 7)
            {
                snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", wifi_pwd->valuestring);
            }
            update_wifi_by_config(&wifi_config);
        }
        else if (j_cmdid->valueint == 706)
        {
            qrzl_log("设备推送升级命令");
        }
        else if (j_cmdid->valueint == 707)
        {
            qrzl_log("恢复出厂设置命令");
            reset_device();
        }
        else if (j_cmdid->valueint == 708)
        {
            qrzl_log("日志采集命令");
            xunji_upload_device_info();
        }
        else if (j_cmdid->valueint == 710)
        {
            qrzl_log("清楚设备缓存命令");
        }
        else if (j_cmdid->valueint == 711)
        {
            qrzl_log("设置白名单命令");
        }
    }

    cJSON_Delete(j_value);
    xunji_terminal_general_response(platform_msg_seq_num, 0x7101, 1);
    
}


// 处理接收到的消息
void xunji_handle_received_message(unsigned char *header_body_msg, int header_body_msg_len) {
    unsigned short msg_id;
    unsigned short msg_body_attr;
    unsigned char sn_bcd[7] = {0};
    unsigned short msg_seq_num;

    unsigned char *header_body_msg_now_ptr = header_body_msg;
    memcpy(&msg_id, header_body_msg_now_ptr, sizeof(msg_id));
    msg_id = ntohs(msg_id);
    header_body_msg_now_ptr += sizeof(msg_id);

    memcpy(&msg_body_attr, header_body_msg_now_ptr, sizeof(msg_body_attr));
    msg_body_attr = ntohs(msg_body_attr);
    header_body_msg_now_ptr += sizeof(msg_body_attr);

    memcpy(sn_bcd, header_body_msg_now_ptr, sizeof(sn_bcd));
    header_body_msg_now_ptr += sizeof(sn_bcd);

    memcpy(&msg_seq_num, header_body_msg_now_ptr, sizeof(msg_seq_num));
    msg_seq_num = ntohs(msg_seq_num);
    header_body_msg_now_ptr += sizeof(msg_seq_num);

    qrzl_log("Received Message ID: 0x%04x", msg_id);
    qrzl_log("Message Body Attribute: 0x%04x", msg_body_attr);
    qrzl_log("Sequence Number: %u", msg_seq_num);

    u_int16_t body_msg_len = msg_body_attr & 1023;
    unsigned char *body_msg = header_body_msg_now_ptr;
    qrzl_log("body_msg_len: %d", body_msg_len);

    // 根据消息ID处理消息体
    switch (msg_id) {
        case 0x0001:  // 终端通用应答
            qrzl_log("Received General Terminal Acknowledgment.");
            break;
        case 0x8001:  // 平台通用应答
            qrzl_log("Received General Platform Acknowledgment.");
            xunji_heart_beat_flag = 0;
            break;
        case 0x0002:  // 终端心跳
            qrzl_log("Received Terminal Heartbeat.");
            break;
        case 0x7101:
            qrzl_log("server msg.");
            xunji_device_control_handler(body_msg, body_msg_len, msg_seq_num);
            break;
        default:
            qrzl_log("Received Unknown Message ID.");
            break;
    }
}

/**
 * 接收消息函数
 * msg: 如果正常获取，那么只有消息头和消息体(解析后的数据)
 * length: 如果正常获取，那么只有消息头和消息体的长度
 */
int xunji_receive_message(int sockfd, unsigned char *msg, int *length)
{
    unsigned char tmp_msg[QRZL_XUNJI_TCP_MAX_MSG_SIZE] = {0};

    int received_len = recv(sockfd, tmp_msg, QRZL_XUNJI_TCP_MAX_MSG_SIZE, 0);

    if (received_len < 0) {
        perror("recvfrom failed");
        return -1;
    }
    // 做简单处理，如果一次收到一个完整的消息就处理，如果不能获取一个完整的消息，就丢掉这个包
    if (tmp_msg[0] != XUNJI_FLAG_START_END || tmp_msg[received_len-1] != XUNJI_FLAG_START_END)
    {
        qrzl_log("消息不完整，直接抛弃");
        return -1;
    }
    unsigned char tmp_not_flag_msg[QRZL_XUNJI_TCP_MAX_MSG_SIZE] = {0};
    size_t tmp_not_flag_msg_len = received_len - 2;
    memcpy(tmp_not_flag_msg, tmp_msg+1, tmp_not_flag_msg_len);
    xunji_unescape_data(tmp_not_flag_msg, &tmp_not_flag_msg_len);

    unsigned char checksum = xunji_calculate_checksum(tmp_not_flag_msg, tmp_not_flag_msg_len-1);
    qrzl_log("checksum: 0x%04x", checksum);
    if (checksum != tmp_not_flag_msg[tmp_not_flag_msg_len-1])
    {
        qrzl_log("校验码错误");
        return -1;
    }
    memcpy(msg, tmp_not_flag_msg, tmp_not_flag_msg_len-1);
    return 0;
}

void xunji_handle_heartbeat(int sig) 
{
    if (xunji_tcp_socket_connectd != 0)
    {
        unsigned char msg[1] = {0};
        if (xunji_heart_beat_flag >= QRZL_XUNJI_HEART_BEAT_MAX_TIME_OUT)
        {
            qrzl_err("发送心跳包长时间没有返回，说明断网了");
            xunji_tcp_err_reset();
        } else {
            qrzl_log("start send heartbeat");
            xunji_send_message(xunji_tcp_sockfd, 0x0002, msg, 0);
            xunji_heart_beat_flag++;
        }
        
    }
    alarm(xunji_heart_beat_times);  // 设置下一个心跳包发送的定时器
}

int xunji_create_socket(const char *hostname, int port_int) {
    struct addrinfo hints, *res;
    int sockfd;

    memset(&hints, 0, sizeof(hints));
    hints.ai_family = AF_INET;          // IPv4
    hints.ai_socktype = SOCK_STREAM;    // TCP
    char port[6] = {0};
    snprintf(port, sizeof(port), "%d", port_int);
    qrzl_log("tcp server hostname: %s, port: %s", hostname, port);

    if (getaddrinfo(hostname, port, &hints, &res) != 0) {
        qrzl_log("getaddrinfo failed");
        return -1;
    }
    qrzl_log("getaddrinfo success");

    sockfd = socket(res->ai_family, res->ai_socktype, res->ai_protocol);
    if (sockfd == -1) {
        qrzl_log("socket creation failed");
        freeaddrinfo(res);
        return -1;
    }
    qrzl_log("socket created");

    if (connect(sockfd, res->ai_addr, res->ai_addrlen) == -1) {
        qrzl_log("connect failed");
        close(sockfd);
        freeaddrinfo(res);
        return -1;
    }
    qrzl_log("socket connected");

    // 设置 SIGALRM 信号处理函数，用于定时发送心跳包
    signal(SIGALRM, xunji_handle_heartbeat);

    // 开始心跳机制
    alarm(10);  // 设置第一个心跳包定时器

    freeaddrinfo(res);
    return sockfd;
}

static void *xunji_tcp_thread_process()
{
    qrzl_log("xunji_tcp_thread_process start");
    while (1)
    {
        if (xunji_init_config != 1)
        {
            sleep(10);
            continue;
        }

        qrzl_log("xunji_init_config find, start create socket");
        xunji_tcp_sockfd = xunji_create_socket(xunji_tcp_server_domain, xunji_tcp_server_port);
        if (xunji_tcp_sockfd == -1)
        {
            qrzl_log("create socket error");
            sleep(10);
            continue;
        }
        xunji_tcp_socket_connectd = 1;

        // 接收回复
        unsigned char recv_msg[QRZL_XUNJI_TCP_MAX_MSG_SIZE] = {0};
        int recv_len = 0;
        while (1)
        {
            memset(recv_msg, 0, sizeof(recv_msg));
            recv_len = 0;
            if (xunji_receive_message(xunji_tcp_sockfd, recv_msg, &recv_len) == 0) {
                // 处理接收到的消息
                xunji_handle_received_message(recv_msg, recv_len);
            }
            if (xunji_tcp_socket_connectd != 1)
            {
                qrzl_log("xunji_tcp_socket 断开连接，不再接收消息");
                break;
            }
        }
    }
    return NULL;
}

int xunji_tcp_err_reset()
{
    qrzl_log("xunji_tcp_err_reset: 关闭socket连接，和tcp线程，并重新开始tcp线程");
    close(xunji_tcp_sockfd);
    pthread_cancel(xunji_tcp_thread_process_tid);
    xunji_tcp_socket_connectd=0;
    xunji_tcp_seq_num=0;
    xunji_heart_beat_flag = 0;
    xunji_init_config = 0;
    int err;
    err = pthread_create(&xunji_tcp_thread_process_tid, NULL, xunji_tcp_thread_process, NULL);
    if (err != 0)
    {
        qrzl_err("creat xunji_tcp_thread_process error, error code: %d", err);
    }
    return 0;
}

int xunji_only_reset_tcp()
{
    qrzl_log("xunji_only_reset_tcp: 关闭socket连接，和tcp线程，并重新开始tcp线程");
    close(xunji_tcp_sockfd);
    pthread_cancel(xunji_tcp_thread_process_tid);
    xunji_tcp_socket_connectd=0;
    xunji_tcp_seq_num=0;
    xunji_heart_beat_flag = 0;
    int err;
    err = pthread_create(&xunji_tcp_thread_process_tid, NULL, xunji_tcp_thread_process, NULL);
    if (err != 0)
    {
        qrzl_err("creat xunji_tcp_thread_process error, error code: %d", err);
    }
    return 0;
}

static void xunji_start_process()
{
    int ret;
    if (xunji_init_config == 0)
    {
        ret = xunji_find_config();
        if (ret != 0)
        {
            return;
        }
        else
        {
            update_device_dynamic_data();
            xunji_update_device_status();
            update_device_dynamic_data();
            xunji_upload_device_info();
            xunji_upload_flow();
        }
    }
}

static void xunji_cloud_client_start()
{
    int ret;
    ret = init_config_data();
    if (ret != 0)
    {
        qrzl_err("初始化数据失败，请检查配置是否正确");
        return;
    }

    update_device_static_data();

    int err;
    err = pthread_create(&xunji_tcp_thread_process_tid, NULL, xunji_tcp_thread_process, NULL);
    if (err != 0)
    {
        qrzl_err("creat xunji_tcp_thread_process error, error code: %d", err);
    }

    int flow_sleep_time = xunji_flow_upload_times;
    while (1)
    {
        xunji_start_process();
        if (flow_sleep_time >= xunji_flow_upload_times)
        {
            if (xunji_init_config == 1)
            {
                update_device_dynamic_data(); 
                xunji_upload_flow();
            }
            flow_sleep_time = 0;
        }
        sleep(15);
        flow_sleep_time += 15;
    }
    
}

/***===================== 以上都是讯技对接相关处理逻辑  ===========================***/
#endif /* QRZL_HTTP_CLIENT_XUNJI */


/***===================== 以下都是JIULING对接相关处理逻辑  ===========================***/
#ifdef QRZL_HTTP_CLIENT_JIULING

// 拼接请求URL和参数
static void jiuling_build_request_url(char *url_buffer, size_t buffer_size) {
    snprintf(url_buffer, buffer_size, "%s", http_request_path);
    snprintf(url_buffer, buffer_size, "%s?sn=%s", url_buffer, g_qrzl_device_static_data.sn);
    snprintf(url_buffer, buffer_size, "%s&imei=%s", url_buffer, g_qrzl_device_static_data.imei);
    snprintf(url_buffer, buffer_size, "%s&iccid=%s", url_buffer, g_qrzl_device_dynamic_data.iccid);
    snprintf(url_buffer, buffer_size, "%s&mac=%s", url_buffer, g_qrzl_device_static_data.mac);
    int main_sim = -1;
    char current_sim_flux[64] = {0};
    if (strcmp("ESIM1_only", g_qrzl_device_dynamic_data.current_sim) == 0)
    {
        main_sim = 1;
        cfg_get_item("esim1_flux_total", current_sim_flux, sizeof(current_sim_flux));
    } 
    else if (strcmp("ESIM2_only", g_qrzl_device_dynamic_data.current_sim) == 0)
    {
        main_sim = 2;
#ifdef QRZL_ESIM2_ON_SIM_SLOT
        cfg_get_item("rsim_flux_total", current_sim_flux, sizeof(current_sim_flux));
#else
        cfg_get_item("esim2_flux_total", current_sim_flux, sizeof(current_sim_flux));
#endif
        
    }
    else if (strcmp("RSIM_only", g_qrzl_device_dynamic_data.current_sim) == 0)
    {
        main_sim = 0;
    }
    // 当前sim卡流量，除非云端下发清空流量统计信息，否则不清零
    uint64_t current_sim_flux_total = strtoull(current_sim_flux, NULL, 10); // 单位byte
    snprintf(url_buffer, buffer_size, "%s&amound_end=%llu", url_buffer, current_sim_flux_total/1024); // 上传单位KB

    char time_string[15] = {0}; // 长度需要能存储 "yyyymmddhhmmss" 和一个 '\0'
    get_local_time("%Y%m%d%H%M%S", time_string, sizeof(time_string));
    snprintf(url_buffer, buffer_size, "%s&current_time=%s", url_buffer, time_string);

    char ssid_urlencode[64 * 3 + 1] = {0}; // 最坏情况下所需要的长度
    url_encode(g_qrzl_device_dynamic_data.wifi_ssid, ssid_urlencode);
    snprintf(url_buffer, buffer_size, "%s&ssid=%s", url_buffer, ssid_urlencode);
    snprintf(url_buffer, buffer_size, "%s&key=%s", url_buffer, g_qrzl_device_dynamic_data.wifi_key_base64);
    snprintf(url_buffer, buffer_size, "%s&hidden=%d", url_buffer, g_qrzl_device_dynamic_data.wifi_enable);
    snprintf(url_buffer, buffer_size, "%s&conn_cnt=%d", url_buffer, g_qrzl_device_dynamic_data.conn_num);

    char lan_ipaddr[16] = {0};
    cfg_get_item("lan_ipaddr", lan_ipaddr, sizeof(lan_ipaddr));
    snprintf(url_buffer, buffer_size, "%s&gateway_ipaddr=%s", url_buffer, lan_ipaddr);

    snprintf(url_buffer, buffer_size, "%s&main_sim=%d", url_buffer, main_sim);
    snprintf(url_buffer, buffer_size, "%s&rssi=%s", url_buffer, g_qrzl_device_dynamic_data.rssi);
    snprintf(url_buffer, buffer_size, "%s&limit_speed=%llu", url_buffer, get_down_limit_net_speed());
    snprintf(url_buffer, buffer_size, "%s&remain_pwr=%s", url_buffer, g_qrzl_device_dynamic_data.remain_power);
    snprintf(url_buffer, buffer_size, "%s&signal=%s", url_buffer, g_qrzl_device_dynamic_data.rssi);
    snprintf(url_buffer, buffer_size, "%s&version=%s", url_buffer, g_qrzl_device_static_data.soft_version);
    snprintf(url_buffer, buffer_size, "%s&lac=%s", url_buffer, g_qrzl_device_dynamic_data.tac);
    snprintf(url_buffer, buffer_size, "%s&cid=%s", url_buffer, g_qrzl_device_dynamic_data.cid);
}

static void jiuling_resp_handler(cJSON *value)
{
    cJSON * j_limit_speed_num = cJSON_GetObjectItem(value, "limitSpeedNum"); 
    if (j_limit_speed_num != NULL && cJSON_IsString(j_limit_speed_num))
    {
        uint64_t limit_speed = strtoull(j_limit_speed_num->valuestring, NULL, 10);
        limit_net_speed(limit_speed, limit_speed);
    }

    cJSON * j_next_rpt_time = cJSON_GetObjectItem(value, "nextRptTime");
    if (j_next_rpt_time != NULL && cJSON_IsString(j_next_rpt_time))
    {
        int next_rpt_time = atoi(j_next_rpt_time->valuestring);
        request_interval_time = next_rpt_time;
    }

    struct wifi_config_t wifi_config = {};
    init_wifi_config_value(&wifi_config);

    cJSON * j_ssid = cJSON_GetObjectItem(value, "ssid");
    if (j_ssid != NULL && j_ssid && strlen(j_ssid->valuestring) > 0)
    {
        snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_ssid->valuestring);
    }

    cJSON * j_key = cJSON_GetObjectItem(value, "key");
    if (j_key != NULL && j_key && strlen(j_key->valuestring) > 7)
    {
        snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_key->valuestring);
    }

    cJSON * j_wifi_hide = cJSON_GetObjectItem(value, "wifiHide");
    if (j_wifi_hide != NULL && cJSON_IsString(j_wifi_hide))
    {
        if (strncmp("0", j_wifi_hide->valuestring, 1) == 0)
        {
            wifi_config.hide = 0;
        }
        else if (strncmp("1", j_wifi_hide->valuestring, 1) == 0)
        {
            wifi_config.hide = 1;
        }
    }

    update_wifi_by_config(&wifi_config);

    cJSON * j_esim = cJSON_GetObjectItem(value, "esim");
    if (j_esim != NULL && cJSON_IsString(j_esim))
    {
        if (strncmp("0", j_esim->valuestring, 1) == 0)
        {
            switch_sim_card_not_restart(0);
        }
        else if (strncmp("1", j_esim->valuestring, 1) == 0)
        {
            switch_sim_card_not_restart(1);
        }
        else if (strncmp("2", j_esim->valuestring, 1) == 0)
        {
            switch_sim_card_not_restart(2);
        }
    }

    cJSON * j_force_reset = cJSON_GetObjectItem(value, "forceReset");
    if (j_force_reset != NULL && cJSON_IsString(j_force_reset))
    {
        if (strcmp("1", j_force_reset->valuestring) == 0)
        {
            reset_device();
            return;
        }
    }

    cJSON * j_force_restart = cJSON_GetObjectItem(value, "forceRestart");
    if (j_force_restart != NULL && cJSON_IsString(j_force_restart))
    {
        if (strcmp("1", j_force_restart->valuestring) == 0)
        {
            restart_device();
            return;
        }
    }

    cJSON * j_clear_flag = cJSON_GetObjectItem(value, "clearflag");
    if (j_clear_flag != NULL && cJSON_IsString(j_clear_flag))
    {
        if (strcmp("1", j_clear_flag->valuestring) == 0)
        {
            cfg_set("esim1_flux_total", "0");
            cfg_set("esim2_flux_total", "0");
            cfg_set("rsim_flux_total", "0");
        }
    }
    
}

/**
    一次处理的主函数，发送请求，并根据请求做出相应的处理
 */
static void jiuling_start_process()
{
    int ret;
    char http_response[QRZL_HTTP_RESPONSE_MAX] = {0}; // 存储响应体
    char http_url[1024] = {0};

    update_device_dynamic_data();

    // 构建请求URL
    jiuling_build_request_url(http_url, sizeof(http_url));
    ret = https_send_get_request(http_url, http_response);
    if (ret != 0 || strcmp(http_response, "") == 0)
    {
        return;
    }
    cJSON *value = cJSON_Parse(http_response);
    if (value == NULL)
    {
        qrzl_err("json value is NULL");
        return;
    }
    // 确保顶层是一个对象
    if (!cJSON_IsObject(value))
    {
        qrzl_err("JSON is not an object.\n");
        cJSON_Delete(value);
        return;
    }

    jiuling_resp_handler(value);

    // 释放 JSON 解析结果
    cJSON_Delete(value);
}

/**
 * 主要用来进入死循环，每隔一段时间开始发送一次http请求，并根据返回值处理
 */
void jiuling_cloud_client_start()
{
    int ret;
    ret = init_config_data();
    if (ret != 0)
    {
        qrzl_err("初始化数据失败，请检查配置是否正确");
        return;
    }
    update_device_static_data();

    while (1) 
    {
        
        jiuling_start_process();
        sleep(request_interval_time);
    }
    
}

/***===================== 以上都是JIULING对接相关处理逻辑  ===========================***/
#endif /* QRZL_HTTP_CLIENT_JIULING */



/***===================== 以下都是XINPU对接相关处理逻辑  ===========================***/
#ifdef QRZL_HTTP_CLIENT_XINPU

// 拼接请求URL和参数
static void xinpu_build_request_url(char *url_buffer, size_t buffer_size) {
    snprintf(url_buffer, buffer_size, "%s", http_request_path);
    snprintf(url_buffer, buffer_size, "%s?imei=%s", url_buffer, g_qrzl_device_static_data.imei);
    snprintf(url_buffer, buffer_size, "%s&iccid=%s", url_buffer, g_qrzl_device_dynamic_data.iccid);
    snprintf(url_buffer, buffer_size, "%s&amountFlow=%llu", url_buffer, g_qrzl_device_dynamic_data.flux_month_total_bytes / 1024);

    char time_string[15] = {0}; // 长度需要能存储 "yyyymmddhhmmss" 和一个 '\0'
    get_local_time("%Y%m%d%H%M%S", time_string, sizeof(time_string));
    snprintf(url_buffer, buffer_size, "%s&currentTime=%s", url_buffer, time_string);
    snprintf(url_buffer, buffer_size, "%s&limitSpeed=%llu", url_buffer, get_down_limit_net_speed());

    char ssid_urlencode[64 * 3 + 1] = {0}; // 最坏情况下所需要的长度
    url_encode(g_qrzl_device_dynamic_data.wifi_ssid, ssid_urlencode);
    snprintf(url_buffer, buffer_size, "%s&ssidName=%s", url_buffer, ssid_urlencode);
    snprintf(url_buffer, buffer_size, "%s&ssidPass=%s", url_buffer, g_qrzl_device_dynamic_data.wifi_key_base64);
    snprintf(url_buffer, buffer_size, "%s&wifiStatus=%d", url_buffer, g_qrzl_device_dynamic_data.wifi_enable);
    int wifi_encrypt = -1;
    if (strncmp("WPA2PSK", g_qrzl_device_dynamic_data.wifi_auth_mode, sizeof(g_qrzl_device_dynamic_data.wifi_auth_mode)) == 0)
    {
        wifi_encrypt = 0;
    }
    else if (strncmp("WPAPSKWPA2PSK", g_qrzl_device_dynamic_data.wifi_auth_mode, sizeof(g_qrzl_device_dynamic_data.wifi_auth_mode)) == 0)
    {
        wifi_encrypt = 1;
    }
    snprintf(url_buffer, buffer_size, "%s&wifiEncrypt=%d", url_buffer, wifi_encrypt);
    snprintf(url_buffer, buffer_size, "%s&wifiStatus5G=%d", url_buffer, 0);
    snprintf(url_buffer, buffer_size, "%s&wifiEncrypt5G=%d", url_buffer, 0);
    snprintf(url_buffer, buffer_size, "%s&ssidName5G=", url_buffer);
    snprintf(url_buffer, buffer_size, "%s&ssidPass5G=", url_buffer);
    snprintf(url_buffer, buffer_size, "%s&connectCount=%d", url_buffer, g_qrzl_device_dynamic_data.conn_num);
    snprintf(url_buffer, buffer_size, "%s&softVersion=%s", url_buffer, g_qrzl_device_static_data.soft_version);

    char web_password_base64[64*4+1] = {0};
    qrzl_base64_encode_safe(g_qrzl_device_dynamic_data.web_password, strlen(g_qrzl_device_dynamic_data.web_password),
        web_password_base64, sizeof(web_password_base64));
    snprintf(url_buffer, buffer_size, "%s&webPassword=%s", url_buffer, web_password_base64);

    int main_sim = -1;
#ifdef QRZL_ESIM2_ON_SIM_SLOT
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0)
    {
        main_sim = 0;
    } 
    else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0)
    {
        main_sim = 1;
    }
#elif QRZL_HAVE_3_ESIM_CARD
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0)
    {
        main_sim = 1;
    } 
    else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0)
    {
        main_sim = 2;
    }
    else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "RSIM_only") == 0)
    {
        main_sim = 0;
    }
#endif
    

    snprintf(url_buffer, buffer_size, "%s&mainSim=%d", url_buffer, main_sim);
    snprintf(url_buffer, buffer_size, "%s&rssi=%s", url_buffer, g_qrzl_device_dynamic_data.rssi);
    snprintf(url_buffer, buffer_size, "%s&dualSim=%d", url_buffer, 1);
    snprintf(url_buffer, buffer_size, "%s&mac=%s", url_buffer, g_qrzl_device_static_data.mac);
    snprintf(url_buffer, buffer_size, "%s&currentIp=%s", url_buffer, g_qrzl_device_dynamic_data.current_wan_ip);
    snprintf(url_buffer, buffer_size, "%s&remainPower=%s", url_buffer, g_qrzl_device_dynamic_data.remain_power);
    snprintf(url_buffer, buffer_size, "%s&mcc=%s", url_buffer, g_qrzl_device_dynamic_data.mcc);
    snprintf(url_buffer, buffer_size, "%s&mnc=%s", url_buffer, g_qrzl_device_dynamic_data.mnc);
    snprintf(url_buffer, buffer_size, "%s&lac=%s", url_buffer, g_qrzl_device_dynamic_data.tac);
    snprintf(url_buffer, buffer_size, "%s&cid=%s", url_buffer, g_qrzl_device_dynamic_data.cid);
    
    char wan_pridns[16] = {0};
    char wan_secdns[16] = {0};
    cfg_get_item("wan1_pridns", wan_pridns, sizeof(wan_pridns));
    cfg_get_item("wan1_secdns", wan_secdns, sizeof(wan_secdns));
    snprintf(url_buffer, buffer_size, "%s&dnsServers=%s,%s", url_buffer, wan_pridns, wan_secdns);
}

static int xinpu_resp_handler(cJSON *value)
{
    int ret = 0;
    cJSON * j_limit_speed_num = cJSON_GetObjectItem(value, "limitSpeed"); 
    if (j_limit_speed_num != NULL && cJSON_IsString(j_limit_speed_num))
    {
        uint64_t limit_speed_num = strtoull(j_limit_speed_num->valuestring, NULL, 10);
        limit_net_speed(limit_speed_num, limit_speed_num);
    }

    cJSON * j_next_report_time = cJSON_GetObjectItem(value, "nextReportTime");
    if (j_next_report_time != NULL && cJSON_IsNumber(j_next_report_time))
    {
        int tmp;
        tmp = atoi(j_next_report_time->valuestring);
        if (tmp > 0)
        {
            request_interval_time = tmp;
        }
    }

    struct wifi_config_t wifi_config = {};
    init_wifi_config_value(&wifi_config);

    cJSON * j_ssid_name = cJSON_GetObjectItem(value, "ssidName");
    if (j_ssid_name != NULL && j_ssid_name && strlen(j_ssid_name->valuestring) > 0)
    {
        snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_ssid_name->valuestring);
    }
    cJSON * j_ssid_pass = cJSON_GetObjectItem(value, "ssidPass");
    if (j_ssid_pass != NULL && j_ssid_pass && strlen(j_ssid_pass->valuestring) > 0)
    {
        snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_ssid_pass->valuestring);
    }
    cJSON * j_wifi_status = cJSON_GetObjectItem(value, "wifiStatus");
    if (j_wifi_status != NULL && cJSON_IsString(j_wifi_status))
    {
        if (strncmp("1", j_wifi_status->valuestring, 1) == 0)
        {
            wifi_config.enable = 1;
        }
        else
        {
            wifi_config.enable = 0;
        }
    }
    cJSON * j_wifi_encrypt = cJSON_GetObjectItem(value, "wifiEncrypt");
    if (j_wifi_encrypt != NULL && cJSON_IsString(j_wifi_encrypt))
    {
        if (strncmp("0", j_wifi_encrypt->valuestring, 1) == 0)
        {
            snprintf(wifi_config.auth_mode, sizeof(wifi_config.auth_mode), "WPA2PSK");
        }
        else if (strncmp("1", j_wifi_encrypt->valuestring, 1) == 0)
        {
            snprintf(wifi_config.auth_mode, sizeof(wifi_config.auth_mode), "WPAPSKWPA2PSK");
        }
    }

    cJSON * j_connect_count = cJSON_GetObjectItem(value, "connectCount");
    if (j_connect_count != NULL && cJSON_IsString(j_connect_count))
    {
        int max_connect = atoi(j_connect_count->valuestring);
        wifi_config.max_access_num = max_connect;
    }

    cJSON * j_wifi_usb_switch = cJSON_GetObjectItem(value, "wifiUsbSwitch");
    if (j_wifi_usb_switch != NULL && cJSON_IsString(j_wifi_usb_switch))
    {
        if (strncmp("1", j_wifi_usb_switch->valuestring, 1) == 0)
        {
            set_network_br0_disconnect(0);
            wifi_config.enable = 1;
        }
        else
        {
            set_network_br0_disconnect(1);
            wifi_config.enable = 0;
        }
    }

    update_wifi_by_config(&wifi_config);

    cJSON * j_force_reset = cJSON_GetObjectItem(value, "forceReset");
    if (j_force_reset != NULL && cJSON_IsString(j_force_reset))
    {
        if (strncmp("1", j_force_reset->valuestring, 1) == 0)
        {
            reset_device();
        }
    }

    cJSON * j_force_restart = cJSON_GetObjectItem(value, "forceRestart");
    if (j_force_restart != NULL && cJSON_IsString(j_force_restart))
    {
        if (strncmp("1", j_force_restart->valuestring, 1) == 0)
        {
            restart_device();
        }
    }

    cJSON * j_web_password = cJSON_GetObjectItem(value, "webPassword");
    if (j_web_password != NULL && j_web_password && strlen(j_web_password->valuestring) > 3)
    {
        update_web_password(j_web_password->valuestring);
    }
    
    cJSON * j_main_sim = cJSON_GetObjectItem(value, "mainSim");
    if (j_main_sim != NULL && cJSON_IsString(j_main_sim))
    {
        if (strncmp("0", j_main_sim->valuestring, 1) == 0)
        {
            switch_sim_card_not_restart(0);
        }
        else if (strncmp("1", j_main_sim->valuestring, 1) == 0)
        {
            switch_sim_card_not_restart(1);
        }
        else if (strncmp("2", j_main_sim->valuestring, 1) == 0)
        {
            switch_sim_card_not_restart(2);
        }
    }

    cJSON * j_dns_servers = cJSON_GetObjectItem(value, "dnsServers");
    if (j_dns_servers != NULL && cJSON_IsString(j_dns_servers))
    {
        char tmp_dns_list[32] = {0};
        strncpy(tmp_dns_list, j_dns_servers->valuestring, sizeof(tmp_dns_list));
        char dns1[16] = {0};
        char dns2[16] = {0};
        // 使用 strtok 函数分割字符串
        char *token = strtok(tmp_dns_list, ",");  // 第一个分割点
        if (token != NULL) {
            strncpy(dns1, token, sizeof(dns1));  // 存储第一个 IP 地址
        }

        token = strtok(NULL, ",");  // 获取下一个分割点
        if (token != NULL) {
            strncpy(dns2, token, sizeof(dns2));  // 存储第二个 IP 地址
        }

        if (valid_ipv4(dns1) && valid_ipv4(dns2))
        {
            qrzl_log("dns1: %s, dns2: %s; 合法dns，准备更新dns", dns1, dns2);
            // 打开文件（"w" 模式会清空文件内容）
            FILE *file = fopen("/etc_rw/dnsmasq.conf", "w");
            if (file == NULL) {
                qrzl_err("无法打开文件/etc_rw/dnsmasq.conf");
                return -1;
            }

            char dnsmasq_conf_content[64] = {0};
            snprintf(dnsmasq_conf_content, sizeof(dnsmasq_conf_content), "nameserver %s\nnameserver %s\n", dns1, dns2);

            // 写入内容到文件
            if (fputs(dnsmasq_conf_content, file) == EOF) {
                qrzl_err("写入文件/etc_rw/dnsmasq.conf失败");
                fclose(file);
                return -1;
            }
            // 关闭文件
            fclose(file);

            cfg_set("wan1_pridns", dns1);
            cfg_set("wan1_secdns", dns2);
            system("killall -9 dnsmasq");
            sleep(1);
            system("dnsmasq -i br0 -r /etc_rw/dnsmasq.conf &");
        }
    }

    return ret;
}

/**
    一次处理的主函数，发送请求，并根据请求做出相应的处理
 */
static void xinpu_start_process()
{
    int ret;
    char http_response[QRZL_HTTP_RESPONSE_MAX] = {0}; // 存储响应体
    char http_url[2048] = {0};

    update_device_dynamic_data();

    // 构建请求URL
    xinpu_build_request_url(http_url, sizeof(http_url));
    ret = http_send_get_request(http_url, http_response);
    if (ret != 0 || strcmp(http_response, "") == 0)
    {
        return;
    }
    cJSON *value = cJSON_Parse(http_response);
    if (value == NULL)
    {
        qrzl_err("json value is NULL");
        return;
    }
    // 确保顶层是一个对象
    if (!cJSON_IsObject(value))
    {
        qrzl_err("JSON is not an object.\n");
        cJSON_Delete(value);
        return;
    }

    xinpu_resp_handler(value);

    // 释放 JSON 解析结果
    cJSON_Delete(value);
}


// 用于临时上报
void *xinpu_cloud_push(void *arg)
{
    char restore_push_message[2] = {0};
    while (1) 
    {
        // 获取XY_restore_push_message， 恢复出厂设置前上报一次的开关
        cfg_get_item("XY_restore_push_message", restore_push_message, 2);
        qrzl_log("XY -> restore_push_message: %s",restore_push_message);
        if(strcmp("1", restore_push_message) == 0){
            xinpu_start_process();
            qrzl_log("XY -> xinpu_cloud_push ok!");
            return NULL;
        }
        sleep(3);
    }
}

/**
 * 主要用来进入死循环，每隔一段时间开始发送一次http请求，并根据返回值处理
 */
void xinpu_cloud_client_start()
{
    int ret;
    ret = init_config_data();
    if (ret != 0)
    {
        qrzl_err("初始化数据失败，请检查配置是否正确");
        return;
    }

    update_device_static_data();

#ifdef QRZL_DEVICE_RESTORE_PUSH
    // 监听是否恢复出厂设置，进行上报
    pthread_t t1;
    pthread_create(&t1, NULL, xinpu_cloud_push, NULL);
    pthread_detach(t1); // 避免资源泄露
#endif

    while (1) 
    {
        xinpu_start_process();
        sleep(request_interval_time);
    }
    
}

/***===================== 以上都是XINPU对接相关处理逻辑  ===========================***/
#endif /* QRZL_HTTP_CLIENT_XINPU */

/***===================== BOMING对接相关处理逻辑 start ===========================***/
#ifdef QRZL_HTTP_CLIENT_BOMING

#define BYTES_PER_MB (1024 * 1024)
/** 首次开机上报标识 */
static int reboot_push_flag = 1;
// 上一次上报设备已使用的流量，初始为0，由当前设备在线后使用的流量获得
static uint64_t boming_last_send_total_flow = 0L; 

// 重启会重置该值
static int master_sim_id_tmp = 0;
static int sole_sim_id_tmp = 0;

static int boming_reporting_device_info(char *http_response)
{
    qrzl_log("BOMING 开始上报设备状态信息...");
    
    cJSON *root = cJSON_CreateObject();
    char temp[128];

    snprintf(temp, sizeof(temp), "%d", reboot_push_flag);
    cJSON_AddStringToObject(root, "bootup_sync", temp);
    cJSON_AddStringToObject(root, "sn", g_qrzl_device_static_data.sn);
    cJSON_AddStringToObject(root, "imei", g_qrzl_device_static_data.imei);
    cJSON_AddStringToObject(root, "imsi", g_qrzl_device_dynamic_data.imsi);
    cJSON_AddStringToObject(root, "iccid", g_qrzl_device_dynamic_data.iccid);
    cJSON_AddStringToObject(root, "mcc", g_qrzl_device_dynamic_data.mcc);
    cJSON_AddStringToObject(root, "mnc", g_qrzl_device_dynamic_data.mnc);
    cJSON_AddStringToObject(root, "web_account", "admin");
    cJSON_AddStringToObject(root, "web_password", g_qrzl_device_dynamic_data.web_password);
    cJSON_AddStringToObject(root, "software_version", g_qrzl_device_static_data.soft_version);
    cJSON_AddStringToObject(root, "ssid", g_qrzl_device_dynamic_data.wifi_ssid);
    cJSON_AddStringToObject(root, "password", g_qrzl_device_dynamic_data.wifi_key);

    snprintf(temp, sizeof(temp), "%d", g_qrzl_device_dynamic_data.wifi_enable);
    cJSON_AddStringToObject(root, "wifi_enabled", temp);
    snprintf(temp, sizeof(temp), "%d", g_qrzl_device_dynamic_data.wifi_hide);
    cJSON_AddStringToObject(root, "broadcast_status", temp);
    cJSON_AddStringToObject(root, "encrypt_type", g_qrzl_device_dynamic_data.wifi_auth_mode);
    snprintf(temp, sizeof(temp), "%d", g_qrzl_device_dynamic_data.max_access_num);
    cJSON_AddStringToObject(root, "max_clients", temp);
    snprintf(temp, sizeof(temp), "%d", g_qrzl_device_dynamic_data.conn_num);
    cJSON_AddStringToObject(root, "client_number", temp);
    snprintf(temp, sizeof(temp), "%llu", g_qrzl_device_dynamic_data.up_speed_bps / 1024);
    cJSON_AddStringToObject(root, "upload_speed", temp);
    snprintf(temp, sizeof(temp), "%llu", g_qrzl_device_dynamic_data.down_speed_bps / 1024);
    cJSON_AddStringToObject(root, "download_speed", temp);
    snprintf(temp, sizeof(temp), "%llu", g_qrzl_device_dynamic_data.realtime_tx_bytes / BYTES_PER_MB);
    cJSON_AddStringToObject(root, "ul_stats", temp);
    snprintf(temp, sizeof(temp), "%llu", g_qrzl_device_dynamic_data.realtime_rx_bytes / BYTES_PER_MB);
    cJSON_AddStringToObject(root, "dl_stats", temp);

    uint64_t byte_diff = 0;
    if (g_qrzl_device_dynamic_data.realtime_total_bytes >= boming_last_send_total_flow) {
        byte_diff = g_qrzl_device_dynamic_data.realtime_total_bytes - boming_last_send_total_flow;
    } else {
        // 说明设备可能重启或统计数据被清空了，直接取当前值
        byte_diff = g_qrzl_device_dynamic_data.realtime_total_bytes;
    }
    uint64_t increment = (byte_diff + BYTES_PER_MB / 2) / BYTES_PER_MB;
    snprintf(temp, sizeof(temp), "%llu", increment);
    cJSON_AddStringToObject(root, "uldl_increment", temp);

    snprintf(temp, sizeof(temp), "%llu", get_down_limit_net_speed());
    cJSON_AddStringToObject(root, "limit_speed", temp);
    snprintf(temp, sizeof(temp), "%llu", g_qrzl_device_dynamic_data.flux_day_total_bytes / BYTES_PER_MB);
    cJSON_AddStringToObject(root, "daily_usage", temp);
    cJSON_AddStringToObject(root, "rsrp", g_qrzl_device_dynamic_data.lte_rsrp);
    cJSON_AddStringToObject(root, "rsrq", g_qrzl_device_dynamic_data.rsrq);
    cJSON_AddStringToObject(root, "sinr", g_qrzl_device_dynamic_data.sinr);
    cJSON_AddStringToObject(root, "rssi", g_qrzl_device_dynamic_data.rssi);
    cJSON_AddStringToObject(root, "eci", "0");
    cJSON_AddStringToObject(root, "pci", g_qrzl_device_dynamic_data.pci);
    cJSON_AddStringToObject(root, "tac", g_qrzl_device_dynamic_data.tac);
    cJSON_AddStringToObject(root, "band", g_qrzl_device_dynamic_data.net_band);
    cJSON_AddStringToObject(root, "lan_ip", g_qrzl_device_dynamic_data.lan_ipaddr);
    cJSON_AddStringToObject(root, "wan_ip", g_qrzl_device_dynamic_data.current_wan_ip);

    snprintf(temp, sizeof(temp), "%d", get_remain_power());
    cJSON_AddStringToObject(root, "battery", temp);
    
    double uptime_seconds = get_device_uptime();
    int run_seconds = (int)uptime_seconds; 

    snprintf(temp, sizeof(temp), "%d", run_seconds);
    cJSON_AddStringToObject(root, "run_time", temp);

    char connect_time_str[124] = {0};
    connect_net_time(connect_time_str, sizeof(connect_time_str));

    cJSON_AddStringToObject(root, "connect_time", connect_time_str);
    snprintf(temp, sizeof(temp), "%d", request_interval_time);
    cJSON_AddStringToObject(root, "sync_interval", temp);
    snprintf(temp, sizeof(temp), "%d", run_seconds);

    time_t now = time(NULL);
    char ts_str[32] = {0};
    snprintf(ts_str, sizeof(ts_str), "%ld", now);
    cJSON_AddStringToObject(root, "timestamp", ts_str);

    // multi_sim 构建
    cJSON *multi_sim = cJSON_CreateObject();
    snprintf(temp, sizeof(temp), "%d", g_qrzl_device_dynamic_data.auto_switch_esim_type ? 0 : 1);
    cJSON_AddStringToObject(multi_sim, "switch_mode", temp);
    int sim_slot = (get_device_current_sim_index_by_data() == 0) ? 2 : ((get_device_current_sim_index_by_data() == 1) ? 0 : 1);
    snprintf(temp, sizeof(temp), "%d", sim_slot);
    cJSON_AddStringToObject(multi_sim, "current_sim_id", temp);
    snprintf(temp, sizeof(temp), "%d", master_sim_id_tmp);
    cJSON_AddStringToObject(multi_sim, "master_sim_id", temp);
    snprintf(temp, sizeof(temp), "%d", sole_sim_id_tmp);
    cJSON_AddStringToObject(multi_sim, "sole_sim_id", temp);
    cJSON_AddStringToObject(multi_sim, "virtual_iccid", g_qrzl_device_dynamic_data.iccid);

    cJSON *sim_list = cJSON_CreateArray();
    
    cJSON *sim1 = cJSON_CreateObject();
    cJSON_AddStringToObject(sim1, "sim_id", "0");
    cJSON_AddStringToObject(sim1, "sim_name", "eSIM1");
    cJSON_AddStringToObject(sim1, "sim_type", "1");
    cJSON_AddStringToObject(sim1, "sim_present", "1");
    cJSON_AddStringToObject(sim1, "sim_banned", g_qrzl_device_dynamic_data.slot_esim1_is_enable == 1 ? "0":"1");
    cJSON_AddStringToObject(sim1, "sim_iccid", g_qrzl_device_static_data.nvro_esim1_iccid);
    cJSON_AddStringToObject(sim1, "sim_imsi", g_qrzl_device_static_data.nvro_esim1_imsi);
    cJSON_AddItemToArray(sim_list, sim1);

    cJSON *sim2 = cJSON_CreateObject();
    cJSON_AddStringToObject(sim2, "sim_id", "1");
    cJSON_AddStringToObject(sim2, "sim_name", "eSIM2");
    cJSON_AddStringToObject(sim2, "sim_type", "1");
    cJSON_AddStringToObject(sim2, "sim_present", "1");
    cJSON_AddStringToObject(sim2, "sim_banned", g_qrzl_device_dynamic_data.slot_esim2_is_enable == 1 ? "0":"1");
    cJSON_AddStringToObject(sim2, "sim_iccid", g_qrzl_device_static_data.nvro_esim2_iccid);
    cJSON_AddStringToObject(sim2, "sim_imsi", g_qrzl_device_static_data.nvro_esim2_imsi);
    cJSON_AddItemToArray(sim_list, sim2);

#ifdef QRZL_HAVE_3_ESIM_CARD
    cJSON *sim3 = cJSON_CreateObject();
    cJSON_AddStringToObject(sim3, "sim_id", "2");
    cJSON_AddStringToObject(sim3, "sim_name", "USIM");
    cJSON_AddStringToObject(sim3, "sim_type", "2");
    cJSON_AddStringToObject(sim3, "sim_present", "0");
    cJSON_AddStringToObject(sim3, "sim_banned", g_qrzl_device_dynamic_data.slot_esim3_is_enable == 1 ? "0":"1");
    cJSON_AddStringToObject(sim3, "sim_iccid", g_qrzl_device_static_data.nvro_esim3_iccid);
    cJSON_AddStringToObject(sim3, "sim_imsi", g_qrzl_device_static_data.nvro_esim3_imsi);
    cJSON_AddItemToArray(sim_list, sim3);
#endif

    cJSON_AddItemToObject(multi_sim, "sim_list", sim_list);
    cJSON_AddItemToObject(root, "multi_sim", multi_sim);

    char *json_str = cJSON_PrintUnformatted(root);

    printf("\nJSON Body => %s\n\n", json_str);

    int ret;

    ret = http_send_post_request(http_request_path, json_str, http_response);

    qrzl_log("http_response 结果：%s", http_response);

    free(json_str); // 手动释放内部申请的内存
    cJSON_Delete(root);

    if (ret != 0)
    {
        return -1;
    }

    // 上报成功才更新上次上报的流量
    boming_last_send_total_flow = g_qrzl_device_dynamic_data.realtime_total_bytes;
    qrzl_log("boming_last_send_total_flow 结果：%llu", boming_last_send_total_flow);

    return 0;
}

static void send_http_response(char *json_str)
{
    // 防止切卡导致没网
    sleep(5);
    int i;
    for (i = 0; i < 3; i++) {
        if (check_network() == 0) {
            break;
        }
    }
    
    char send_results_response[QRZL_HTTP_RESPONSE_MAX] = {0}; // 存储响应体
    
    // 发送POST请求，响应执行结果
    int res = http_send_post_request(http_request_path, json_str, send_results_response);
    if (res != 0) {
        qrzl_log("响应执行结果，请求失败");
    }
}

// 处理单条指令的函数
static cJSON* process_single_command(cJSON *cmd_value)
{
    cJSON *item = cJSON_CreateObject();
    int result_code = 0;
    
    // 获取基本字段
    cJSON * json_action = cJSON_GetObjectItem(cmd_value, "action");
    cJSON * json_imei = cJSON_GetObjectItem(cmd_value, "imei");
    
    if (json_action == NULL || !cJSON_IsString(json_action)) {
        qrzl_log("action字段无效");
        result_code = 1;
        cJSON_AddStringToObject(item, "error_code", "1");
        return item;
    }
    
    if (json_imei == NULL || !cJSON_IsString(json_imei)) {
        qrzl_log("imei字段无效");
        result_code = 1;
        cJSON_AddStringToObject(item, "error_code", "1");
        return item;
    }
    
    char *action_str = json_action->valuestring;
    char *imei_str = json_imei->valuestring;
    
    // 添加基本字段到返回结果
    cJSON_AddStringToObject(item, "action", action_str);
    cJSON_AddStringToObject(item, "imei", imei_str);
    
    qrzl_log("处理指令 action=%s, imei=%s", action_str, imei_str);
    
    // 根据action执行相应的操作
    if (strcmp("1", action_str) == 0) {
        qrzl_log("执行重启指令");
        result_code = restart_device();
        
    } else if (strcmp("2", action_str) == 0) {
        qrzl_log("执行恢复出厂设置指令");
        result_code = reset_device();
        
    } else if (strcmp("3", action_str) == 0) {
        qrzl_log("执行设备关机指令");
        result_code = shutdown_device();
        
    } else if (strcmp("4", action_str) == 0) {
        qrzl_log("执行设备限速指令");
        cJSON * json_limit_speed = cJSON_GetObjectItem(cmd_value, "limit_speed");
        if (json_limit_speed != NULL && cJSON_IsString(json_limit_speed)) {
            uint64_t limit_speed_num = strtoull(json_limit_speed->valuestring, NULL, 10);
            result_code = limit_net_speed(limit_speed_num, limit_speed_num);
            // 添加limit_speed参数到返回结果
            cJSON_AddStringToObject(item, "limit_speed", json_limit_speed->valuestring);
        } else {
            qrzl_log("limit_speed参数无效");
            result_code = 1;
        }
        
    } else if (strcmp("5", action_str) == 0) {
        qrzl_log("执行修改上报周期指令");
        cJSON * json_sync_interval = cJSON_GetObjectItem(cmd_value, "sync_interval");
        if (json_sync_interval != NULL && cJSON_IsString(json_sync_interval)) {
            int sync_interval_num = atoi(json_sync_interval->valuestring);
            if (sync_interval_num < 10 || sync_interval_num > 65535) {
                qrzl_log("sync_interval超出范围(10-65535)");
                result_code = 1;
            } else {
                request_interval_time = sync_interval_num;
                cfg_set(NV_QRZL_CLOUD_REQUEST_INTERVAL_TIME, json_sync_interval->valuestring);
                // 添加sync_interval参数到返回结果
                cJSON_AddStringToObject(item, "sync_interval", json_sync_interval->valuestring);
            }
        } else {
            qrzl_log("sync_interval参数无效");
            result_code = 1;
        }
        
    } else if (strcmp("21", action_str) == 0) {
        qrzl_log("执行WiFi更新指令");
        cJSON * json_ssid = cJSON_GetObjectItem(cmd_value, "ssid");
        cJSON * json_password = cJSON_GetObjectItem(cmd_value, "password");
        if (json_ssid != NULL && cJSON_IsString(json_ssid) && json_password != NULL && cJSON_IsString(json_password)) {
            // 初始化 WIFI 结构体，用于更新
            struct wifi_config_t wifi_config = {};
            init_wifi_config_value(&wifi_config);
            // 更新wifi信息
            snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", json_ssid->valuestring);
            snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", json_password->valuestring);
            int update_wifi_relust = update_wifi_by_config(&wifi_config);
            if (update_wifi_relust != 0) {
                result_code = 1;
            }
            cJSON_AddStringToObject(item, "ssid", json_ssid->valuestring);
            cJSON_AddStringToObject(item, "password", json_password->valuestring);
        } else {
            qrzl_log("WiFi参数无效");
            result_code = 1;
        }
        
    } else if (strcmp("22", action_str) == 0) {
        qrzl_log("执行打开或关闭wifi指令");
        cJSON * json_wifi_enabled = cJSON_GetObjectItem(cmd_value, "wifi_enabled");
        if (json_wifi_enabled != NULL && cJSON_IsString(json_wifi_enabled)) {
            // 初始化 WIFI 结构体，用于更新
            struct wifi_config_t wifi_config = {};
            init_wifi_config_value(&wifi_config);
            // 更新wifi信息
            wifi_config.enable = atoi(json_wifi_enabled->valuestring);
            int update_wifi_relust = update_wifi_by_config(&wifi_config);
            if (update_wifi_relust != 0) {
                result_code = 1;
            }
            // 添加wifi_enabled参数到返回结果
            cJSON_AddStringToObject(item, "wifi_enabled", json_wifi_enabled->valuestring);
        } else {
            qrzl_log("WiFi参数无效");
            result_code = 1;
        }
        
    } else if (strcmp("23", action_str) == 0) {
        qrzl_log("执行隐藏或广播 wifi 热点指令");
        cJSON * json_broadcast_status = cJSON_GetObjectItem(cmd_value, "broadcast_status");
        if (json_broadcast_status != NULL && cJSON_IsString(json_broadcast_status)) {
            // 初始化 WIFI 结构体，用于更新
            struct wifi_config_t wifi_config = {};
            init_wifi_config_value(&wifi_config);
            // 更新wifi信息
            wifi_config.hide = atoi(json_broadcast_status->valuestring);
            int update_wifi_relust = update_wifi_by_config(&wifi_config);
            if (update_wifi_relust != 0) {
                result_code = 1;
            }
            // 添加broadcast_status参数到返回结果
            cJSON_AddStringToObject(item, "broadcast_status", json_broadcast_status->valuestring);
        } else {
            qrzl_log("WiFi参数无效");
            result_code = 1;
        }
        
    } else if (strcmp("24", action_str) == 0) {
        qrzl_log("执行设置 wifi 加密模式指令");
        cJSON * json_encrypt_type = cJSON_GetObjectItem(cmd_value, "encrypt_type");
        if (json_encrypt_type != NULL && cJSON_IsString(json_encrypt_type)) {
            // 初始化 WIFI 结构体，用于更新
            struct wifi_config_t wifi_config = {};
            init_wifi_config_value(&wifi_config);
            // 更新wifi信息
            snprintf(wifi_config.auth_mode, sizeof(wifi_config.auth_mode), "%s", json_encrypt_type->valuestring);
            int update_wifi_relust = update_wifi_by_config(&wifi_config);
            if (update_wifi_relust != 0) {
                result_code = 1;
            }
            // 添加encrypt_type参数到返回结果
            cJSON_AddStringToObject(item, "encrypt_type", json_encrypt_type->valuestring);
        } else {
            qrzl_log("WiFi参数无效");
            result_code = 1;
        }
        
    } else if (strcmp("25", action_str) == 0) {
        qrzl_log("执行设置WiFi最大连接数指令");
        cJSON * json_max_clients = cJSON_GetObjectItem(cmd_value, "max_clients");
        if (json_max_clients != NULL && cJSON_IsString(json_max_clients)) {
            int max_clients_num = atoi(json_max_clients->valuestring);
            if (max_clients_num < 1 || max_clients_num > 10) {
                qrzl_log("max_clients超出范围(1-32)");
                result_code = 1;
            } else {
                // 初始化 WIFI 结构体，用于更新
                struct wifi_config_t wifi_config = {};
                init_wifi_config_value(&wifi_config);
                // 更新wifi信息
                wifi_config.max_access_num = max_clients_num;
                int update_wifi_relust = update_wifi_by_config(&wifi_config);
                if (update_wifi_relust != 0) {
                    result_code = 1;
                }
            }
            // 添加max_clients参数到返回结果
            cJSON_AddStringToObject(item, "max_clients", json_max_clients->valuestring);
        } else {
            qrzl_log("max_clients参数无效");
            result_code = 1;
        }
        
    } else if (strcmp("81", action_str) == 0) {
        qrzl_log("设置最优先使用 SIM 卡");
        cJSON * json_master_sim_id = cJSON_GetObjectItem(cmd_value, "master_sim_id");
        if (json_master_sim_id != NULL && cJSON_IsString(json_master_sim_id)) {
            int master_sim_id = atoi(json_master_sim_id->valuestring);
            master_sim_id_tmp = master_sim_id;
            // 获取当前的切卡模式 0: 手动， 1: 自动
            char switch_type[2] = {0};
            cfg_get_item("auto_switch_esim_type", switch_type, sizeof(switch_type));
            qrzl_log("当前的切卡模式 (0: 手动， 1: 自动): %s", switch_type);
            // 手动挡切自动挡
            if (strcmp("0", switch_type) == 0) {
                qrzl_log("切换至智能寻网");
                cfg_set("auto_switch_esim_type", "1");
            }
            // 切卡 “0”代表 eSIM1，“1”代表 eSIM2,“2”代表 USIM；
            switch (master_sim_id)
            {
            case 0:
                switch_sim_card_not_restart(1);
                break;
            case 1:
                switch_sim_card_not_restart(2);
                break;
            case 2:
                switch_sim_card_not_restart(0);
                break;
            default:
                qrzl_log("没有此卡号");
                result_code = 1;
                break;
            }
        }
            // 添加max_clients参数到返回结果
            cJSON_AddStringToObject(item, "master_sim_id", json_master_sim_id->valuestring);
    } else if (strcmp("82", action_str) == 0) {
        qrzl_log("设置手动模式下指定使用 SIM 卡");
        cJSON * json_sole_sim_id = cJSON_GetObjectItem(cmd_value, "sole_sim_id");
        if (json_sole_sim_id != NULL && cJSON_IsString(json_sole_sim_id)) {
            int sole_sim_id = atoi(json_sole_sim_id->valuestring);
            // 获取当前的切卡模式 0: 手动， 1: 自动
            char switch_type[2] = {0};
            cfg_get_item("auto_switch_esim_type", switch_type, sizeof(switch_type));
            qrzl_log("当前的切卡模式 (0: 手动， 1: 自动): %s", switch_type);
            // 自动挡切手动挡
            if (strcmp("1", switch_type) == 0) {
                qrzl_log("切换至手动寻网");
                cfg_set("auto_switch_esim_type", "0");
            }
            // 切卡 “0”代表 eSIM1，“1”代表 eSIM2,“2”代表 USIM；
            switch (sole_sim_id)
            {
            case 0:
                switch_sim_card_not_restart(1);
                break;
            case 1:
                switch_sim_card_not_restart(2);
                break;
            case 2:
                switch_sim_card_not_restart(0);
                break;
            default:
                qrzl_log("没有此卡号");
                result_code = 1;
                break;
            }
            // 添加max_clients参数到返回结果
            cJSON_AddStringToObject(item, "sole_sim_id", json_sole_sim_id->valuestring);
        } else {
            qrzl_log("sole_sim_id参数无效");
            result_code = 1;
        }
    
    } else if (strcmp("83", action_str) == 0) {
        qrzl_log("设置切卡模式");
        cJSON * json_switch_mode = cJSON_GetObjectItem(cmd_value, "switch_mode");
        if (json_switch_mode != NULL && cJSON_IsString(json_switch_mode)) {
            // 获取当前的切卡模式 0: 手动， 1: 自动
            char switch_type[2] = {0};
            cfg_get_item("auto_switch_esim_type", switch_type, sizeof(switch_type));
            qrzl_log("当前的切卡模式 (0: 手动， 1: 自动): %s", switch_type);
            // 切换切卡模式
            if (strcmp(json_switch_mode->valuestring, switch_type) != 0) {
                cfg_set("auto_switch_esim_type", json_switch_mode->valuestring);

                // 【设置手动模式时需同时指定使用 SIM 卡】
                if (strcmp(json_switch_mode->valuestring, "0") == 0) {
                    // 获取指定卡号
                    cJSON * json_sole_sim_id = cJSON_GetObjectItem(cmd_value, "sole_sim_id");
                    if (json_sole_sim_id != NULL && cJSON_IsString(json_sole_sim_id)) {
                        // 切卡 “0”代表 eSIM1，“1”代表 eSIM2,“2”代表 USIM；
                        switch (atoi(json_sole_sim_id->valuestring))
                        {
                        case 0:
                            switch_sim_card_not_restart(1);
                            break;
                        case 1:
                            switch_sim_card_not_restart(2);
                            break;
                        case 2:
                            switch_sim_card_not_restart(0);
                            break;
                        default:
                            qrzl_log("没有此卡号");
                            result_code = 1;
                            break;
                        }
                        cJSON_AddStringToObject(item, "sole_sim_id", json_sole_sim_id->valuestring);
                    } else {
                        qrzl_log("sole_sim_id 值类型异常");
                        result_code = 1;
                    }
                }
            }
            // 添加max_clients参数到返回结果
            cJSON_AddStringToObject(item, "switch_mode", json_switch_mode->valuestring);
        } else {
            qrzl_log("switch_mode参数无效");
            result_code = 1;
        }
    
    } else if (strcmp("84", action_str) == 0) {
        qrzl_log("设置 SIM 卡禁用");
        cJSON * json_sim_id = cJSON_GetObjectItem(cmd_value, "sim_id");
        if (json_sim_id != NULL && cJSON_IsString(json_sim_id)) {
            // 获取sim_id数组长度
            uint8_t sim_len = cJSON_GetArraySize(json_sim_id);
            int i;
            for (i = 0; i < sim_len; i++)
            {
                cJSON * item = cJSON_GetArrayItem(json_sim_id, i);
                if (item && cJSON_IsString(item)) {
                    qrzl_log("sim_id-> i = %d, sim_id = %s", i, item->valuestring);
                    // TODO 获取当前使用的卡号，如果禁用当前使用的卡号则不允许

                    set_slot_state(0, atoi(item->valuestring));
                } else {
                    qrzl_log("sim_id 类型错误");
                    result_code = 1;
                }
            }
            
            // 添加max_clients参数到返回结果
            cJSON_AddStringToObject(item, "sim_id", json_sim_id->valuestring);
        } else {
            qrzl_log("sim_id参数无效");
            result_code = 1;
        }
    
    } else if (strcmp("85", action_str) == 0) {
        qrzl_log("设置 SIM 卡解禁");
        cJSON * json_sim_id = cJSON_GetObjectItem(cmd_value, "sim_id");
        if (json_sim_id != NULL && cJSON_IsString(json_sim_id)) {
            // 获取sim_id数组长度
            uint8_t sim_len = cJSON_GetArraySize(json_sim_id);
            int i;
            for (i = 0; i < sim_len; i++)
            {
                cJSON * item = cJSON_GetArrayItem(json_sim_id, i);
                if (item && cJSON_IsString(item)) {
                    qrzl_log("sim_id-> i = %d, sim_id = %s", i, item->valuestring);
                    set_slot_state(1, atoi(item->valuestring));
                } else {
                    qrzl_log("sim_id 类型错误");
                    result_code = 1;
                }
            }
            
            // 添加max_clients参数到返回结果
            cJSON_AddStringToObject(item, "sim_id", json_sim_id->valuestring);
        } else {
            qrzl_log("sim_id参数无效");
            result_code = 1;
        }
    
    } else {
        qrzl_log("未知的action: %s", action_str);
        result_code = 1;
    }
    
    // 添加执行结果
    char result_code_str[2];
    snprintf(result_code_str, sizeof(result_code_str), "%d", result_code);
    cJSON_AddStringToObject(item, "error_code", result_code_str);
    
    return item;
}

static void boming_resp_handler(cJSON *value)
{
    // 检查是否为多条指令格式
    cJSON * action_list = cJSON_GetObjectItem(value, "action_list");
    
    if (action_list != NULL && cJSON_IsString(action_list)) {
        // 处理多条指令
        qrzl_log("处理多条指令，长度为: %d", cJSON_GetArraySize(action_list));
        
        // 创建返回的JSON结构
        cJSON *root = cJSON_CreateObject();
        cJSON *response_array = cJSON_CreateArray();
        
        int i;
        for (i = 0; i < cJSON_GetArraySize(action_list); i++) {
            cJSON * cmd_item = cJSON_GetArrayItem(action_list, i);
            if (cmd_item) {
                cJSON *result_item = process_single_command(cmd_item);
                if (result_item) {
                    cJSON_AddItemToArray(response_array, result_item);
                }
            }
        }
        
        cJSON_AddItemToObject(root, "action_list", response_array);
        
        // 序列化并输出结果
        char *json_str = cJSON_PrintUnformatted(root);
        if (json_str) {
            qrzl_log("多条指令执行结果：%s", json_str);
            // 这里应该发送HTTP响应给服务器
            send_http_response(json_str);
            free(json_str);
        }
        
        cJSON_Delete(root);
        
    } else {
        // 检查是否为单条指令格式
        cJSON * json_action = cJSON_GetObjectItem(value, "action");
        if (json_action != NULL && cJSON_IsString(json_action)) {
            // 处理单条指令
            qrzl_log("处理单条指令");
            cJSON *result_item = process_single_command(value);
            if (result_item) {
                char *json_str = cJSON_PrintUnformatted(result_item);
                if (json_str) {
                    qrzl_log("单条指令执行结果：%s", json_str);
                    // 这里应该发送HTTP响应给服务器
                    send_http_response(json_str);
                    free(json_str);
                }
                cJSON_Delete(result_item);
            }
        } else {
            qrzl_log("无效的指令格式");
        }
    }
}


static void boming_start_process()
{
    int ret;
    char http_response[QRZL_HTTP_RESPONSE_MAX] = {0}; // 存储响应体

    update_device_dynamic_data();

    // 发送POST 上报设备信息
    ret = boming_reporting_device_info(http_response);
    // 开机上报一次成功后，更新标识
    qrzl_log("ret: %d", ret);
    qrzl_log("reboot_push_flag：%d", reboot_push_flag);
    if (reboot_push_flag != 0 && ret == 0) {
        reboot_push_flag = 0;
        qrzl_log("开机上报完成");
    }
    if (ret != 0 || strcmp(http_response, "") == 0)
    {
        return;
    }
    cJSON *value = cJSON_Parse(http_response);
    if (value == NULL)
    {
        qrzl_err("json value is NULL");
        return;
    }
    // 确保顶层是一个对象
    if (!cJSON_IsObject(value))
    {
        qrzl_err("JSON is not an object.\n");
        cJSON_Delete(value);
        return;
    }

    boming_resp_handler(value);

    // 释放 JSON 解析结果
    cJSON_Delete(value);
}

void boming_cloud_client_start() {
    int ret;
    ret = init_config_data();
    if (ret != 0)
    {
        qrzl_err("初始化数据失败，请检查配置是否正确");
        return;
    }
    update_device_static_data();
    
    // if (reboot_push_flag == 1) {
    //     qrzl_log("reboot_push_flag :%d , boming cloud client start in 90s", reboot_push_flag);
    //     sleep(90);
    // }

    while (1) 
    {
        boming_start_process();
        sleep(request_interval_time);
    }
}

/***===================== BOMING对接相关处理逻辑 end ===========================***/
#endif /* QRZL_HTTP_CLIENT_BOMING */

/**
 * 子线程的主函数，根据不同的客户需求匹配不同的http请求类型。
 * 因为每一家客户对接的服务提供商都不一样，所以有不同的处理方式
 */
void* start_http_control_client()
{
#ifdef QRZL_CUSTOM_TIANMU_LOGIC
    char flux_total_bytes[21] = {0};
    int ret = cfg_get_item("flux_total", flux_total_bytes, 21);
    if(ret == 0){
        g_qrzl_device_dynamic_data.flux_total_bytes = atoll(flux_total_bytes);
    }
#endif 
    // 防止开机时一开始没网
    int i;
    for (i = 0; i < 3; i++) {
        if (check_network() == 0) {
            break;
        }
    }
    
    // Use compile-time macro selection instead of runtime configuration
#ifdef QRZL_HTTP_CLIENT_QICHENG
    qrzl_log("开始QICHENG格式云接口处理");
    qicheng_cloud_client_start();
#elif defined(QRZL_HTTP_CLIENT_XUNJI)
    qrzl_log("开始XUNJI格式云接口处理");
    xunji_cloud_client_start();
#elif defined(QRZL_HTTP_CLIENT_JIULING)
    qrzl_log("开始JIULING格式云接口处理");
    jiuling_cloud_client_start();
#elif defined(QRZL_HTTP_CLIENT_XINPU)
    qrzl_log("开始XINPU格式云接口处理");
    xinpu_cloud_client_start();
#elif defined(QRZL_HTTP_CLIENT_BOMING)
    qrzl_log("开始BOMING格式云接口处理");
    boming_cloud_client_start();
#else
    qrzl_log("没有定义HTTP客户端类型宏，start_http_control_client线程退出");
#endif
    return NULL;
}

