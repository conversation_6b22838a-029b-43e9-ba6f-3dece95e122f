# WiFiDog + goahead 认证服务器实现方案

## 方案概述

本方案在goahead web服务器中实现了WiFiDog所需的认证服务器接口，使得WiFiDog可以使用网关自身作为认证服务器，而不需要依赖外部认证服务器。

## 架构设计

```
用户设备 -> WiFiDog -> goahead认证接口 -> OneLink认证页面 -> 认证成功 -> 放行网络访问
```

### 工作流程

1. **用户连接WiFi**: 设备连接到WiFi网络
2. **流量拦截**: WiFiDog拦截HTTP请求，重定向到认证页面
3. **认证重定向**: goahead接收认证请求，重定向到OneLink认证页面
4. **用户认证**: 用户在OneLink页面完成认证
5. **认证验证**: WiFiDog定期向goahead验证用户认证状态
6. **网络放行**: 认证成功后，放行用户网络访问

## 实现的接口

### 1. 登录接口 (ONELINK_LOGIN)
- **URL**: `/goform/goform_set_cmd_process?goformId=ONELINK_LOGIN`
- **功能**: 处理WiFiDog的登录请求，重定向到OneLink认证页面
- **参数**: 
  - `gw_address`: 网关地址
  - `gw_port`: 网关端口
  - `gw_id`: 网关ID
  - `mac`: 客户端MAC地址
  - `ip`: 客户端IP地址
  - `url`: 重定向URL
- **响应**: 302重定向到OneLink认证页面

### 2. 认证验证接口 (ONELINK_AUTH)
- **URL**: `/goform/goform_set_cmd_process?goformId=ONELINK_AUTH`
- **功能**: 验证用户认证状态，控制网络访问
- **参数**:
  - `stage`: 认证阶段 (login/counters/logout)
  - `ip`: 客户端IP
  - `mac`: 客户端MAC
  - `token`: 认证令牌
  - `incoming/outgoing`: 流量统计
- **响应**: 
  - `Auth: 1` - 认证成功/继续
  - `Auth: 0` - 认证失败/拒绝

### 3. Ping接口 (ONELINK_PING)
- **URL**: `/goform/goform_set_cmd_process?goformId=ONELINK_PING`
- **功能**: 处理WiFiDog心跳检测
- **响应**: `Pong`

### 4. Portal页面接口 (ONELINK_PORTAL)
- **URL**: `/goform/goform_set_cmd_process?goformId=ONELINK_PORTAL`
- **功能**: 显示认证成功页面
- **响应**: HTML成功页面

### 5. 消息页面接口 (ONELINK_MSG)
- **URL**: `/goform/goform_set_cmd_process?goformId=ONELINK_MSG`
- **功能**: 显示错误消息页面
- **响应**: HTML错误页面

## 文件修改清单

### 1. goahead/server/default.c
- 添加了WiFiDog认证服务器接口函数
- 实现了goform统一处理函数
- 集成了OneLink认证URL构建逻辑

### 2. goahead/server/goahead.c
- 注册了WiFiDog认证接口的URL处理器
- 使用宏控制功能启用

### 3. wifidog/wifidog_onelink_test.conf
- 配置认证服务器为网关自身
- 设置正确的认证接口路径

## 配置说明

### WiFiDog配置
```
AuthServer {
    Hostname *************
    HTTPPort 80
    SSLAvailable no
    Path /goform/
    LoginScriptPathFragment goform_set_cmd_process?goformId=ONELINK_LOGIN&
    PortalScriptPathFragment goform_set_cmd_process?goformId=ONELINK_PORTAL&
    MsgScriptPathFragment goform_set_cmd_process?goformId=ONELINK_MSG&
    PingScriptPathFragment goform_set_cmd_process?goformId=ONELINK_PING&
    AuthScriptPathFragment goform_set_cmd_process?goformId=ONELINK_AUTH&
}
```

### 宏控制
使用 `ZXIC_ONELINK_TEST` 宏控制功能启用：
```c
#ifdef ZXIC_ONELINK_TEST
// WiFiDog认证服务器代码
#endif
```

## 测试验证

### 1. 编译项目
```bash
cd /home/<USER>/work/Code-u28/ap/project/zx297520v3/prj_mifi_mz804_my/build
make app RF_TYPE=230A DCXO=yes
```

### 2. 启动服务
```bash
# 启动goahead
/tmp/goahead &

# 启动wifidog
/tmp/wifidog -f -c /etc_rw/wifidog_onelink_test.conf
```

### 3. 运行测试脚本
```bash
./ap/app/wifidog/test_goahead_auth_server.sh
```

## 优势

1. **自包含**: 不依赖外部认证服务器
2. **集成度高**: 与现有goahead系统无缝集成
3. **可维护性**: 代码集中，易于维护和调试
4. **灵活性**: 可以根据需要扩展认证逻辑
5. **稳定性**: 避免了网络连接问题导致的认证失败

## 注意事项

1. **宏控制**: 使用 `ZXIC_ONELINK_TEST` 宏控制功能启用
2. **权限管理**: 确保脚本有执行权限
3. **网络配置**: 确保br0接口配置正确
4. **防火墙规则**: WiFiDog会自动管理iptables规则
5. **日志监控**: 通过printf输出的日志监控运行状态

## 扩展功能

可以根据需要扩展以下功能：
1. 用户认证状态持久化存储
2. 流量统计和限制
3. 用户会话管理
4. 认证日志记录
5. 多种认证方式支持

## 故障排除

1. **认证失败**: 检查goahead是否正常运行
2. **重定向问题**: 检查OneLink URL配置
3. **网络访问问题**: 检查iptables规则和脚本执行
4. **日志分析**: 查看goahead和wifidog的日志输出
