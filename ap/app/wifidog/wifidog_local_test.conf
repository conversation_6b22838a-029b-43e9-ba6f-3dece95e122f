# WiFiDog Configuration for OneLink Authentication
# 修复版本 - 解决用户直接上网而不跳转认证页面的问题
# 网关地址：br0 IP *************

# Gateway identification
GatewayID zte_mifi_onelink_fixed

# Network interfaces
GatewayInterface br0
GatewayAddress *************
GatewayPort 2060

# External interface (auto-detected if not specified)
# ExternalInterface wan1

# Authentication server configuration
# 使用网关自身作为认证服务器，避免外部服务器连接问题
AuthServer {
    Hostname *************00
    HTTPPort 9080
    SSLAvailable no
    Path /wifidog/
    LoginScriptPathFragment login/?
    PortalScriptPathFragment portal/?
    MsgScriptPathFragment gw_message.php?
    PingScriptPathFragment ping/?
    AuthScriptPathFragment auth/?
}

# Daemon settings
Daemon 1

# HTTP server settings
HTTPDName WiFiDog-OneLink
HTTPDMaxConn 20
HTTPDRealm WiFiDog-OneLink

# Client management
CheckInterval 60
ClientTimeout 5

# Popular servers for connectivity check
PopularServers *******,***************

# Trusted MAC addresses (bypass authentication for testing)
# 添加测试设备的 MAC 地址来绕过认证
# TrustedMACList 00:11:22:33:44:55,aa:bb:cc:dd:ee:ff

# Firewall rules - 严格控制未认证用户的访问
FirewallRuleSet global {
    # Allow DNS - 必须允许DNS解析
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
    
    # Allow DHCP
    FirewallRule allow udp port 67
    FirewallRule allow tcp port 67
    
    # Allow NTP
    FirewallRule allow udp port 123
    
    # 注意：不在global中允许HTTP/HTTPS，强制认证
    # Block SMTP to prevent spam
    FirewallRule block tcp port 25
}

FirewallRuleSet validating-users {
    # Users in validation process - allow limited access
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
    FirewallRule allow udp port 67
    FirewallRule allow tcp port 67
    FirewallRule allow tcp port 9080 to *************00
    FirewallRule allow tcp port 443 to *************00
    FirewallRule allow tcp port 2060 to *************
}

FirewallRuleSet known-users {
    # Authenticated users - allow all
    FirewallRule allow to 0.0.0.0/0
}

FirewallRuleSet unknown-users {
    # Unauthenticated users - very limited access
    
    # Allow DNS for domain resolution
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
    
    # Allow DHCP
    FirewallRule allow udp port 67
    FirewallRule allow tcp port 67
    
    # Allow access to authentication server (修正IP地址)
    FirewallRule allow tcp port 9080 to *************00
    FirewallRule allow tcp port 443 to *************00
    FirewallRule allow tcp port 2060 to *************
    
    # 移除对外部服务器的访问，强制认证
    # 不允许访问外部HTTP/HTTPS服务
    
    # Block everything else by default
}

FirewallRuleSet locked-users {
    # Locked users - block all traffic
    FirewallRule block to 0.0.0.0/0
}
