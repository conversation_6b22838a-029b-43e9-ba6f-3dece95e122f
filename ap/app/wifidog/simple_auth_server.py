#!/usr/bin/env python3
"""
Simple WiFiDog Authentication Server for Testing
This is a minimal implementation of the WiFiDog authentication protocol
for testing purposes only.
"""

import http.server
import socketserver
import urllib.parse
import json
from datetime import datetime

PORT = 9080

class WiFiDogAuthHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        """Handle GET requests from WiFiDog gateway and clients"""
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path
        query = urllib.parse.parse_qs(parsed_path.query)
        
        print(f"[{datetime.now()}] {self.command} {self.path}")
        
        if path.startswith('/wifidog/ping'):
            self.handle_ping(query)
        elif path.startswith('/wifidog/auth'):
            self.handle_auth(query)
        elif path.startswith('/wifidog/login'):
            self.handle_login(query)
        elif path.startswith('/wifidog/portal'):
            self.handle_portal(query)
        elif path.startswith('/wifidog/gw_message.php'):
            self.handle_message(query)
        else:
            self.send_error(404, "Not Found")
    
    def do_POST(self):
        """Handle POST requests"""
        self.do_GET()
    
    def handle_ping(self, query):
        """Handle ping requests from WiFiDog gateway"""
        # WiFiDog ping protocol response
        # Pong response indicates the auth server is alive
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'Pong')
        print("  -> Responded with Pong")
    
    def handle_auth(self, query):
        """Handle authentication requests from WiFiDog gateway"""
        # Extract parameters
        stage = query.get('stage', [''])[0]
        ip = query.get('ip', [''])[0]
        mac = query.get('mac', [''])[0]
        token = query.get('token', [''])[0]
        
        print(f"  -> Auth request: stage={stage}, ip={ip}, mac={mac}, token={token}")
        
        # For testing, we'll allow all users
        # In production, you would check against a database
        if stage == 'login':
            # User is trying to login
            auth_response = 'Auth: 1'  # 1 = allow, 0 = deny
        elif stage == 'counters':
            # Gateway is updating traffic counters
            auth_response = 'Auth: 1'
        else:
            # Default allow
            auth_response = 'Auth: 1'
        
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(auth_response.encode())
        print(f"  -> Responded with: {auth_response}")
    
    def handle_login(self, query):
        """Handle login page requests from clients"""
        # Extract parameters
        gw_address = query.get('gw_address', [''])[0]
        gw_port = query.get('gw_port', [''])[0]
        gw_id = query.get('gw_id', [''])[0]
        url = query.get('url', [''])[0]
        
        print(f"  -> Login request: gw_address={gw_address}, gw_port={gw_port}, gw_id={gw_id}")
        
        # Generate a simple login page
        login_page = f"""
<!DOCTYPE html>
<html>
<head>
    <title>WiFiDog Test Login</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .container {{ max-width: 500px; margin: 0 auto; text-align: center; }}
        .button {{ background: #4CAF50; color: white; padding: 15px 32px; 
                   text-decoration: none; display: inline-block; font-size: 16px; 
                   margin: 4px 2px; cursor: pointer; border-radius: 4px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>WiFiDog Test Authentication</h1>
        <p>Welcome to the WiFiDog test captive portal!</p>
        <p>Gateway ID: {gw_id}</p>
        <p>Your IP: {self.client_address[0]}</p>
        <p>Requested URL: {url}</p>
        
        <form method="get" action="http://{gw_address}:{gw_port}/wifidog/auth">
            <input type="hidden" name="token" value="test_token_123">
            <input type="submit" value="Login (Free Access)" class="button">
        </form>
        
        <p><small>This is a test authentication server for WiFiDog testing.</small></p>
    </div>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(login_page.encode())
        print("  -> Sent login page")
    
    def handle_portal(self, query):
        """Handle portal page requests (after successful login)"""
        # Generate a simple portal page
        portal_page = """
<!DOCTYPE html>
<html>
<head>
    <title>WiFiDog Test Portal</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 500px; margin: 0 auto; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome!</h1>
        <p>You have successfully authenticated with WiFiDog.</p>
        <p>You now have internet access.</p>
        <p><a href="http://www.google.com">Test your connection</a></p>
    </div>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(portal_page.encode())
        print("  -> Sent portal page")
    
    def handle_message(self, query):
        """Handle error message requests"""
        message = query.get('message', ['Unknown error'])[0]
        
        error_page = f"""
<!DOCTYPE html>
<html>
<head>
    <title>WiFiDog Message</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .container {{ max-width: 500px; margin: 0 auto; text-align: center; }}
        .error {{ color: red; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>WiFiDog Message</h1>
        <p class="error">{message}</p>
    </div>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(error_page.encode())
        print(f"  -> Sent error message: {message}")

def main():
    print(f"Starting WiFiDog Test Authentication Server on port {PORT}")
    print("This server implements the basic WiFiDog authentication protocol:")
    print("  - /wifidog/ping - Health check endpoint")
    print("  - /wifidog/auth - Authentication endpoint")
    print("  - /wifidog/login - Login page")
    print("  - /wifidog/portal - Post-login portal")
    print("  - /wifidog/gw_message.php - Error messages")
    print()
    print("Configure your wifidog.conf with:")
    print("  AuthServer {")
    print(f"    Hostname *************  # Replace with your server IP")
    print(f"    HTTPPort {PORT}")
    print("    Path /wifidog/")
    print("  }")
    print()
    
    with socketserver.TCPServer(("", PORT), WiFiDogAuthHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nShutting down server...")
            httpd.shutdown()

if __name__ == "__main__":
    main()
