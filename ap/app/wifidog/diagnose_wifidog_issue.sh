#!/bin/sh

# WiFiDog 问题诊断脚本
# 用于分析用户连接wifi没有跳到认证页面而直接能上网的问题

echo "=== WiFiDog 问题诊断报告 ==="
echo "时间: $(date)"
echo ""

# 1. 检查wifidog进程状态
echo "1. WiFiDog 进程状态:"
ps | grep wifidog | grep -v grep
if [ $? -eq 0 ]; then
    echo "   ✓ WiFiDog 进程正在运行"
else
    echo "   ✗ WiFiDog 进程未运行"
fi
echo ""

# 2. 检查网络接口状态
echo "2. 网络接口状态:"
echo "   br0 接口:"
ifconfig br0 2>/dev/null | grep "inet addr" || echo "   ✗ br0 接口未配置"
echo "   wan1 接口:"
ifconfig wan1 2>/dev/null | grep "inet addr" || echo "   ✗ wan1 接口未配置"
echo ""

# 3. 检查iptables规则
echo "3. WiFiDog iptables 规则分析:"

# 检查NAT表中的REDIRECT规则
echo "   NAT表 REDIRECT 规则:"
redirect_count=$(iptables -t nat -L WiFiDog_br0_Unknown -n 2>/dev/null | grep "REDIRECT.*2060" | wc -l)
if [ "$redirect_count" -gt 0 ]; then
    echo "   ✓ REDIRECT 规则存在"
    # 检查规则的包计数
    iptables -t nat -L WiFiDog_br0_Unknown -n -v 2>/dev/null | grep "REDIRECT.*2060"
else
    echo "   ✗ REDIRECT 规则不存在"
fi

# 检查FILTER表中的规则
echo "   FILTER表 WiFiDog_br0_Global 规则:"
iptables -t filter -L WiFiDog_br0_Global -n -v 2>/dev/null | head -10

echo "   FILTER表 WiFiDog_br0_Unknown 规则:"
iptables -t filter -L WiFiDog_br0_Unknown -n -v 2>/dev/null | head -10
echo ""

# 4. 检查认证服务器连通性
echo "4. 认证服务器连通性检查:"
auth_server=$(grep "Hostname" /etc_rw/wifidog_local_test.conf 2>/dev/null | awk '{print $2}')
auth_port=$(grep "HTTPPort" /etc_rw/wifidog_local_test.conf 2>/dev/null | awk '{print $2}')

if [ -n "$auth_server" ] && [ -n "$auth_port" ]; then
    echo "   认证服务器: $auth_server:$auth_port"
    nc -z -w 3 "$auth_server" "$auth_port" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "   ✓ 认证服务器可访问"
    else
        echo "   ✗ 认证服务器不可访问"
    fi
else
    echo "   ✗ 无法从配置文件获取认证服务器信息"
fi
echo ""

# 5. 检查wifidog监听端口
echo "5. WiFiDog 监听端口检查:"
netstat -ln | grep ":2060"
if [ $? -eq 0 ]; then
    echo "   ✓ WiFiDog 正在监听 2060 端口"
else
    echo "   ✗ WiFiDog 未监听 2060 端口"
fi
echo ""

# 6. 分析问题原因
echo "6. 问题分析:"

# 检查是否有流量被重定向
redirect_packets=$(iptables -t nat -L WiFiDog_br0_Unknown -n -v 2>/dev/null | grep "REDIRECT.*2060" | awk '{print $1}')
if [ -n "$redirect_packets" ] && [ "$redirect_packets" -gt 0 ]; then
    echo "   ✓ 有流量被重定向到 wifidog ($redirect_packets 个包)"
else
    echo "   ✗ 没有流量被重定向到 wifidog"
    echo "     可能原因:"
    echo "     - 流量被其他规则提前处理"
    echo "     - WiFiDog_br0_Global 规则过于宽松"
    echo "     - 客户端使用了HTTPS而不是HTTP"
fi

# 检查Global规则是否过于宽松
global_http_packets=$(iptables -t filter -L WiFiDog_br0_Global -n -v 2>/dev/null | grep "tcp dpt:80" | awk '{print $1}')
global_https_packets=$(iptables -t filter -L WiFiDog_br0_Global -n -v 2>/dev/null | grep "tcp dpt:443" | awk '{print $1}')

if [ -n "$global_http_packets" ] && [ "$global_http_packets" -gt 0 ]; then
    echo "   ⚠ Global规则允许了 $global_http_packets 个HTTP包通过"
fi

if [ -n "$global_https_packets" ] && [ "$global_https_packets" -gt 0 ]; then
    echo "   ⚠ Global规则允许了 $global_https_packets 个HTTPS包通过"
fi

echo ""

# 7. 建议的解决方案
echo "7. 建议的解决方案:"
echo "   1. 修改 wifidog.conf 配置，移除 global 规则中的 HTTP/HTTPS 允许"
echo "   2. 确保认证服务器使用网关本身 (*************)"
echo "   3. 检查是否有其他防火墙规则干扰"
echo "   4. 重启 wifidog 服务"
echo ""

echo "=== 诊断完成 ==="
