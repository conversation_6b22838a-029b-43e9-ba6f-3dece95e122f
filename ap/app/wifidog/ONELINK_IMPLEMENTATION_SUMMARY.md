# OneLink 实现总结

## 问题分析与解决方案

### 1. ZXIC_ONELINK_TEST 宏定义作用

ZXIC_ONELINK_TEST 宏在 `wlan_main.c` 中的作用：

- **add_sta_mac 函数**：当设备连接WiFi时，检查MAC是否已认证，如果未认证则调用脚本禁用网络访问
- **remove_sta_mac 函数**：当设备断开WiFi时，调用脚本清理该MAC的所有iptables规则
- **cleanup_mac_iptables_rules 函数**：清理指定MAC地址相关的所有iptables规则（已改为脚本方式）

### 2. 重写 one_link_authenticated.sh 脚本

**主要改进：**
- 添加了 `rule_exists()` 函数，避免使用不支持的 `-C` 选项
- 优化了 `block_mac()` 函数，确保禁用时只有一条iptables记录
- 改进了 `cleanup_mac_rules()` 函数，确保放行时完全清除所有规则
- 正确处理IPv6规则，在add_sta_mac中禁用IPv6，在放行时恢复IPv6访问

**脚本位置：** `ap/project/zx297520v3/prj_mifi_mz804_chuangsan/fs/normal/rootfs/sbin/one_link_authenticated.sh`

### 3. 修复 zte_web_get_onelink_register_page 函数

**修改内容：**
- 修正了获取路由器MAC地址的NV项，从 `at_wifi_mac` 改为 `wifi_mac`，并添加备用选项
- 确保 terminalMac 为终端真实MAC（从wp中获取）
- 确保 mac 为mifi路由器MAC（从nv wifi_mac获取）
- 确保 iccid 从nv ziccid获取

**文件位置：** `ap/app/goahead/interface5.0/zte_web_util.c`

### 4. 优化 forward.c 中域名提取逻辑

**现状：** forward.c 中的域名提取逻辑已经正确实现，能够从 `one_link_customers_get_token_url` 提取域名，如果为null则使用默认值 `wireless.cmonelink.com`

**新增功能：** 添加了MAC管理函数：
- `allow_mac_internet_access()` - 清除MAC的iptables限制规则
- `block_mac_internet_access()` - 禁用MAC的网络访问

### 5. QRZL_ONE_LINK_CUSTOMER_MY 检查

**检查结果：** 该宏的使用是正确的，没有发现错误。

### 6. 实现 qrzl_app 进程消息处理

**现状：** qrzl_app.c 中已经实现了对 `MSG_CMD_QRZL_APP_WIFI_CONNECTED` 和 `MSG_CMD_QRZL_APP_WIFI_DISCONNECT` 消息的处理。

**消息定义：** 在 `ap/app/include/other_msg.h` 中正确定义了相关消息ID。

### 7. 修改 wlan_main.c 和 default.c

**主要修改：**
- `add_sta_mac()` 函数：改为调用脚本 `/sbin/one_link_authenticated.sh block <mac>`
- `remove_sta_mac()` 函数：改为调用脚本 `/sbin/one_link_authenticated.sh allow <mac>`
- `block_unauthenticated_mac()` 函数：改为调用脚本处理，不再直接执行iptables命令

### 8. WiFiDog 配置问题分析与解决

**问题根因：**
1. **配置文件中global规则过于宽松**：允许了所有HTTP/HTTPS流量，导致用户可以直接上网
2. **认证服务器连接问题**：外部认证服务器无法访问
3. **流量没有被正确重定向**：REDIRECT规则包计数为0

**解决方案：**
1. **修复配置文件** `wifidog_onelink_test.conf`：
   - 移除global规则中的通用HTTP/HTTPS允许
   - 修改认证服务器为网关本身（*************）
   - 调整防火墙规则，确保未认证用户无法直接上网

2. **创建诊断脚本** `diagnose_wifidog_issue.sh`：
   - 检查wifidog进程状态
   - 分析iptables规则
   - 检查认证服务器连通性
   - 提供问题诊断和解决建议

3. **创建测试脚本** `test_wifidog_onelink.sh`：
   - 自动化测试流程
   - 清理现有规则
   - 启动wifidog进行测试

## 文件修改清单

### 修改的文件：
1. `ap/project/zx297520v3/prj_mifi_mz804_chuangsan/fs/normal/rootfs/sbin/one_link_authenticated.sh` - 重写脚本
2. `ap/app/zte_comm/wlan/src/wlan_main.c` - 修改MAC处理逻辑
3. `ap/app/goahead/server/default.c` - 修改block_unauthenticated_mac函数
4. `ap/app/goahead/interface5.0/zte_web_util.c` - 修复MAC获取逻辑
5. `ap/app/dnsmasq/dnsmasq-2.86/src/forward.c` - 添加MAC管理函数
6. `ap/app/wifidog/wifidog_onelink_test.conf` - 修复配置问题

### 新增的文件：
1. `ap/app/wifidog/wifidog_onelink_fixed.conf` - 修复版配置文件
2. `ap/app/wifidog/diagnose_wifidog_issue.sh` - 诊断脚本
3. `ap/app/wifidog/test_wifidog_onelink.sh` - 测试脚本

## 测试验证

### 1. 编译项目
```bash
cd /home/<USER>/work/Code-u28/ap/project/zx297520v3/prj_mifi_mz804_my/build
make app RF_TYPE=230A DCXO=yes
```

### 2. 测试脚本功能
```bash
# 测试MAC禁用
/sbin/one_link_authenticated.sh block aa:bb:cc:dd:ee:ff

# 测试MAC放行
/sbin/one_link_authenticated.sh allow aa:bb:cc:dd:ee:ff
```

### 3. 测试wifidog功能
```bash
# 运行诊断脚本
./ap/app/wifidog/diagnose_wifidog_issue.sh

# 运行测试脚本
./ap/app/wifidog/test_wifidog_onelink.sh
```

## 预期效果

1. **MAC管理**：设备连接WiFi时自动禁用网络访问，认证后放行
2. **认证流程**：用户访问HTTP网站时被重定向到认证页面
3. **OneLink集成**：正确获取终端MAC、路由器MAC和ICCID信息
4. **IPv6支持**：正确处理IPv6流量的禁用和放行

## 注意事项

1. 确保脚本有执行权限：`chmod +x /sbin/one_link_authenticated.sh`
2. 测试时注意检查iptables规则是否正确应用
3. 如果使用wifidog，确保配置文件中不要在global规则中允许通用HTTP/HTTPS访问
4. 认证服务器建议使用网关本身，避免外部服务器连接问题
