config BR2_PACKAGE_WIFIDOG
	prompt "wifidog........................... A wireless captive portal solution"
	tristate
	default m if CONFIG_DEVEL
	select BR2_PACKAGE_LIBPTHREAD
	select BR2_PACKAGE_IPTABLES
	select BR2_PACKAGE_IPTABLES_MOD_NAT
	select BR2_PACKAGE_IPTABLES-MOD_IPOPT
	help
	  The Wifidog project is a complete and embeddable captive
	  portal solution for wireless community groups or individuals
	  who wish to open a free Hotspot while still preventing abuse
	  of their Internet connection.
	  
	  http://dev.wifidog.org/

