~ # iptables -t nat -nvL
Chain PREROUTING (policy ACCEPT 23884 packets, 3520K bytes)
 pkts bytes target     prot opt in     out     source               destination
42185 4623K port_mapping  all  --  *      *       0.0.0.0/0            0.0.0.0/0
42185 4623K DMZ        all  --  *      *       0.0.0.0/0            0.0.0.0/0
42185 4623K port_forward  all  --  *      *       0.0.0.0/0            0.0.0.0/0
  564  105K WiFiDog_br0_Outgoing  all  --  br0    *       0.0.0.0/0            0.0.0.0/0

Chain INPUT (policy ACCEPT 15149 packets, 865K bytes)
 pkts bytes target     prot opt in     out     source               destination

Chain OUTPUT (policy ACCEPT 6312 packets, 432K bytes)
 pkts bytes target     prot opt in     out     source               destination

Chain POSTROUTING (policy ACCEPT 830 packets, 58641 bytes)
 pkts bytes target     prot opt in     out     source               destination
18128 1289K MASQUERADE  all  --  *      wan1    0.0.0.0/0            0.0.0.0/0

Chain DMZ (1 references)
 pkts bytes target     prot opt in     out     source               destination

Chain WiFiDog_br0_AuthServers (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 ACCEPT     all  --  *      *       0.0.0.0/0            ***************

Chain WiFiDog_br0_Global (1 references)
 pkts bytes target     prot opt in     out     source               destination
   39  2256 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:53
   33  1716 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:53
    1   350 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:67
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:67
    5   380 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:123
   29  1704 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:80
  120  8212 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:443

Chain WiFiDog_br0_Internet (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 ACCEPT     all  --  *      *       0.0.0.0/0            0.0.0.0/0           mark match 0x2
    0     0 ACCEPT     all  --  *      *       0.0.0.0/0            0.0.0.0/0           mark match 0x1
  388 94167 WiFiDog_br0_Unknown  all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain WiFiDog_br0_Outgoing (1 references)
 pkts bytes target     prot opt in     out     source               destination
  176 10774 WiFiDog_br0_Router  all  --  *      *       0.0.0.0/0            192.168.100.1
  388 94167 WiFiDog_br0_Internet  all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain WiFiDog_br0_Router (1 references)
 pkts bytes target     prot opt in     out     source               destination
  176 10774 ACCEPT     all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain WiFiDog_br0_Unknown (1 references)
 pkts bytes target     prot opt in     out     source               destination
  388 94167 WiFiDog_br0_AuthServers  all  --  *      *       0.0.0.0/0            0.0.0.0/0
  388 94167 WiFiDog_br0_Global  all  --  *      *       0.0.0.0/0            0.0.0.0/0
    0     0 REDIRECT   tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:80 redir ports 2060

Chain port_forward (1 references)
 pkts bytes target     prot opt in     out     source               destination

Chain port_mapping (1 references)
 pkts bytes target     prot opt in     out     source               destination
~ # iptables  -nvL
Chain INPUT (policy ACCEPT 63423 packets, 17M bytes)
 pkts bytes target     prot opt in     out     source               destination
    0     0 DROP       tcp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           tcp dpt:1900
    0     0 DROP       udp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           udp dpt:53
    0     0 DROP       tcp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           tcp dpt:53
    0     0 DROP       udp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           udp dpt:67
    0     0 DROP       udp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           udp dpt:1900
    0     0 DROP       tcp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           tcp dpt:1900
   15  1260 ACCEPT     icmp --  wan1   *       0.0.0.0/0            0.0.0.0/0           icmp type 0
    0     0 DROP       tcp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           tcp dpt:80
    0     0 DROP       tcp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           tcp dpt:443
    0     0 DROP       icmp --  wan1   *       0.0.0.0/0            0.0.0.0/0
63423   17M children_web_filter  all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain FORWARD (policy ACCEPT 450K packets, 392M bytes)
 pkts bytes target     prot opt in     out     source               destination
13019  858K WiFiDog_br0_Internet  all  --  br0    *       0.0.0.0/0            0.0.0.0/0
 450K  392M web_filter  all  --  *      *       0.0.0.0/0            0.0.0.0/0
 450K  392M macipport_filter  all  --  *      *       0.0.0.0/0            0.0.0.0/0
 450K  392M children_filter  all  --  *      *       0.0.0.0/0            0.0.0.0/0
 450K  392M children_web_filter_phone  all  --  *      *       0.0.0.0/0            0.0.0.0/0
15564  839K TCPMSS     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp flags:0x06/0x02 TCPMSS clamp to PMTU

Chain OUTPUT (policy ACCEPT 70287 packets, 19M bytes)
 pkts bytes target     prot opt in     out     source               destination

Chain WiFiDog_br0_AuthServers (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 ACCEPT     all  --  *      *       0.0.0.0/0            ***************

Chain WiFiDog_br0_Global (1 references)
 pkts bytes target     prot opt in     out     source               destination
   52  3008 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:53
  105  4947 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:53
    2   678 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:67
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:67
    6   456 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:123
  139 39269 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:80
12440  717K ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:443
    0     0 REJECT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:25 reject-with icmp-port-unreachable

Chain WiFiDog_br0_Internet (1 references)
 pkts bytes target     prot opt in     out     source               destination
   80  4323 DROP       all  --  *      *       0.0.0.0/0            0.0.0.0/0           state INVALID
  437 24508 TCPMSS     tcp  --  *      wan1    0.0.0.0/0            0.0.0.0/0           tcp flags:0x06/0x02 TCPMSS clamp to PMTU
12938  853K WiFiDog_br0_AuthServers  all  --  *      *       0.0.0.0/0            0.0.0.0/0
    0     0 WiFiDog_br0_Locked  all  --  *      *       0.0.0.0/0            0.0.0.0/0           mark match 0x254
12937  853K WiFiDog_br0_Global  all  --  *      *       0.0.0.0/0            0.0.0.0/0
    0     0 WiFiDog_br0_Validate  all  --  *      *       0.0.0.0/0            0.0.0.0/0           mark match 0x1
    0     0 WiFiDog_br0_Known  all  --  *      *       0.0.0.0/0            0.0.0.0/0           mark match 0x2
  190 87416 WiFiDog_br0_Unknown  all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain WiFiDog_br0_Known (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 ACCEPT     all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain WiFiDog_br0_Locked (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 REJECT     all  --  *      *       0.0.0.0/0            0.0.0.0/0           reject-with icmp-port-unreachable

Chain WiFiDog_br0_Unknown (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:53
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:53
    0     0 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:67
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:67
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            192.168.100.1       tcp dpt:9080
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            17.0.0.0/8          tcp dpt:80
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            17.0.0.0/8          tcp dpt:443
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            8.8.8.8             tcp dpt:80
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            8.8.8.8             tcp dpt:443
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            8.8.4.4             tcp dpt:80
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            8.8.4.4             tcp dpt:443
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            114.114.114.114     tcp dpt:80
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            114.114.114.114     tcp dpt:443
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            192.168.100.1       tcp dpt:2060
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            192.168.100.1       tcp dpt:80
  190 87416 REJECT     all  --  *      *       0.0.0.0/0            0.0.0.0/0           reject-with icmp-port-unreachable

Chain WiFiDog_br0_Validate (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 ACCEPT     all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain children_filter (1 references)
 pkts bytes target     prot opt in     out     source               destination

Chain children_web_filter (1 references)
 pkts bytes target     prot opt in     out     source               destination

Chain children_web_filter_phone (1 references)
 pkts bytes target     prot opt in     out     source               destination

Chain macipport_filter (1 references)
 pkts bytes target     prot opt in     out     source               destination

Chain web_filter (1 references)
 pkts bytes target     prot opt in     out     source               destination







 ~ # iptables -t nat -nvL
Chain PREROUTING (policy ACCEPT 23935 packets, 3527K bytes)
 pkts bytes target     prot opt in     out     source               destination
42494 4643K port_mapping  all  --  *      *       0.0.0.0/0            0.0.0.0/0
42494 4643K DMZ        all  --  *      *       0.0.0.0/0            0.0.0.0/0
42494 4643K port_forward  all  --  *      *       0.0.0.0/0            0.0.0.0/0
  873  125K WiFiDog_br0_Outgoing  all  --  br0    *       0.0.0.0/0            0.0.0.0/0

Chain INPUT (policy ACCEPT 15248 packets, 870K bytes)
 pkts bytes target     prot opt in     out     source               destination

Chain OUTPUT (policy ACCEPT 6390 packets, 437K bytes)
 pkts bytes target     prot opt in     out     source               destination

Chain POSTROUTING (policy ACCEPT 835 packets, 58941 bytes)
 pkts bytes target     prot opt in     out     source               destination
18360 1302K MASQUERADE  all  --  *      wan1    0.0.0.0/0            0.0.0.0/0

Chain DMZ (1 references)
 pkts bytes target     prot opt in     out     source               destination

Chain WiFiDog_br0_AuthServers (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 ACCEPT     all  --  *      *       0.0.0.0/0            ***************

Chain WiFiDog_br0_Global (1 references)
 pkts bytes target     prot opt in     out     source               destination
   58  3380 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:53
   47  2444 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:53
    1   350 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:67
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:67
    6   456 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:123
   41  2384 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:80
  233 14240 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:443

Chain WiFiDog_br0_Internet (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 ACCEPT     all  --  *      *       0.0.0.0/0            0.0.0.0/0           mark match 0x2
    0     0 ACCEPT     all  --  *      *       0.0.0.0/0            0.0.0.0/0           mark match 0x1
  598  109K WiFiDog_br0_Unknown  all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain WiFiDog_br0_Outgoing (1 references)
 pkts bytes target     prot opt in     out     source               destination
  275 15904 WiFiDog_br0_Router  all  --  *      *       0.0.0.0/0            192.168.100.1
  598  109K WiFiDog_br0_Internet  all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain WiFiDog_br0_Router (1 references)
 pkts bytes target     prot opt in     out     source               destination
  275 15904 ACCEPT     all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain WiFiDog_br0_Unknown (1 references)
 pkts bytes target     prot opt in     out     source               destination
  598  109K WiFiDog_br0_AuthServers  all  --  *      *       0.0.0.0/0            0.0.0.0/0
  598  109K WiFiDog_br0_Global  all  --  *      *       0.0.0.0/0            0.0.0.0/0
    0     0 REDIRECT   tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:80 redir ports 2060

Chain port_forward (1 references)
 pkts bytes target     prot opt in     out     source               destination

Chain port_mapping (1 references)
 pkts bytes target     prot opt in     out     source               destination


~ #  iptables -t filter -nvL
Chain INPUT (policy ACCEPT 63598 packets, 17M bytes)
 pkts bytes target     prot opt in     out     source               destination
    0     0 DROP       tcp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           tcp dpt:1900
    0     0 DROP       udp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           udp dpt:53
    0     0 DROP       tcp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           tcp dpt:53
    0     0 DROP       udp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           udp dpt:67
    0     0 DROP       udp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           udp dpt:1900
    0     0 DROP       tcp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           tcp dpt:1900
   15  1260 ACCEPT     icmp --  wan1   *       0.0.0.0/0            0.0.0.0/0           icmp type 0
    0     0 DROP       tcp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           tcp dpt:80
    0     0 DROP       tcp  --  wan1   *       0.0.0.0/0            0.0.0.0/0           tcp dpt:443
    0     0 DROP       icmp --  wan1   *       0.0.0.0/0            0.0.0.0/0
63598   17M children_web_filter  all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain FORWARD (policy ACCEPT 459K packets, 403M bytes)
 pkts bytes target     prot opt in     out     source               destination
18722 1263K WiFiDog_br0_Internet  all  --  br0    *       0.0.0.0/0            0.0.0.0/0
 459K  403M web_filter  all  --  *      *       0.0.0.0/0            0.0.0.0/0
 459K  403M macipport_filter  all  --  *      *       0.0.0.0/0            0.0.0.0/0
 459K  403M children_filter  all  --  *      *       0.0.0.0/0            0.0.0.0/0
 459K  403M children_web_filter_phone  all  --  *      *       0.0.0.0/0            0.0.0.0/0
15638  842K TCPMSS     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp flags:0x06/0x02 TCPMSS clamp to PMTU

Chain OUTPUT (policy ACCEPT 70524 packets, 19M bytes)
 pkts bytes target     prot opt in     out     source               destination

Chain WiFiDog_br0_AuthServers (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 ACCEPT     all  --  *      *       0.0.0.0/0            ***************

Chain WiFiDog_br0_Global (1 references)
 pkts bytes target     prot opt in     out     source               destination
   70  4070 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:53
  146  6905 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:53
    2   678 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:67
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:67
    7   532 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:123
  160 41941 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:80
17985 1108K ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:443
    0     0 REJECT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:25 reject-with icmp-port-unreachable

Chain WiFiDog_br0_Internet (1 references)
 pkts bytes target     prot opt in     out     source               destination
   89  4908 DROP       all  --  *      *       0.0.0.0/0            0.0.0.0/0           state INVALID
  666 36816 TCPMSS     tcp  --  *      wan1    0.0.0.0/0            0.0.0.0/0           tcp flags:0x06/0x02 TCPMSS clamp to PMTU
18632 1258K WiFiDog_br0_AuthServers  all  --  *      *       0.0.0.0/0            0.0.0.0/0
    0     0 WiFiDog_br0_Locked  all  --  *      *       0.0.0.0/0            0.0.0.0/0           mark match 0x254
18631 1258K WiFiDog_br0_Global  all  --  *      *       0.0.0.0/0            0.0.0.0/0
    0     0 WiFiDog_br0_Validate  all  --  *      *       0.0.0.0/0            0.0.0.0/0           mark match 0x1
    0     0 WiFiDog_br0_Known  all  --  *      *       0.0.0.0/0            0.0.0.0/0           mark match 0x2
  258 95962 WiFiDog_br0_Unknown  all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain WiFiDog_br0_Known (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 ACCEPT     all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain WiFiDog_br0_Locked (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 REJECT     all  --  *      *       0.0.0.0/0            0.0.0.0/0           reject-with icmp-port-unreachable

Chain WiFiDog_br0_Unknown (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:53
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:53
    0     0 ACCEPT     udp  --  *      *       0.0.0.0/0            0.0.0.0/0           udp dpt:67
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            0.0.0.0/0           tcp dpt:67
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            192.168.100.1       tcp dpt:9080
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            17.0.0.0/8          tcp dpt:80
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            17.0.0.0/8          tcp dpt:443
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            8.8.8.8             tcp dpt:80
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            8.8.8.8             tcp dpt:443
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            8.8.4.4             tcp dpt:80
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            8.8.4.4             tcp dpt:443
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            114.114.114.114     tcp dpt:80
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            114.114.114.114     tcp dpt:443
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            192.168.100.1       tcp dpt:2060
    0     0 ACCEPT     tcp  --  *      *       0.0.0.0/0            192.168.100.1       tcp dpt:80
  258 95962 REJECT     all  --  *      *       0.0.0.0/0            0.0.0.0/0           reject-with icmp-port-unreachable

Chain WiFiDog_br0_Validate (1 references)
 pkts bytes target     prot opt in     out     source               destination
    0     0 ACCEPT     all  --  *      *       0.0.0.0/0            0.0.0.0/0

Chain children_filter (1 references)
 pkts bytes target     prot opt in     out     source               destination

Chain children_web_filter (1 references)
 pkts bytes target     prot opt in     out     source               destination

Chain children_web_filter_phone (1 references)
 pkts bytes target     prot opt in     out     source               destination

Chain macipport_filter (1 references)
 pkts bytes target     prot opt in     out     source               destination

Chain web_filter (1 references)
 pkts bytes target     prot opt in     out     source               destination
~ #