#ifndef _WLAN_INTERFACE
#define _WLAN_INTERFACE

#include "wifi.h"

int zte_wlan_get_ssid1_settings(char* ssid, char* password, int ssid_len, int password_len);
int zte_wlan_ssid1_set(char* ssid, char* password);
int zte_wlan_capture_sta_num();

void zte_wlan_get_wifi_mac_list(RT_802_11_MAC_TABLE *maclist);
#if 0 //kw 3
int pipecmd(const char *cmd, char* result);
int get_channel();
#endif
int get_vap_sta_num(char* vapIface);

#endif
