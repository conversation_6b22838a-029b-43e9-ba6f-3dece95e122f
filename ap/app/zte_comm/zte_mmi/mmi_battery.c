/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_batterry.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��MMI��ȡ���͵����Ϣ
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
******************************************************************************/


/************************************************************************************
                           ͷ�ļ�
***********************************************************************************/
#include "mmi_lcd.h"
#include <linux/netlink.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <mmi_common.h>
#include "hotplug.h"


/************************************************************************************
                            ȫ�ֱ�������
***********************************************************************************/

static UINT32 s_mmi_poweroff_voltage_num = 0;//�����͵�ػ���ѹ�Ĵ��������������Զ��ػ�
static SINT32 g_mmi_voltageEx = 0;//ÿ20s��ѯ��ѹʱ��������һ�εĵ�ѹ
#if defined(QRZL_UE) && defined(JCV_HW_MZ901_V1_0)
static SINT32 g_mmi_charging_voltageEx = 0;
#endif
static E_zMmi_Charge_State s_mmi_charge_state = STATE_CHARGE_MAX; //ÿ���յ������ϱ�ʱ�����浱ǰ�ĳ��״̬
static E_zMmi_Charge_State s_mmi_charge_stateEx = STATE_CHARGE_MAX;//ÿ���յ������ϱ�ʱ��������һ�εĳ��״̬
static E_zMmi_Voltage_level s_mmi_voltage_level = VOLT_MAX;//��ѯ��ѹʱ�����ݵ�ѹֵ���浱ǰ������Χ

static BOOL g_mmi_chg_switch_off = FALSE;//��¼���رտ��أ�Ĭ���������
static BOOL s_mmi_usb_insert_status = FALSE;//0:USB����״̬ 1:USB δ����״̬
static SINT32 s_mmi_battery_pers = 0;//�����ٷֱ�����

static MMI_TEMP_DETECT last_temp = MMI_TEMP_DETECT_MAX;//�����һ���¶�״̬
static SINT32 s_mmi_temp_count = 0;//�¶��쳣ʱ������������3�κ������Ӧ����


extern E_zMmiShowMode g_showMode;
extern UINT32 g_temp_protect;
extern UINT32 g_discharge_protect;
extern UINT32 g_charge_protect;
extern UINT32 g_fast_poweron;
/**********************************************************************************
*��������:��ȡ�����Ϣ
***********************************************************************************/
SINT32 mmi_get_batteryinfo(UINT32 batteryinfo)
{
	if (batteryinfo != 0) {
		T_zMMIBatteryInfo * pBatInfo = (T_zMMIBatteryInfo *)batteryinfo;
		pBatInfo->chg_state = s_mmi_charge_state;
		pBatInfo->bat_level = s_mmi_voltage_level;
		pBatInfo->bat_pers = s_mmi_battery_pers;
#if defined(QRZL_UE) && defined(JCV_HW_MZ801_V1_2)
		pBatInfo->bat_grid = pBatInfo->bat_pers <= 5 ? 0 : (pBatInfo->bat_pers <= 25 ? 1 : (pBatInfo->bat_pers <= 50 ? 2 : (pBatInfo->bat_pers <= 75 ? 3 : (pBatInfo->bat_pers < 100 ? 4 : 5) ))); /*����Ҫ��<=5��ʾ���0��,<=25��ʾ1��*/
#else
		pBatInfo->bat_grid = pBatInfo->bat_pers <= 5 ? 0 : (pBatInfo->bat_pers <= 25 ? 1 : (pBatInfo->bat_pers <= 50 ? 2 : (pBatInfo->bat_pers < 100 ? 3 : 4))); /*����Ҫ��<=5��ʾ���0��,<=25��ʾ1��*/
#endif
	}
	return MMI_SUCCESS;
}

SINT32 mmi_RegisterBatteryTaskInfoItem()
{
	T_zMMITaskInfoItem batteryTaskInfoItem = {0};

	batteryTaskInfoItem.task = MMI_TASK_BATTERY;
	batteryTaskInfoItem.taskinfo = (VOID *)malloc(sizeof(T_zMMIBatteryInfo));
	batteryTaskInfoItem.get_taskinfo_fun = mmi_get_batteryinfo;
	batteryTaskInfoItem.get_ledinfo_fun = mmi_getLedBatteryInfo;
#ifndef DISABLE_LCD
	batteryTaskInfoItem.get_lcdinfo_fun = mmi_getLcdBatteryInfo;
#endif
	batteryTaskInfoItem.ledinfo = (VOID *)malloc(sizeof(T_zMmi_Led_Info));
	//batteryAppInfoItem.show_mode = 2;
	mmi_register_taskinfo_item(&batteryTaskInfoItem);
	return MMI_SUCCESS;
}
SINT32 mmi_RegisterPowerTaskInfoItem()
{
	T_zMMITaskInfoItem batteryTaskInfoItem = {0};
	batteryTaskInfoItem.task = MMI_TASK_POWER;
	batteryTaskInfoItem.taskinfo = (VOID *)malloc(sizeof(T_zMMIBatteryInfo));
	batteryTaskInfoItem.get_taskinfo_fun = mmi_get_batteryinfo;
	batteryTaskInfoItem.get_ledinfo_fun = mmi_getLedBatteryInfo;
#ifndef DISABLE_LCD
	batteryTaskInfoItem.get_lcdinfo_fun = mmi_getLcdPowerInfo;
#endif
	batteryTaskInfoItem.ledinfo = (VOID *)malloc(sizeof(T_zMmi_Led_Info));
	//batteryAppInfoItem.show_mode = 2;
	mmi_register_taskinfo_item(&batteryTaskInfoItem);
	return MMI_SUCCESS;
}

static VOID mmi_set_battery_update(VOID)
{
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		mmi_set_update_flag(MMI_TASK_BATTERY);
	}
	if (g_showMode == MMI_MODE_LED || g_showMode == MMI_MODE_ALL) {
		E_zMmi_Work_Mode mode = mmi_get_led_mode();
		if (mode != MMI_IDLE_LEDOFF_MODE && mode != MMI_FAKE_POWEROFF_MODE && mode != MMI_IDLE_STANDBY_LEDOFF_MODE) {
			mmi_set_update_flag(MMI_TASK_BATTERY);
		}
	}
}
VOID mmi_set_discharge_low_current(BOOL discharge_low_current_flag)
{
	if (discharge_low_current_flag) {
		system(MMI_DISCHG_LOW_CURRENT);
	} else {
		system(MMI_DISCHG_HIGH_CURRENT);
	}
}

/**********************************************************************************
*��������:��֪charger����ٹػ�ģʽ
***********************************************************************************/
VOID mmi_set_fake_poweroff_charger(int fake_poweroff_flag)
{
	if (fake_poweroff_flag == 1) {
		system(MMI_SET_FAKEPOWEROFF_CHARGER);
	} else {
		system(MMI_CAN_FAKEPOWEROFF_CHARGER);
	}
}

/**********************************************************************************
*��������:�͵�ػ�����
***********************************************************************************/
static void mmi_lowbattery_shutdown(UINT32 voltagepower)
{
	if (voltagepower < POWEROFFLEVEL) {
		s_mmi_poweroff_voltage_num ++ ;
		if (s_mmi_poweroff_voltage_num == 3) {
			s_mmi_poweroff_voltage_num = 0;
			mmi_set_abnormal_poweroff_flag();

			slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI poweroff,lowerbattery!!!\n");
			mmi_set_poweroff();
		}
	} else {
		s_mmi_poweroff_voltage_num = 0;
	}
}

/**********************************************************************************
*��������:���ݵ�ص�ѹ���õ�ذٷֱȡ�webUI��Ӧ��ظ�
�������״̬(5%,10%,20%,25%,normal)
***********************************************************************************/
#if defined(QRZL_UE) && defined(JCV_HW_MZ901_V1_0)
static VOID mmi_battery_capacity_calculate(SINT32 voltagepower, BOOL charging_status)
{
        int cur_power = 0;
        int bat_level = 0;
        E_zMmi_Voltage_level voltage_level = 0;
        static int webUI_batterychange_flag = 0;

        if(charging_status) {
                cur_power = voltagepower > g_mmi_charging_voltageEx ? voltagepower : g_mmi_charging_voltageEx;

                bat_level = get_charging_voltage_level_from_table(cur_power, TRUE);
                voltage_level = mmi_get_battery_volt_percent_level(bat_level, TRUE);

                slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI %s webUI_batterychange_flag=%d charging_status (%d->%d) voltage_level (%d->%d) \n ", __func__, webUI_batterychange_flag, s_mmi_charge_state, charging_status, s_mmi_voltage_level, voltage_level);

                if(VOLT_FULLLEVEL == voltage_level) {
                        //webui full icon 
                        mmi_set_webUI_batterycharge(WEBUIUNCHARGING, TRUE);
                        webUI_batterychange_flag = 0;
                }
                else if(0 == webUI_batterychange_flag || charging_status != s_mmi_charge_state) {
                        mmi_set_webUI_batterycharge(WEBUICHARGING, FALSE);
                        webUI_batterychange_flag = 1;
                }

        		s_mmi_battery_pers = bat_level;
                s_mmi_voltage_level = voltage_level;

                slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI %s salvikie charging voltagepower=%d g_mmi_charging_voltageEx=%d  cur_power=%d bat_level=%d s_mmi_voltage_level=%d\n ", __func__, voltagepower, g_mmi_charging_voltageEx, cur_power, bat_level, s_mmi_voltage_level);

                g_mmi_charging_voltageEx = cur_power;

				// 充电状态需要 保存下非充电状态下值, 当状态变化时
				g_mmi_voltageEx = cur_power;
                return;
        }

        if (g_mmi_voltageEx != 0) {
                cur_power = voltagepower < g_mmi_voltageEx ? voltagepower : g_mmi_voltageEx;
        } else {
                cur_power = voltagepower;
        }

        if (g_discharge_protect) {
			mmi_lowbattery_discharge_protect(voltagepower);
		}
        mmi_lowbattery_shutdown(voltagepower);
        g_mmi_voltageEx = cur_power;
		g_mmi_charging_voltageEx = cur_power;
        bat_level = get_charging_voltage_level_from_table(cur_power, FALSE);
        mmi_set_webUI_batterypers(bat_level);
        s_mmi_battery_pers = bat_level;
        s_mmi_voltage_level = mmi_get_battery_volt_percent_level(bat_level, FALSE);

        slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI %s voltagepower = %d !! cur_power = %d  !! s_mmi_battery_pers = %d s_mmi_voltage_level=%d\n ", __func__, voltagepower, cur_power, s_mmi_battery_pers, s_mmi_voltage_level);

}
#endif

static VOID mmi_battery_capacity_compare(SINT32 voltagepower)
{
	int cur_power = 0;
	int bat_level = 0;
	if (g_mmi_voltageEx != 0) {
		cur_power = voltagepower < g_mmi_voltageEx ? voltagepower : g_mmi_voltageEx;
	} else {
		cur_power = voltagepower;
	}

	if (g_discharge_protect) {
		mmi_lowbattery_discharge_protect(voltagepower);
	}
	mmi_lowbattery_shutdown(voltagepower);
	g_mmi_voltageEx = cur_power;
	bat_level = get_voltage_level_from_table(cur_power);
	mmi_set_webUI_batterypers(bat_level);
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_battery_capacity_compare voltagepower = %d !! cur_power = %d  !! bat_level = %d\n ", voltagepower, cur_power, bat_level);
	s_mmi_battery_pers = bat_level;
	s_mmi_voltage_level = mmi_set_battery_state(bat_level);

}


VOID mmi_set_mode_fast_poweron(BOOL chgflag)
{

	if (!g_fast_poweron)
		return;
	if (g_showMode == MMI_MODE_LED || g_showMode == MMI_MODE_ALL) {
		if (chgflag) {
			if (mmi_get_led_mode() == MMI_FAKE_POWEROFF_MODE || mmi_get_led_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE) {
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_battery_info  startcharging MMI_FAKE_POWEROFF_CHARGE_MODE \n\n");
				mmi_set_led_mode(MMI_FAKE_POWEROFF_CHARGE_MODE);
				mmi_set_update_flag(MMI_TASK_CTRL);
			}
		} else {
			if (mmi_get_led_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE) {
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_battery_info  discharging MMI_FAKE_POWEROFF_MODE \n\n");
				mmi_set_led_mode(MMI_FAKE_POWEROFF_MODE);
				mmi_set_update_flag(MMI_TASK_CTRL);
			}
		}
	}
#ifndef DISABLE_LCD
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		if (chgflag) {
			if (mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_MODE || mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE) {
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_battery_info  startcharging MMI_FAKE_POWEROFF_CHARGE_MODE \n\n");
				mmi_set_lcd_mode(MMI_FAKE_POWEROFF_CHARGE_MODE);
				mmi_set_update_flag(MMI_TASK_CTRL);
			}
		} else {
			if (mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE) {
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_battery_info  discharging MMI_FAKE_POWEROFF_MODE \n\n");
				mmi_set_lcd_mode(MMI_FAKE_POWEROFF_MODE);
				mmi_set_update_flag(MMI_TASK_CTRL);
			}
		}
	}
#endif
}

/**********************************************************************************
*����˵������ȡ���״̬
 ***********************************************************************************/
static VOID mmi_set_battery_info(VOID)
{
	E_zMmi_Charge_State chg_sta = STATE_CHARGE_MAX;
	int power = 0;
	chg_sta = mmi_get_charge_status();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_battery_info chg_sta = %d!!\n", chg_sta);
	s_mmi_charge_stateEx = s_mmi_charge_state;
	if (chg_sta == STATE_CHARGING) {
#if defined(QRZL_UE) && defined(JCV_HW_MZ901_V1_0)
		power = mmi_voltage_state_read();
        mmi_battery_capacity_calculate(power, TRUE);
		s_mmi_charge_state = STATE_CHARGING;
		//g_mmi_voltageEx = 0;
		mmi_set_mode_fast_poweron(TRUE);
#else
		s_mmi_charge_state = STATE_CHARGING;
		mmi_set_webUI_batterycharge(WEBUICHARGING, FALSE);
		g_mmi_voltageEx = 0;
		mmi_set_mode_fast_poweron(TRUE);
#endif
	} else if (chg_sta == STATE_FULL) {
		s_mmi_charge_state = STATE_FULL;
		g_mmi_voltageEx = 0;
		mmi_set_webUI_batterycharge(WEBUIUNCHARGING, TRUE);
		mmi_set_mode_fast_poweron(TRUE);
		if (g_charge_protect == 1) {
			system(MMI_TURN_OFF_CHG_FULL);
			slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_set_battery_info off charge full!!\n");
		}
	} else if (chg_sta == STATE_DISCHARGE) {
		s_mmi_charge_state = STATE_DISCHARGE;
		mmi_set_webUI_batterycharge(WEBUIUNCHARGING, FALSE);
		
		power = mmi_voltage_state_read();
		if (power > 0 && power < INT_MAX) {//kw 3
#if defined(QRZL_UE) && defined(JCV_HW_MZ901_V1_0)
			mmi_battery_capacity_calculate(power, FALSE);
#else
			mmi_battery_capacity_compare(power);
#endif
		}
		
		mmi_set_mode_fast_poweron(FALSE);
	} else if (chg_sta == STATE_CHARGERROR) {
		s_mmi_charge_state = STATE_DISCHARGE;
		mmi_set_webUI_batterycharge(WEBUIUNCHARGING, FALSE);

		power = mmi_voltage_state_read();
		if (power > 0 && power < INT_MAX) {//kw 3
#if defined(QRZL_UE) && defined(JCV_HW_MZ901_V1_0)
			mmi_battery_capacity_calculate(power, FALSE);
#else
			mmi_battery_capacity_compare(power);
#endif
		}
		
		mmi_set_mode_fast_poweron(FALSE);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_set_battery_info invalid charge state!!\n");
	}

}


/**********************************************************************************
*����˵������ȡUSB״̬
 ***********************************************************************************/
BOOL mmi_get_usb_insert_state()
{
	return s_mmi_usb_insert_status;
}

E_zMmi_Voltage_level mmi_get_voltage_level()
{
	return s_mmi_voltage_level;
}

E_zMmi_Charge_State mmi_get_charge_state()
{
	return s_mmi_charge_state;
}




/**********************************************************************************
*����˵������charging_netlink�׽���
 ����ֵ�� �򿪵�netlink�׽������������򿪳ɹ����ط�0ֵ
***********************************************************************************/
int open_charging_netlink()
{
	struct sockaddr_nl addr;
	int s = -1;
	memset(&addr, 0, sizeof(addr));
	addr.nl_family = AF_NETLINK;
	addr.nl_pid = getpid();
	addr.nl_groups = 0xffffffff;
	s = socket(PF_NETLINK, SOCK_DGRAM, NETLINK_KOBJECT_UEVENT);
	if (s < 0) {		
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI charging socketfail %d\n",errno);
		return -1;
	}
//  setsockopt(s, SOL_SOCKET, SO_RCVBUFFORCE, &sz, sizeof(sz));
	if (bind(s, (struct sockaddr *) &addr, sizeof(addr)) < 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI charging bindfail %d\n",errno);
		close(s);
		return -1;
	}
	return s;
}

/**********************************************************************************
*����˵����netlink���ݼ�������
��   �룺const char *key��ָ��ؼ��ֵ�ָ��
         const char *buf��ָ���ַ�����ָ��
         size_t len���ַ�������

��   ����
����ֵ�� �����ַ����ж�Ӧ�ؼ��ֵ���ʼ��ַ�������������ؼ��֣�����NULL
��   ����
***********************************************************************************/
static const char *search_netlink(const char *key, const char *buf, size_t len)
{
	size_t curlen = 0;
	size_t keylen = strlen((char *)key);
	char *cur = (char *)buf;

	while (cur < buf + len - keylen) {
		curlen = strlen(cur);
		if (curlen == 0)
			break;
		if (!strncmp(key, cur, keylen) && cur[keylen] == '=') {
			return cur + keylen + 1;
		}
		cur += (curlen + 1);
	}
	return NULL;
}

/**********************************************************************************
*����˵������netlink����������Ϣ�ַ������������Ϣ
 ***********************************************************************************/
void process_netlink_event(int netlink_fd)
{
	char buf[1024] = {0};
	int byte_counts = -1;
	const char *keys = NULL, *subsys = NULL, *action = NULL;
	const char *power_supply_name = NULL;


	byte_counts = recv(netlink_fd, buf, sizeof(buf)-1, 0);
	buf[sizeof(buf)-1] = '\0';//cov

	if (byte_counts <= 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI receive from netlonk ret %d err %d \n", byte_counts, errno);
		return;
	}

	keys = (char *)(buf + strlen((char *)buf) + 1);
	byte_counts -= (strlen((char*)buf) + 1);

	subsys = search_netlink("SUBSYSTEM", keys, byte_counts);
	action = search_netlink("ACTION", keys, byte_counts);
	power_supply_name = search_netlink("POWER_SUPPLY_NAME", keys, byte_counts);
	slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI process_netlink_event subsys=%s, action=%s, power_supply_name=%s\n", subsys, action, power_supply_name);

	if ((subsys != NULL) && (action != NULL) && (power_supply_name != NULL)) {//klocwork
		if ((!strcmp(subsys, "power_supply")) && (!(strcmp(action, "change")))) {
			if (!strcmp(power_supply_name, "charger")) {
				set_wake_lock(MMI_MAIN_LOCK_ID);
				mmi_reset_idle_timer();
				mmi_set_battery_info();
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI BATTERY process_netlink_event s_mmi_charge_state = %d s_mmi_charge_stateEx=%d\n", s_mmi_charge_state, s_mmi_charge_stateEx);
				mmi_set_mode_active();
#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
				/* USB插入时唤醒数码管显示 */
				mmi_digits_wakeup();
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI USB insert wakeup digits display\n");
#endif
				if (s_mmi_charge_state != s_mmi_charge_stateEx) {
					mmi_set_battery_update();
				}
			} else if (!strcmp(power_supply_name, "boost")) {
				if (g_discharge_protect) {
					set_wake_lock(MMI_MAIN_LOCK_ID);
					mmi_set_discharge_info();
					set_wake_unlock(MMI_MAIN_LOCK_ID);
				}
			} else {
				return;
			}
		}
	}
}

int battery_app_msg_parse(const char *msg, int msglen, struct hotplug_event *event)
{
	int byte_counts = msglen;
	const char *keys = NULL, *subsys = NULL, *action = NULL;
	const char *power_supply_name = NULL;
	
	keys = (char *)(msg + strlen((char *)msg) + 1);
	byte_counts -= (strlen((char*)msg) + 1);

	subsys = search_netlink("SUBSYSTEM", keys, byte_counts);
	action = search_netlink("ACTION", keys, byte_counts);
	power_supply_name = search_netlink("POWER_SUPPLY_NAME", keys, byte_counts);
	slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI process_netlink_event subsys=%s, action=%s, power_supply_name=%s\n", subsys, action, power_supply_name);

	if ((subsys != NULL) && (action != NULL) && (power_supply_name != NULL)) {
		if ((!strcmp(subsys, "power_supply")) && (!(strcmp(action, "change")))) {
			if (!strcmp(power_supply_name, "charger")) {
				mmi_set_battery_info();
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI BATTERY process_netlink_event s_mmi_charge_state = %d s_mmi_charge_stateEx=%d\n", s_mmi_charge_state, s_mmi_charge_stateEx);
				if (s_mmi_charge_state != s_mmi_charge_stateEx 
					&& (s_mmi_charge_state == STATE_DISCHARGE || s_mmi_charge_stateEx == STATE_DISCHARGE)) {
					set_wake_lock(MMI_MAIN_LOCK_ID);
					mmi_reset_idle_timer();
					mmi_set_mode_active();
				#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
					/* USB插入时唤醒数码管显示 */
					mmi_digits_wakeup();
					slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI USB insert wakeup digits display\n");
				#endif
				}
				if (s_mmi_charge_state != s_mmi_charge_stateEx) {
					mmi_set_battery_update();
				}
			} else if (!strcmp(power_supply_name, "boost")) {
				if (g_discharge_protect) {
					set_wake_lock(MMI_MAIN_LOCK_ID);
					mmi_set_discharge_info();
					set_wake_unlock(MMI_MAIN_LOCK_ID);
				}
			} else {
				return -1;
			}
		}
	}
	return -1;
}

/**********************************************************************************
*����˵������ѹ��ѯ��ʱ���ص�����ÿ20���һ�ε�ѹ
 ***********************************************************************************/
static VOID *mmi_batterypower_process_thread(VOID *arg)
{
	UINT32 voltagepower = 0;

	prctl(PR_SET_NAME, "mmigetbatvol", 0, 0, 0);

	while (1) {
		set_wake_lock(MMI_GET_POWER_LOCK_ID);
		
		if (g_discharge_protect) {
			voltagepower = mmi_voltage_state_read();
			if (voltagepower > 0 && voltagepower < INT_MAX) {//kw 3
		#if defined(QRZL_UE) && defined(JCV_HW_MZ901_V1_0)
				mmi_battery_capacity_calculate(voltagepower, FALSE);
		#else
				mmi_battery_capacity_compare(voltagepower);
		#endif
				mmi_set_battery_update();
			}
		} else {
			if (mmi_get_charge_state() == STATE_DISCHARGE) {
				voltagepower = mmi_voltage_state_read();
				if (voltagepower > 0 && voltagepower < INT_MAX) {//kw 3
		#if defined(QRZL_UE) && defined(JCV_HW_MZ901_V1_0)
					mmi_battery_capacity_calculate(voltagepower, FALSE);
		#else
					mmi_battery_capacity_compare(voltagepower);
		#endif
					mmi_set_battery_update();
				}
			
			} else if (mmi_get_charge_state() == STATE_CHARGING) {
				voltagepower = mmi_voltage_state_read();
				if (voltagepower > 0 && voltagepower < INT_MAX) {//kw 3
		#if defined(QRZL_UE) && defined(JCV_HW_MZ901_V1_0)
					mmi_battery_capacity_calculate(voltagepower, TRUE);
		#else
					mmi_battery_capacity_compare(voltagepower);
		#endif
					mmi_set_battery_update();
				}
			} else {
				/*������������µĵ͵籣��*/
				if (g_temp_protect) {
					voltagepower = mmi_voltage_state_read();
					if (voltagepower > 0 && voltagepower < INT_MAX) {//kw 3
						mmi_lowbattery_shutdown(voltagepower);
					}
				}
			}
		}
		set_wake_unlock(MMI_GET_POWER_LOCK_ID);
		mmi_sleep(CHECK_POWER_TIME_INTERVAL);
	}
}

/**********************************************************************************
*����˵����������ѹ��ѯ�߳�
 ***********************************************************************************/
static VOID mmi_create_get_voltage_thread(VOID)
{
	pthread_t mmi_battryvol_thread;
	if (pthread_create(&mmi_battryvol_thread, NULL, &mmi_batterypower_process_thread, NULL) == -1) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI pthread_create BatteryPower_thread error\n");
		return;
	}
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_create_get_voltage_timer success!!\n");
}
/**********************************************************************************
*����˵���������������߳�
 ***********************************************************************************/
void *mmi_chargingcheck_process_thread(void *arg)
{
	int charging_netlink = -1;
	int fd_counts = -1;
	fd_set readfds;
	prctl(PR_SET_NAME, "mmichgcheck", 0, 0, 0);
	if ((charging_netlink = open_charging_netlink()) < 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI charging_netlink wrong \n");
		return NULL;
	}
	while (1) {
/*		FD_ZERO(&readfds);
		FD_SET(charging_netlink, &readfds);

		fd_counts = select(charging_netlink + 1, &readfds, NULL, NULL, NULL);

		if (fd_counts < 0) {
			slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI select usb_netlink error! \n");
			continue;
		} else if (fd_counts == 0) {
			slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI select usb_netlink timeout\n");
			continue;
		} else {
			if (charging_netlink > 0 && FD_ISSET(charging_netlink, &readfds)) {*/
				process_netlink_event(charging_netlink);
			//}
		//}
	}
}

/**********************************************************************************
*����˵�����������״̬����߳�
 ***********************************************************************************/
static VOID mmi_create_chargestate_check_thread(VOID)
{
	pthread_t mmi_chgstate_thread;
	if (pthread_create(&mmi_chgstate_thread, NULL, &mmi_chargingcheck_process_thread, NULL) == -1) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_create_chargestate_check_thread error\n");
		return;
	}
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_create_chargestate_check_thread success!!!\n");

}

/**************************************************************************************
*����˵��:��ʼ�����״̬ ���õ�ص�
***************************************************************************************/
static VOID mmi_charge_state_init(VOID)
{
	mmi_set_battery_info();
	mmi_set_battery_update();
}

/**************************************************************************************
*����˵��:��ʼ����ص�״̬
***************************************************************************************/
VOID mmi_battery_init(VOID)
{

	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI BATTERY mmi_battery_Led_Init begin!!\n");
#ifdef JCV_CHARLIEPLEX_LED_DIGITS
	//初始化 把数码管都熄灭
	mmi_file_operate(CHARLIEPLEX_LED_DIGITS_DEV_PATH, "OFF");
#endif
	mmi_charge_state_init();
	if (g_discharge_protect) {
		mmi_load_state_init();
	}
	//mmi_create_chargestate_check_thread();
	hotplug_parse_register(DEVICE_TYPE_APP_MMI_BATTERY, battery_app_msg_parse);
	mmi_create_get_voltage_thread();
}
