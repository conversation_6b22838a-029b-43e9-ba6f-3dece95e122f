# WiFiDog 故障排除指南

## 🚨 当前问题分析

根据您提供的日志，发现以下问题：

### 1. SSL 支持问题
```
[4] SSLPeerVerification is set but no SSL compiled in. Ignoring!
[4] SSLCertPath is set but not SSL compiled in. Ignoring!
```
**原因**: WiFiDog 编译时没有包含 SSL 支持
**影响**: 无法使用 HTTPS 连接认证服务器

### 2. 域名解析问题
```
iptables v1.4.3.2: host/network `msftconnecttest.com' not found
```
**原因**: iptables 无法解析域名
**影响**: 防火墙规则添加失败

### 3. 认证服务器连接问题
```
[4] Auth server did NOT say Pong!
```
**原因**: 无法连接到 wireless.cmonelink.com
**影响**: 认证功能无法正常工作

## 🔧 解决方案

### 方案1: 使用修复后的配置（推荐）

1. **使用修复后的配置文件**:
```bash
# 复制修复后的配置
cp wifidog_onelink_test.conf /etc_rw/wifidog_onelink_test.conf

# 重新启动 WiFiDog
/tmp/wifidog -f -c /etc_rw/wifidog_onelink_test.conf
```

**修复内容**:
- ✅ 禁用 SSL 配置
- ✅ 使用 HTTP 端口 80
- ✅ 防火墙规则使用 IP 地址而不是域名

### 方案2: 使用本地测试服务器（最稳定）

1. **启动本地认证服务器**:
```bash
chmod +x simple_auth_test.sh
./simple_auth_test.sh &
```

2. **使用本地测试配置**:
```bash
# 使用本地测试配置
/tmp/wifidog -f -c wifidog_local_test.conf
```

### 方案3: 添加信任 MAC 地址（快速测试）

如果只是想快速测试基本功能：

```bash
# 获取测试设备的 MAC 地址
arp -a | grep 192.168.100

# 在配置文件中添加信任 MAC
echo "TrustedMACList aa:bb:cc:dd:ee:ff" >> /etc_rw/wifidog_onelink_test.conf
```

## 🔍 诊断步骤

### 1. 运行诊断脚本
```bash
chmod +x diagnose_wifidog.sh
./diagnose_wifidog.sh
```

### 2. 检查网络基础设施
```bash
# 检查 br0 接口
ifconfig br0

# 检查路由
route -n

# 测试 DNS
nslookup wireless.cmonelink.com

# 测试网络连通性
ping *******
```

### 3. 检查 WiFiDog 状态
```bash
# 检查进程
ps aux | grep wifidog

# 检查端口监听
netstat -ln | grep 2060

# 检查 iptables 规则
iptables -L | grep WiFiDog
```

## 📋 完整修复流程

### 步骤1: 停止当前 WiFiDog
```bash
# 找到 WiFiDog 进程
ps aux | grep wifidog

# 停止进程
kill <PID>

# 或者使用 wdctl
wdctl stop
```

### 步骤2: 清理 iptables 规则
```bash
# 清理 WiFiDog 相关规则
iptables -F WiFiDog_br0_Internet 2>/dev/null
iptables -F WiFiDog_br0_Known 2>/dev/null
iptables -F WiFiDog_br0_Unknown 2>/dev/null
iptables -F WiFiDog_br0_Locked 2>/dev/null
iptables -F WiFiDog_br0_Validating 2>/dev/null

# 删除链
iptables -X WiFiDog_br0_Internet 2>/dev/null
iptables -X WiFiDog_br0_Known 2>/dev/null
iptables -X WiFiDog_br0_Unknown 2>/dev/null
iptables -X WiFiDog_br0_Locked 2>/dev/null
iptables -X WiFiDog_br0_Validating 2>/dev/null
```

### 步骤3: 使用修复后的配置
```bash
# 方案A: 使用修复后的 OneLink 配置
/tmp/wifidog -f -c wifidog_onelink_test.conf

# 方案B: 使用本地测试配置
./simple_auth_test.sh &
/tmp/wifidog -f -c wifidog_local_test.conf
```

### 步骤4: 验证功能
```bash
# 检查 WiFiDog 状态
wdctl status

# 检查客户端
wdctl clients

# 测试认证服务器
curl http://*************:8080/wifidog/ping
```

## 🎯 预期结果

修复后，您应该看到：

```
[6] Reading configuration file '/etc_rw/wifidog_onelink_test.conf'
[5] Creating web server on *************:2060
[6] Initializing Firewall
[5] Waiting for connections
[6] ONLINE status became ON
[6] AUTH_ONLINE status became ON
```

**没有以下错误**:
- ❌ SSL 相关警告
- ❌ iptables 域名解析错误
- ❌ "Auth server did NOT say Pong!" 错误

## 🔄 持续监控

### 监控命令
```bash
# 实时查看日志
tail -f /var/log/messages | grep wifidog

# 监控客户端连接
watch -n 5 'wdctl clients'

# 监控网络流量
watch -n 5 'iptables -L WiFiDog_br0_Internet -n -v'
```

### 常见问题处理

1. **客户端无法重定向**:
   - 检查防火墙规则
   - 确认网关地址配置
   - 验证 captive portal 检测

2. **认证后无法上网**:
   - 检查路由配置
   - 确认 NAT 规则
   - 验证上游连接

3. **频繁断线**:
   - 调整 CheckInterval
   - 检查网络稳定性
   - 优化防火墙规则

## 📞 技术支持

如果问题仍然存在，请提供：
1. 诊断脚本输出
2. 完整的 WiFiDog 启动日志
3. 网络拓扑信息
4. 客户端设备类型和行为
