#ifndef __QRZL_JIUYAO_AUTH_CONTROL_H_
#define __QRZL_JIUYAO_AUTH_CONTROL_H_

typedef struct {
    char code[30];           // 200 表示成功， -1 表示失败
    char msg[128];      // 错误或成功信息
} JiuYaoCurlResult;

// JIUYAO 获取sign信息接口
JiuYaoCurlResult jiuyao_get_sign_info(const char *mac, const char *terminalMac, char *callback_url);

// 获取已认证的设备
char *jiuyao_check_device_authed();
/**
 * 判断mac是否已认证
 * @param mac 终端mac
 */
int jiuyao_is_mac_authed(const char *mac);

// JIUYAO 设备上/下线上报
void jiuyao_device_line_type_push(const char *type, const char *mac, const char *terminalMac);

// 异常页面字符串返回
char *jiuyao_exception_page(const char *msg, const char *code);

#endif