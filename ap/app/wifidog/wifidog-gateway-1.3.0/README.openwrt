$Id$

OpenWRT specific README
=======================

So, you want to run wifidog on one of linksys's WRT wireless routers!

OpenWRT is the embedded linux-gnu bundle that runs on the linksys WRT
series routers (among numerous others).

OpenWRT's home page is http://openwrt.org/

---- I just want to RUN the thing: ----
-DO NOT use the wifidog packages distributed by OpenWRT (you are asking for trouble, they are broken in various ways; you will get no support if you do)
-Use the official wifidog packages on sourceforge (currently only available for whiterussian.

---- I want to develop and test on OpenWRT ----

To build wifidog so that it may be run on the linksys wrt routers you
must first obtain the OpenWRT toolchain.  This toolchain is a set of
compilers and other software development tools that will allow you,
running on your intel/pentium/mac computer to compile and develop software
that is to run on the mips based linksys wrt series routers, which is
based on another computer cpu chip entirely.

You have several options for building wifidog using the OpenWRT toolchain.

Option 1. get the prebuilt, minimal OpenWRT toolchain (The OpenWRT SDK), and give the makefile it's path.  This is the best option, assuming you have a x86_64 Os (the SDK is distributed only for x86_64).

For OpenWRT 0.9 (Whiterussian):
	cd ~ 
	wget http://downloads.openwrt.org/whiterussian/newest/OpenWrt-SDK-Linux-i686-1.tar.bz2
	tar -jxvf OpenWrt-SDK-Linux-i686-1.tar.bz2
	cd wifidog
	make whiterussianipk OPENWRTSDK=~/OpenWrt-SDK-Linux-i686-1/

For OpenWRT Kamikaze up till 7.09:
	cd ~ 
	wget http://downloads.openwrt.org/kamikaze/7.09/brcm-2.4/OpenWrt-SDK-brcm-2.4-for-Linux-x86_64.tar.bz2
	tar -jxvf OpenWrt-SDK-brcm-2.4-for-Linux-x86_64.tar.bz2
	cd wifidog
	make kamikazeipk OPENWRTSDK=~/OpenWrt-SDK-brcm-2.4-for-Linux-x86_64
	
For OpenWRT Kamikaze 8.09 and up, there is no SDK available and the 7.09 SDK does not work.  So Option 1 is not an option.  Option 2 is not an option either since building the SDK did not seem to work (https://forum.openwrt.org/viewtopic.php?id=17879).  So jump to Option 3!

   If it works (!) you will have an ipkg file in $(OPENWRTSDK)/bin/packages/
   You can then boot up your OpenWrt
   router, copy the .ipk to it, and install it using the ipkg commands.

   You should also make sure that the wifidog prereqs are already
   installed on the router before you try to run wifidog.  Note that if you build the 
   packages with the instructions above, they will download the required dependencies auomatically (if you have an internet connecion on yout router) and will refuse to install without them.

   The prereqs are:
   * iptables command and modules mac, mark and MARK
   * iptables kernel module mac
   * libpthread

   These are all packages you can install on your running OpenWrt router
   using the ipkg commands.  If the router is on the net, the ipkg
   commands can download the packages from www.openwrt.org, just like
   debian apt-get or fedora yum or up2date.

Option 2. Build your own SDK (or find someone to do it for you)
cd ~
wget http://downloads.openwrt.org/kamikaze/7.09/kamikaze_7.09.tar.bz2
tar -jxvf kamikaze_7.09.tar.bz2
cd kamikaze_7.09
make menuconfig #(Make sure you build the SDK in "special targets")
make #(could take hours downloading and compiling all dependencies)
Follow the instructions in Option 1, using the SDK you build instead of downloading it.

Option 3. Use the full buildroot directly (time consuming...)
   cd wifidog
   make dist
   cd ~
   wget http://downloads.openwrt.org/kamikaze/7.09/kamikaze_7.09.tar.bz2
   tar -jxvf kamikaze_7.09.tar.bz2
   cp -R wifidog/contrib/build-openwrt-kamikazeipk/wifidog kamikaze_7.09/package/
   cp wifidog/wifidog-1.1.5.tar.gz kamikaze_7.09/dl/
   cd kamikaze_7.09
   make menuconfig #(Follow instructions on OpenWRT's site to setup your buildroot for your platform)
   make #(could take hours downloading and compiling all dependencies)

For Kamikaze 8.09 and up, there is an extra dependency to add to the package, so here would be the new procedure
   cd wifidog
   make dist
   cd ~
   wget http://downloads.openwrt.org/kamikaze/8.09/kamikaze_8.09.tar.bz2
   tar -jxvf kamikaze_8.09.tar.bz2
   cp -R wifidog/contrib/build-openwrt-kamikazeipk8.09up/wifidog kamikaze_8.09/package/
   cp wifidog/wifidog-1.1.5.tar.gz kamikaze_8.09/dl/
   cd kamikaze_8.09
   make menuconfig #(Follow instructions on OpenWRT's site to setup your buildroot for your platform)
   make #(could take hours downloading and compiling all dependencies)

