#include <stdio.h>      // 可选（printf、perror）
#include <fcntl.h>      // open、O_WRONLY、O_TRUNC
#include <unistd.h>     // write、close
#include <string.h>     // strlen
#include <errno.h>
#include <strings.h>  // 支持 strcasecmp


#include	"wsIntrn.h"

#include "qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"
#include "cloud_control/cmp_auth_control.h"

#define PROC_AUTH_PATH "/proc/cjportal/auth"
#define NV_BUF_LEN 5048  // NV字符串最大长度



extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;


static sym_fd_t	formSymtab = -1;

void websHeader(webs_t wp)
{
	a_assert(websValid(wp));

	websWrite(wp, T("HTTP/1.0 200 OK\n"));

	websWrite(wp, T("Server: %s\r\n"), WEBS_NAME);
#ifdef WEBINSPECT_FIX
	websWrite(wp, T("X-Frame-Options: SAMEORIGIN\n"));
#endif	
	websWrite(wp, T("Pragma: no-cache\n"));
	websWrite(wp, T("Cache-control: no-cache\n"));
	websWrite(wp, T("Content-Type: text/html\n"));
	websWrite(wp, T("\n"));
	websWrite(wp, T("<html>\n"));
}


int websFormDefine(char_t *name, void (*fn)(webs_t wp, char_t *path, 
	char_t *query))
{
	a_assert(name && *name);
	a_assert(fn);

	if (fn == NULL) {
		return -1;
	}

	symEnter(formSymtab, name, valueInteger((int) fn), (int) NULL);
	return 0;
}

void websFooter(webs_t wp)
{
	a_assert(websValid(wp));

	websWrite(wp, T("</html>\n"));
}

int websFormHandler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, 
	char_t *url, char_t *path, char_t *query)
{	
	char_t		form_buf[FNAMESIZE];
	char_t		*cp, *form_name;
	sym_t		*sp;
	int			(*fn)(void *sock, char_t *path, char_t *args);

	a_assert(websValid(wp));
	a_assert(url && *url);
	a_assert(path && *path == '/');

	websStats.formHits++;
#ifdef WEBS_SECURITY
	if (strstr(query,"_method")) {
		printf("websFH: qry=%s\n",query);
		websError(wp, 405, T(""));
		return 1;
	}
#endif

	gstrncpy(form_buf, path, TSZ(form_buf)-1);
	if ((form_name = gstrchr(&form_buf[1], '/')) == NULL) {
		websError(wp, 200, T("Missing form name"));
		return 1;
	}
	form_name++;
	if ((cp = gstrchr(form_name, '/')) != NULL) {
		*cp = '\0';
	}

	sp = symLookup(formSymtab, form_name);
	if (sp == NULL) {
#ifdef WEBINSPECT_FIX
		websDone(wp, 0);
#else
		websError(wp, 200, T("Form %s is not defined"), form_name);
#endif		
	} else {
		fn = (int (*)(void *, char_t *, char_t *)) sp->content.value.integer;
		a_assert(fn);
		if (fn) {
			(*fn)((void*) wp, form_name, query);
		}
	}
	return 1;
}


void websFormClose()
{
	if (formSymtab != -1) {
		symClose(formSymtab);
		formSymtab = -1;
	}
}


void websFormOpen()
{
	formSymtab = symOpen(WEBS_SYM_INIT);
}

// 写入/proc文件系统授权
static int write_mac_to_proc(const char *mac) {
#ifdef ZXIC_ONELINK_TEST
    // 清理该MAC地址相关的所有iptables规则
	char cmd_allow[256];
	snprintf(cmd_allow, sizeof(cmd_allow), "/sbin/one_link_authenticated.sh allow %s", mac);
	system(cmd_allow);
#else
    int fd = open(PROC_AUTH_PATH, O_WRONLY | O_TRUNC);
    if (fd < 0) {
        printf("Failed to open %s: %s\n", PROC_AUTH_PATH, strerror(errno));
        return -1;
    }

    char line[64];
    int len = snprintf(line, sizeof(line), "%s\n", mac);

    if (write(fd, line, len) < 0) {
        printf("Failed to write to %s: %s\n", PROC_AUTH_PATH, strerror(errno));
        close(fd);
        return -1;
    }

    close(fd);
#endif
    return 0;
}

/**
 * 在 mac_list 末尾添加一个 mac（如果不存在）
 * mac_list: 存储所有mac的字符串 (以 ; 分隔, 末尾也有 ;)
 * new_mac: 要添加的mac
 * 返回值: 0 表示已存在，1 表示添加成功，-1 表示缓冲区不足
 */
int nv_add_mac(char *mac_list, const char *new_mac, size_t buf_size) {
    char temp[64];
    size_t len;

    if (!mac_list || !new_mac) return -1;

    // 构造 "mac;" 格式，避免匹配到类似前缀
    snprintf(temp, sizeof(temp), "%s;", new_mac);

    // 如果已存在则直接返回
    if (strstr(mac_list, temp) != NULL) {
        return 0; // 已存在
    }

    len = strlen(mac_list);
    if (len + strlen(temp) >= buf_size) {
        return -1; // 缓冲区不足
    }

    // 追加到末尾
    strcat(mac_list, temp);
    return 1; // 添加成功
}

/**************************************************************
 * 电信认证回调接口
 *************************************************************/
void CMPauthCallbackHandler(webs_t wp)
{
    char terminalMac_out[32] = {0};
    const char *token = websGetVar(wp, T("token"), T("NULL"));
    const char *mac = websGetVar(wp, T("mac"), T("NULL"));
    const char *terminalMac = websGetVar(wp, T("terminalMac"), T("NULL"));

    printf("CALLBACK -> AUTH token: %s\n", token);
    printf("CALLBACK -> AUTH mac (device): %s\n", mac);
    printf("CALLBACK -> AUTH terminalMac (phone): %s\n", terminalMac);

    if (strcmp(token, "NULL") != 0 && strcmp(terminalMac, "NULL") != 0) {
        printf("CALLBACK -> 认证成功, 正在加入授权文件\n");

        if (convert_mac_format(terminalMac, terminalMac_out, sizeof(terminalMac_out), ':') >= 0) {
            if (write_mac_to_proc(terminalMac_out) < 0) {
                printf("CALLBACK -> %s 加入授权文件失败\n", terminalMac_out);
                websRedirect(wp, "/auth_failed.html");
            } else {
                printf("CALLBACK -> %s 加入授权文件成功\n", terminalMac_out);

                // 保存 NV
                // char nv_old[NV_BUF_LEN] = {0};
                // cfg_get_item("cmp_authed_mac", nv_old, sizeof(nv_old));
                char mac_nv[32] = {0};
                convert_mac_format(terminalMac, mac_nv, sizeof(mac_nv), ':');
				// int save_res = nv_add_mac(nv_old, mac_nv, sizeof(nv_old));
				int save_res = update_authed_mac_list("cmp_authed_mac", mac_nv);
				if (save_res == 0) {
					// nv_set_item(NV_RO, "cmp_authed_mac", nv_old, 1);
					// cfg_set("cmp_authed_mac", nv_old);
					// nv_commit(NV_RO);
					// printf("CALLBACK -> cmp_authed_mac 更新为: %s\n", nv_old);
					websRedirect(wp, "/auth_success.html");  // 成功跳转
				} else {
					printf("CALLBACK -> cmp_authed_mac 长度过长，或已存在. code: %s\n", save_res);
            		websRedirect(wp, "/auth_failed.html");
				}
            }
        } else {
            printf("CALLBACK -> MAC 地址格式转换失败\n");
            websRedirect(wp, "/auth_failed.html");
        }
    } else {
        printf("CALLBACK -> token 或 terminalMac 缺失，认证失败\n");
        websRedirect(wp, "/auth_failed.html");
    }
}

/**************************************************************
 * 移动认证回调接口
 *************************************************************/
void One_Link_authCallbackHandler(webs_t wp)
{
    char terminalMac_out[32] = {0};
    const char *token = websGetVar(wp, T("token"), T("NULL"));
    const char *mac = websGetVar(wp, T("mac"), T("NULL"));
    const char *terminalMac = websGetVar(wp, T("terminalMac"), T("NULL"));

    printf("CALLBACK -> AUTH token: %s\n", token);
    printf("CALLBACK -> AUTH mac (device): %s\n", mac);
    printf("CALLBACK -> AUTH terminalMac (phone): %s\n", terminalMac);

    if (strcmp(token, "NULL") != 0 && strcmp(terminalMac, "NULL") != 0) {
        printf("CALLBACK -> 认证成功, 正在加入授权文件\n");

        if (convert_mac_format(terminalMac, terminalMac_out, sizeof(terminalMac_out), ':') >= 0) {
            if (write_mac_to_proc(terminalMac_out) < 0) {
                printf("CALLBACK -> %s 加入授权文件失败\n", terminalMac_out);
                websRedirect(wp, "/auth_failed.html");
            } else {
                printf("CALLBACK -> %s 加入授权文件成功\n", terminalMac_out);

                // 保存 NV
                // char nv_old[NV_BUF_LEN] = {0};
                // cfg_get_item("one_link_authed_mac", nv_old, sizeof(nv_old));
                char mac_nv[32] = {0};
                convert_mac_format(terminalMac, mac_nv, sizeof(mac_nv), ':');
				// int save_res = nv_add_mac(nv_old, mac_nv, sizeof(nv_old));
				int save_res = update_authed_mac_list("one_link_authed_mac", mac_nv);
				if (save_res == 0) {
					// nv_set_item(NV_RO, "one_link_authed_mac", nv_old, 1);
					// cfg_set("one_link_authed_mac", nv_old);
					// nv_commit(NV_RO);
					// printf("CALLBACK -> one_link_authed_mac 更新为: %s\n", nv_old);
					websRedirect(wp, "/auth_success.html");  // 成功跳转
				} else {
					printf("CALLBACK -> one_link_authed_mac 长度过长，或已存在. code: %s\n", save_res);
            		websRedirect(wp, "/auth_failed.html");
				}
            }
        } else {
            printf("CALLBACK -> MAC 地址格式转换失败\n");
            websRedirect(wp, "/auth_failed.html");
        }
    } else {
        printf("CALLBACK -> token 或 terminalMac 缺失，认证失败\n");
        websRedirect(wp, "/auth_failed.html");
    }
}


void MY_authCallbackHandler(webs_t wp)
{
    char terminalMac_out[32] = {0};
    const char *token = websGetVar(wp, T("codeToken"), T("NULL"));
    const char *terminalMac = websGetVar(wp, T("terminalMac"), T("NULL"));

    printf("CALLBACK -> AUTH token: %s\n", token);
    printf("CALLBACK -> AUTH terminalMac (phone): %s\n", terminalMac);

    if (strcmp(token, "NULL") != 0 && strcmp(terminalMac, "NULL") != 0) {
        printf("CALLBACK -> 认证成功, 正在加入授权文件\n");

        if (convert_mac_format(terminalMac, terminalMac_out, sizeof(terminalMac_out), ':') >= 0) {
            if (write_mac_to_proc(terminalMac_out) < 0) {
                printf("CALLBACK -> %s 加入授权文件失败\n", terminalMac_out);
                // websRedirect(wp, "/auth_failed.html");
            } else {
                printf("CALLBACK -> %s 加入授权文件成功\n", terminalMac_out);

                char mac_nv[32] = {0};
                convert_mac_format(terminalMac, mac_nv, sizeof(mac_nv), ':');

                // 获取运营商
                char save_authed_nv_name[25] = {0};
                char imsi_str[32] = {0};
                int isp = 0;
                cfg_get_item("sim_imsi", imsi_str, sizeof(imsi_str));
                isp = get_isp_by_imsi(imsi_str);

                printf("imsi_str: %s", imsi_str);
                printf("isp: %d", isp);

                switch (isp)
                {
                    case 1:
                        // 移动
                        snprintf(save_authed_nv_name, sizeof(save_authed_nv_name), "%s", "one_link_authed_mac");
                        break;
                    case 2:
                        // 联通
                        break;
                    case 3:
                        // 电信
                        snprintf(save_authed_nv_name, sizeof(save_authed_nv_name), "%s", "cmp_authed_mac");
                        break;
                    default:
                        break;
                }

				int save_res = update_authed_mac_list(save_authed_nv_name, mac_nv);
				if (save_res == 0) {
					// nv_set_item(NV_RO, "one_link_authed_mac", nv_old, 1);
					// cfg_set("one_link_authed_mac", nv_old);
					// nv_commit(NV_RO);
					// printf("CALLBACK -> one_link_authed_mac 更新为: %s\n", nv_old);
					websRedirect(wp, "/auth_success.html");  // 成功跳转
				} else {
					printf("CALLBACK -> one_link_authed_mac 长度过长，或已存在. code: %s\n", save_res);
            		websRedirect(wp, "/auth_failed.html");
				}
            }
        } else {
            printf("CALLBACK -> MAC 地址格式转换失败\n");
            websRedirect(wp, "/auth_failed.html");
        }
    } else {
        printf("CALLBACK -> token 或 terminalMac 缺失，认证失败\n");
        websRedirect(wp, "/auth_failed.html");
    }

}

void BW_authCallbackHandler(webs_t wp)
{
    // status=success&iccid=89860405192580177831&mac=3c%3A68%3A01%3Acb%3A44%3Ac4&terminalMac=3c%3A68%3A01%3Ada%3A87%3A04
    
    char terminalMac_out[32] = {0};

    const char *status = websGetVar(wp, T("status"), T("NULL"));
    const char *terminalMac = websGetVar(wp, T("terminalMac"), T("NULL"));

    printf("CALLBACK -> AUTH status: %s\n", status);
    printf("CALLBACK -> AUTH terminalMac (phone): %s\n", terminalMac);

    if (strcmp(status, "success") == 0 && strcmp(terminalMac, "NULL") != 0) {
        // 将terminalMac url解码
        char terminalMac_decoded[32] = {0};
        url_decode(terminalMac, terminalMac_decoded, sizeof(terminalMac_decoded));
        
        if (convert_mac_format(terminalMac_decoded, terminalMac_out, sizeof(terminalMac_out), ':') >= 0) {
            if (write_mac_to_proc(terminalMac_out) < 0) {
                printf("CALLBACK -> %s 加入授权文件失败\n", terminalMac_out);
                websRedirect(wp, "/auth_failed.html");
            } else {
                printf("CALLBACK -> %s 加入授权文件成功\n", terminalMac_out);

                char mac_nv[32] = {0};
                convert_mac_format(terminalMac_decoded, mac_nv, sizeof(mac_nv), ':');

                // 获取运营商
                char save_authed_nv_name[25] = {0};
                char imsi_str[32] = {0};
                int isp = 0;
                cfg_get_item("sim_imsi", imsi_str, sizeof(imsi_str));
                isp = get_isp_by_imsi(imsi_str);

                printf("imsi_str: %s", imsi_str);
                printf("isp: %d", isp);

                switch (isp)
                {
                    case 1:
                        // 移动
                        snprintf(save_authed_nv_name, sizeof(save_authed_nv_name), "%s", "one_link_authed_mac");
                        break;
                    case 2:
                        // 联通
                        break;
                    case 3:
                        // 电信
                        snprintf(save_authed_nv_name, sizeof(save_authed_nv_name), "%s", "cmp_authed_mac");
                        break;
                    default:
                        break;
                }

				int save_res = update_authed_mac_list(save_authed_nv_name, mac_nv);
				if (save_res == 0) {
					websRedirect(wp, "/auth_success.html");  // 成功跳转
				} else {
					printf("CALLBACK -> one_link_authed_mac 长度过长，或已存在. code: %s\n", save_res);
            		websRedirect(wp, "/auth_failed.html");
				}
            }
        } else {
            printf("CALLBACK -> MAC 地址格式转换失败\n");
            websRedirect(wp, "/auth_failed.html");
        }
    } else {
        printf("CALLBACK -> token 或 terminalMac 缺失，认证失败\n");
        websRedirect(wp, "/auth_failed.html");
    }
}