#ifndef __QRZL_CHUANGSAN_AUTH_CONTROL_H_
#define __QRZL_CHUANGSAN_AUTH_CONTROL_H_


/**
 * 获取已认证信息
 */
char *cs_one_link_get_authed();

/**
 * 发送短信
 * @param iccid 
 * @param mac 设备mac
 * @param terminalMac 终端mac
 * @param phoneNum 手机号码
 * @return -1 请求失败， 0 请求成功
 */
int cs_one_link_send_sms(char *iccid, char *mac, char *terminalMac, char *phoneNum);

/**
 * 终端认证
 */
int cs_one_link_verify(char *iccid, char *mac, char *terminalMac, char *phoneNum, char *verifyCode);

/**
 * 设备上下线上报
 */
int cs_one_link_on_offline(int push_type, char *iccid, char *mac, char *terminalMac, char *terminalIp);

#endif