# $Id$
# WiFiDog Configuration file

# Parameter: GatewayID
# Default: default
# Optional
#
# Set this to the node ID on the auth server
# This is used to give a customized login page to the clients and for
# monitoring/statistics purpose. If you run multiple gateways on the same
# machine each gateway needs to have a different gateway id.
# If none is supplied, the mac address of the GatewayInterface interface will be used,
# without the : separators

# GatewayID default

# Parameter: ExternalInterface
# Default: NONE
# Optional
#
# Set this to the external interface (the one going out to the Inernet or your larger LAN).  
# Typically vlan1 for OpenWrt, and eth0 or ppp0 otherwise,
# Normally autodetected

# ExternalInterface eth0

# Parameter: GatewayInterface
# Default: NONE
# Mandatory
#
# Set this to the internal interface (typically your wifi interface).    
# Typically br-lan for Openwrt (by default the wifi interface is bridged with wired lan in openwrt)
# and eth1, wlan0, ath0, etc. otherwise
# You can get this interface with the ifconfig command and finding your wifi interface

GatewayInterface br-lan

# Parameter: GatewayAddress
# Default: Find it from GatewayInterface
# Optional
#
# Set this to the internal IP address of the gateway.  Not normally required.

# GatewayAddress ***********

# Parameter: HtmlMessageFile
# Default: wifidog-msg.html
# Optional
#
# This allows you to specify a custome HTML file which will be used for
# system errors by the gateway. Any $title, $message and $node variables
# used inside the file will be replaced.
#
# HtmlMessageFile /opt/wifidog/etc/wifidog-.html

# Parameter: AuthServer
# Default: NONE
# Mandatory, repeatable
#
# This allows you to configure your auth server(s).  Each one will be tried in order, untill one responds.
# Set this to the hostname or IP of your auth server(s), the path where
# WiFiDog-auth resides in and the port it listens on.
#AuthServer {
#	Hostname                 (Mandatory; Default: NONE)
#	SSLAvailable             (Optional; Default: no; Possible values: yes, no)
#	SSLPort                  (Optional; Default: 443)
#	HTTPPort                 (Optional; Default: 80)
#	Path                     (Optional; Default: /wifidog/ Note:  The path must be both prefixed and suffixed by /.  Use a single / for server root.)
#   LoginScriptPathFragment  (Optional; Default: login/? Note:  This is the script the user will be sent to for login.)
#   PortalScriptPathFragment (Optional; Default: portal/? Note:  This is the script the user will be sent to after a successfull login.)
#   MsgScriptPathFragment    (Optional; Default: gw_message.php? Note:  This is the script the user will be sent to upon error to read a readable message.)
#   PingScriptPathFragment    (Optional; Default: ping/? Note:  This is the wifidog-ping protocol. See http://dev.wifidog.org/wiki/doc/developer/WiFiDogProtocol_V1)
#   AuthScriptPathFragment    (Optional; Default: auth/? Note:  This is the wifidog-auth protocol. See http://dev.wifidog.org/wiki/doc/developer/WiFiDogProtocol_V1)
#}
# If SSLAvailable is set, then the client will be redirected to the
# auth daemon on its HTTPS port. If Wifidog is compiled with SSL support,
# then Wifidog will also use HTTPS to talk to the auth server instead of
# plain HTTP.
#

#AuthServer {
#    Hostname auth.ilesansfil.org
#    SSLAvailable yes
#    Path /
#}

#AuthServer {
#    Hostname auth2.ilesansfil.org
#    SSLAvailable yes
#    Path /
#}

# Parameter: DeltaTraffic
# Default: no
# Optional
#
# Set this to true if you want to reset each user's traffic (Outgoing and Incoming) value after each Auth operation.
# If this is enabled, Wifidog will add two new parameters to the AuthScriptPathFragment: Incoming_Delta, Outgoing_delta. 
# DeltaTraffic no

# Parameter: Daemon
# Default: 1
# Optional
#
# Set this to true if you want to run as a daemon
# Daemon 1

# Parameter: GatewayPort
# Default: 2060
# Optional
#
# Listen on this port
# GatewayPort 2060

# Parameter: ProxyPort
# Default: 0 (disable)
# Optional
#
# Redirect http traffic of knowns & probations users
# to a local transparent proxy listening on ProxyPort port
# ProxyPort 0

# Parameter: HTTPDName
# Default: WiFiDog
# Optional
#
# Define what name the HTTPD server will respond
# HTTPDName WiFiDog

# Parameter: HTTPDMaxConn
# Default: 10
# Optional
#
# How many sockets to listen to
# HTTPDMaxConn 10

# Parameter: HTTPDRealm
# Default: WiFiDog
# Optional
#
# The name of the HTTP authentication realm. This only used when a user
# tries to access a protected WiFiDog internal page. See HTTPUserName.
# HTTPDRealm WiFiDog

# Parameter: HTTPDUserName / HTTPDPassword
# Default: unset
# Optional
#
# The gateway exposes some information such as the status page through its web
# interface. This information can be protected with a username and password,
# which can be set through the HTTPDUserName and HTTPDPassword parameters.
# HTTPDUserName admin
# HTTPDPassword secret

# Parameter: CheckInterval
# Default: 60
# Optional
#
# How many seconds should we wait between timeout checks.  This is also
# how often the gateway will ping the auth server and how often it will
# update the traffic counters on the auth server.  Setting this too low
# wastes bandwidth, setting this too high will cause the gateway to take 
# a long time to switch to it's backup auth server(s).

# CheckInterval 60

# Parameter: ClientTimeout
# Default: 5
# Optional
#
# Set this to the desired of number of CheckInterval of inactivity before a client is logged out
# The timeout will be INTERVAL * TIMEOUT
ClientTimeout 5

# Parameter: SSLPeerVerification
# Default: yes
# Optional
#
# Enable peer certificate verification when talking to the auth
# server over SSL/TLS. Disabling this setting is mainly useful if
# you do not want  to install ca-certificates.
#
# If this setting is set to yes, then the certificates in
# the directory indicated by SSLCertPath will be used to
# verify the auth server.
#
# This setting requires that WifiDog is compiled with SSL support.
# It will be ignored otherwise.
#
# To disable SSL completely for testing purposes, set SSLAvailable
# to False for the auth server in question. Note that this will disable
# HTTPS when redirecting clients to your auth server.
#
# SSLPeerVerification yes

# Parameter: SSLCertPath
# Default: /etc/ssl/certs/
# Optional
#
# Where to look for SSL certificates to verify the auth servers
# certificate. Note that these will only be used if the auth server
# in question is configured with SSLAvailable yes.
#
# The certificates in this directory must be named by their hash
# value. For OpenWRT, you need a ca-certificates package newer
# than what is shipped in Barrier Breaker (see 
# https://dev.openwrt.org/ticket/16537).
#
# This setting requires that WifiDog is compiled with SSL support.
# It will be ignored otherwise.
#
# SSLCertPath /etc/ssl/certs/ 

# Parameter: SSLAllowedCipherList
# Default: all ciphers supported
# Optional
#
# Which cipher suite to allow. Note that CyaSSL will ignore cipher
# suites that use algorithms that aren't compiled in or cipher
# suites *WITH ERRORS IN THEIR NAMES*.
#
# Please see CyaSSL documentation for allowed values, format is a
# string where the ciphers are separated by colons (:) with no
# spaces. Ciphers are ordered from most desirable to least desirable.
#
# SSLAllowedCipherList ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-SHA:ECDHE-ECDSA-AES256-SHA:ECDHE-RSA-AES128-SHA:ECDHE-RSA-AES256-SHA:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA256:ECDH-ECDSA-AES128-GCM-SHA256:ECDH-ECDSA-AES256-GCM-SHA384:ECDH-RSA-AES128-GCM-SHA256:ECDH-RSA-AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA256:AES256-SHA256:ECDH-ECDSA-AES128-SHA:ECDH-ECDSA-AES256-SHA:ECDH-RSA-AES128-SHA:ECDH-RSA-AES256-SHA:AES128-SHA:AES256-SHA

# Parameter: SSLUseSNI
# Default: no
# Optional
#
# Enable SNI (Server Name Indication) TLS extension.
# Enabling this setting is mainly useful if the auth server is hosted
# multiple secure (HTTPS) websites. The WifiDog should indicate which hostname
# it is attempting to connect to at the start of the handshaking process.
#
# This setting requires that WifiDog is compiled with SSL support.
# It will be ignored otherwise.
#
# SSLUseSNI no

# Parameter: TrustedMACList
# Default: none
# Optional
#

# Check DNS health by querying IPs of these hosts
PopularServers kernel.org,ieee.org

# Comma separated list of MAC addresses who are allowed to pass
# through without authentication.
# N.B.: weak security, since MAC addresses are easy to spoof.
#
#TrustedMACList 00:00:DE:AD:BE:AF,00:00:C0:1D:F0:0D

# Parameter: FirewallRuleSet
# Default: none
# Mandatory
#
# Groups a number of FirewallRule statements together.

# Parameter: FirewallRule
# Default: none
# 
# Define one firewall rule in a rule set.

# Rule Set: global
# 
# Used for rules to be applied to all other rulesets except locked.
FirewallRuleSet global {

    # FirewallRule syntax:
    # FirewallRule (block|drop|allow|log|ulog) [(tcp|udp|icmp) [port X or port-range X:Y]] [to IP/CIDR]

    ## To block SMTP out, as it's a tech support nightmare, and a legal liability
    #FirewallRule block tcp port 25
    
    ## Use the following if you don't want clients to be able to access machines on 
    ## the private LAN that gives internet access to wifidog.  Note that this is not
    ## client isolation;  The laptops will still be able to talk to one another, as
    ## well as to any machine bridged to the wifi of the router.
    # FirewallRule block to ***********/16
    # FirewallRule block to **********/12
    # FirewallRule block to 10.0.0.0/8
    
    ## This is an example ruleset for the Teliphone service.
    #FirewallRule allow udp to ************/27
    #FirewallRule allow udp to **********/27
    #FirewallRule allow tcp port 80 to ************

    ## This is an example ruleset for example.com
    ## example.com means example.com and *.example.com
    #FirewallRule allow tcp to example.com

    ## Use the following if you are having problems with Apple iOS 7 clients.
    ## See #7 and #14 at https://github.com/wifidog/wifidog-gateway/issues/
    #FirewallRule allow tcp to apple.com
    #FirewallRule allow tcp to icloud.com

    ## Use the following to log or ulog the traffic you want to allow or block.
    # For OPENWRT: use of these feature requires modules ipt_LOG or ipt_ULOG present in dependencies
    # iptables-mod-extra and iptables-mod-ulog (to adapt it to the linux distribution). 
    # Note: the log or ulog rule must be passed before, the rule you want to match.
    # for openwrt: use of these feature requires modules ipt_LOG or ipt_ULOG present in dependencies
    # iptables-mod-extra and iptables-mod-ulog
    # For example, you want to log (ulog works the same way) the traffic allowed on port 80 to the ip ************:
    #FirewallRule log tcp port 80 to ************
    #FirewallRule allow tcp port 80 to ************
    # And you want to know, who matche your block rule:
    #FirewallRule log to 0.0.0.0/0
    #FirewallRule block to 0.0.0.0/0
}

# Rule Set: validating-users
#
# Used for new users validating their account
FirewallRuleSet validating-users {
    FirewallRule allow to 0.0.0.0/0
}

# Rule Set: known-users
#
# Used for normal validated users.
FirewallRuleSet known-users {
    FirewallRule allow to 0.0.0.0/0
}

# Rule Set: auth-is-down
#
# Does nothing when not configured.
#
# Used when auth server is down
#FirewallRuleSet auth-is-down {
#  FirewallRule allow to 0.0.0.0/0
#}

# Rule Set: unknown-users
#
# Used for unvalidated users, this is the ruleset that gets redirected.
#
# XXX The redirect code adds the Default DROP clause.
FirewallRuleSet unknown-users {
	# Use to-ipset to block or allow externally specified hosts.
	# Ipsets are created with the ipset utility. This is useful to
    # block or allow hosts at runtime externally.
	# For example, if your auth server requires users to log in
	# via Facebook, use the ipset feature built into dnsmasq to
    # to populate a list of various IPs used by the Facebook networks.
	#FirewallRule allow to-ipset fb
    FirewallRule allow udp port 53
    FirewallRule allow tcp port 53
    FirewallRule allow udp port 67
    FirewallRule allow tcp port 67
}

# Rule Set: locked-users
#
# Not currently used
FirewallRuleSet locked-users {
    FirewallRule block to 0.0.0.0/0
}
