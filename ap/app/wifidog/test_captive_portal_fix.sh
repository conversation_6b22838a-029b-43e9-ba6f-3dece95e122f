#!/bin/sh

# 测试WiFiDog认证页面修复效果

echo "=== WiFiDog 认证页面修复测试 ==="
echo "时间: $(date)"
echo ""

# 配置参数
AUTH_SERVER="***************"
AUTH_PORT="9080"
GATEWAY_IP="*************"
GATEWAY_PORT="2060"

echo "测试配置:"
echo "  认证服务器: $AUTH_SERVER:$AUTH_PORT"
echo "  网关地址: $GATEWAY_IP:$GATEWAY_PORT"
echo ""

# 1. 检查修复后的配置文件
echo "1. 检查修复后的配置文件:"
echo ""

echo "   认证服务器配置:"
grep -A 10 "AuthServer" ap/app/wifidog/wifidog_local_test.conf | sed 's/^/     /'
echo ""

echo "   unknown-users防火墙规则:"
sed -n '/FirewallRuleSet unknown-users/,/^}/p' ap/app/wifidog/wifidog_local_test.conf | grep -E "(FirewallRule.*9080|FirewallRule.*443)" | sed 's/^/     /'
echo ""

echo "   validating-users防火墙规则:"
sed -n '/FirewallRuleSet validating-users/,/^}/p' ap/app/wifidog/wifidog_local_test.conf | grep -E "(FirewallRule.*9080|FirewallRule.*443)" | sed 's/^/     /'
echo ""

# 2. 测试认证服务器连接
echo "2. 测试认证服务器连接:"
echo ""

echo "   测试端口连通性:"
if nc -z $AUTH_SERVER $AUTH_PORT 2>/dev/null; then
    echo "     ✓ 认证服务器端口 $AUTH_SERVER:$AUTH_PORT 可达"
    
    # 测试ping接口
    echo "   测试ping接口:"
    ping_response=$(curl -s --connect-timeout 5 "http://$AUTH_SERVER:$AUTH_PORT/wifidog/ping/" 2>/dev/null)
    if [ "$ping_response" = "Pong" ]; then
        echo "     ✓ Ping接口正常: $ping_response"
    else
        echo "     ✗ Ping接口异常: $ping_response"
    fi
    
    # 测试登录页面
    echo "   测试登录页面:"
    login_response=$(curl -s --connect-timeout 5 -I "http://$AUTH_SERVER:$AUTH_PORT/wifidog/login/?gw_address=$GATEWAY_IP&gw_port=$GATEWAY_PORT&gw_id=zte_mifi_onelink_fixed&ip=*************05&mac=aa:bb:cc:dd:ee:ff&url=http%3A%2F%2Fwww.baidu.com" 2>/dev/null | head -1)
    if echo "$login_response" | grep -q "200 OK"; then
        echo "     ✓ 登录页面可访问: $login_response"
    else
        echo "     ✗ 登录页面访问失败: $login_response"
    fi
    
else
    echo "     ✗ 认证服务器端口 $AUTH_SERVER:$AUTH_PORT 不可达"
    echo "     请确认simple_auth_server.py在$AUTH_SERVER上运行"
fi
echo ""

# 3. 生成测试用的重定向URL
echo "3. 生成测试用的重定向URL:"
echo ""

test_url="http://$AUTH_SERVER:$AUTH_PORT/wifidog/login/?gw_address=$GATEWAY_IP&gw_port=$GATEWAY_PORT&gw_id=zte_mifi_onelink_fixed&ip=*************05&mac=aa:bb:cc:dd:ee:ff&url=http%3A%2F%2Fwww.baidu.com"

echo "   测试URL:"
echo "     $test_url"
echo ""

echo "   可以在客户端设备浏览器中直接访问此URL来测试认证页面"
echo ""

# 4. 检查WiFiDog进程状态
echo "4. 检查WiFiDog进程状态:"
echo ""

wifidog_pid=$(ps | grep wifidog | grep -v grep | awk '{print $1}')
if [ -n "$wifidog_pid" ]; then
    echo "   ✓ WiFiDog进程运行中 (PID: $wifidog_pid)"
    
    # 检查配置文件
    wifidog_conf=$(ps | grep wifidog | grep -v grep | grep -o '/[^ ]*\.conf')
    if [ -n "$wifidog_conf" ]; then
        echo "   使用配置文件: $wifidog_conf"
    fi
else
    echo "   ✗ WiFiDog进程未运行"
    echo "   请使用以下命令启动WiFiDog:"
    echo "     /tmp/wifidog -f -c /etc_rw/wifidog_local_test.conf -d 7"
fi
echo ""

# 5. 提供测试步骤
echo "5. 手动测试步骤:"
echo ""

echo "   步骤1: 确保认证服务器运行"
echo "     在***************上运行: python3 simple_auth_server.py"
echo ""

echo "   步骤2: 启动WiFiDog (如果未运行)"
echo "     /tmp/wifidog -f -c /etc_rw/wifidog_local_test.conf -d 7"
echo ""

echo "   步骤3: 客户端设备测试"
echo "     1. 连接到WiFi网络"
echo "     2. 在浏览器中访问 http://www.baidu.com"
echo "     3. 应该自动重定向到认证页面"
echo "     4. 点击登录按钮完成认证"
echo ""

echo "   步骤4: 验证认证成功"
echo "     认证后应该能正常访问互联网"
echo ""

# 6. 故障排除
echo "6. 故障排除:"
echo ""

echo "   如果仍然没有弹出认证页面:"
echo "   1. 检查客户端设备网络设置，确保没有使用代理"
echo "   2. 尝试清除浏览器缓存和Cookie"
echo "   3. 尝试使用不同的浏览器或设备"
echo "   4. 检查WiFiDog日志: tail -f /path/to/wifidog.log"
echo "   5. 确认客户端设备IP在192.168.100.x网段"
echo ""

echo "   如果认证页面显示但无法认证:"
echo "   1. 检查认证服务器日志"
echo "   2. 确认认证服务器的auth接口正常工作"
echo "   3. 检查WiFiDog与认证服务器的通信"
echo ""

echo "=== 测试完成 ==="
echo ""
echo "修复要点总结:"
echo "1. ✓ 修正了防火墙规则中的认证服务器IP地址"
echo "2. ✓ unknown-users和validating-users规则集都已更新"
echo "3. ✓ 现在客户端可以访问***************:9080的认证服务器"
echo ""
echo "预期效果: 客户端访问HTTP网站时应该弹出认证页面"
