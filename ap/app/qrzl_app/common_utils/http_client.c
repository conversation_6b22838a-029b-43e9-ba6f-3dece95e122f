#include "http_client.h"
#include <stdio.h>
#include <string.h>
#include <curl/curl.h>
#include "../qrzl_utils.h"


// 写入回调
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0) {
        fprintf(stderr, "[HTTP] 响应缓冲区已满，丢弃数据\n");
        return 0;
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    strncat(response, (char *)contents, copyLen);

    return totalSize;
}

// GET 请求
int http_get(const char *url, char *response, size_t resp_size) {
    CURL *curl;
    CURLcode res;

    if (!url || !response) return HTTP_ERROR;
    response[0] = '\0';

    qrzl_log("[HTTP][GET] 请求 URL: %s\n", url);

    curl = curl_easy_init();
    if (!curl) return HTTP_ERROR;

    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);

    res = curl_easy_perform(curl);

    if (res == CURLE_OK) {
        qrzl_log("[HTTP][GET] 响应: %s\n", response);
    } else {
        fprintf(stderr, "[HTTP][GET] 请求失败: %s\n", curl_easy_strerror(res));
    }

    curl_easy_cleanup(curl);
    return (res == CURLE_OK) ? HTTP_OK : HTTP_ERROR;
}

// POST 请求
int http_post(const char *url, const char *body, char *response, size_t resp_size) {
    CURL *curl;
    CURLcode res;
    struct curl_slist *headers = NULL;

    if (!url || !response) return HTTP_ERROR;
    response[0] = '\0';

    qrzl_log("[HTTP][POST] 请求 URL: %s\n", url);
    qrzl_log("[HTTP][POST] 请求体: %s\n", body ? body : "(空)");

    curl = curl_easy_init();
    if (!curl) return HTTP_ERROR;

    headers = curl_slist_append(headers, "Content-Type: application/json");

    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body ? body : "");
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);

    res = curl_easy_perform(curl);

    if (res == CURLE_OK) {
        qrzl_log("[HTTP][POST] 响应: %s\n", response);
    } else {
        fprintf(stderr, "[HTTP][POST] 请求失败: %s\n", curl_easy_strerror(res));
    }

    curl_slist_free_all(headers);
    curl_easy_cleanup(curl);

    return (res == CURLE_OK) ? HTTP_OK : HTTP_ERROR;
}
