
include $(COMMON_BASE_MK)

NAME=wifidog
SRC_DIR=wifidog-gateway-1.3.0

# Configure and build wifidog
all: configure
	make -C $(SRC_DIR) all

# Configure wifidog with cross-compilation settings
configure: $(SRC_DIR)/Makefile

$(SRC_DIR)/Makefile: $(SRC_DIR)/configure
	cd $(SRC_DIR) && \
	CROSS_ROOT=/home/<USER>/work/Code-u28/build/compiler/gcc-4.9.4_thumb_linux && \
	CROSS_COMPILE=$$CROSS_ROOT/usr/bin/arm-buildroot-linux-uclibcgnueabi- && \
	STAGEDIR_REAL=/home/<USER>/work/Code-u28/ap/staging && \
	./configure \
		--host=arm-buildroot-linux-uclibcgnueabi \
		--build=x86_64-linux-gnu \
		--target=arm-buildroot-linux-uclibcgnueabi \
		--prefix=/usr \
		--sysconfdir=/etc \
		--localstatedir=/var \
		--disable-shared \
		--enable-static \
		CC="$${CROSS_COMPILE}gcc" \
		CXX="$${CROSS_COMPILE}g++" \
		CPP="$${CROSS_COMPILE}gcc -E" \
		CXXCPP="$${CROSS_COMPILE}g++ -E" \
		AR="$${CROSS_COMPILE}ar" \
		RANLIB="$${CROSS_COMPILE}ranlib" \
		STRIP="$${CROSS_COMPILE}strip" \
		CFLAGS="-g -Os -pipe -fno-builtin -Wall -ffunction-sections -fomit-frame-pointer -msoft-float -fno-common -march=armv7-a -fno-short-enums -mthumb -mthumb-interwork -Wa,-mimplicit-it=thumb -idirafter $$STAGEDIR_REAL/include -isystem $$STAGEDIR_REAL/uClibc/usr/include -DQRZL_WIFIDOG_ONELINK -DQRZL_ONE_LINK_CUSTOMER_MY" \
		CPPFLAGS="-idirafter $$STAGEDIR_REAL/include -isystem $$STAGEDIR_REAL/uClibc/usr/include -I$(zte_lib_path)/libnvram" \
		CXXFLAGS="-g -Os -pipe -fno-builtin -Wall -ffunction-sections -fomit-frame-pointer -msoft-float -fno-common -march=armv7-a -fno-short-enums -mthumb -mthumb-interwork -Wa,-mimplicit-it=thumb -idirafter $$STAGEDIR_REAL/include -isystem $$STAGEDIR_REAL/uClibc/usr/include" \
		LDFLAGS="-g -fno-common -fno-builtin -Wl,--gc-sections -L$$STAGEDIR_REAL/lib -L$$STAGEDIR_REAL/uClibc/lib -L$(zte_lib_path)/libnvram -lnvram" \
		ac_cv_func_malloc_0_nonnull=yes \
		ac_cv_func_realloc_0_nonnull=yes \
		cross_compiling=yes

# Generate configure script if it doesn't exist
$(SRC_DIR)/configure: $(SRC_DIR)/configure.in
	cd $(SRC_DIR) && \
	unset CC CXX AR RANLIB STRIP CFLAGS CPPFLAGS LDFLAGS && \
	if [ -f autogen.sh ]; then \
		./autogen.sh; \
	else \
		autoreconf -fiv; \
	fi

clean:
	-make -C $(SRC_DIR) clean
	-rm -f $(SRC_DIR)/Makefile $(SRC_DIR)/config.status $(SRC_DIR)/config.log

distclean: clean
	-rm -f $(SRC_DIR)/configure $(SRC_DIR)/Makefile.in $(SRC_DIR)/src/Makefile.in
	-rm -rf $(SRC_DIR)/autom4te.cache

romfs:
	if [ -f $(SRC_DIR)/src/wifidog ]; then \
		cp $(SRC_DIR)/src/wifidog $(SRC_DIR)/src/wifidog.elf; \
		$(ROMFSINST) $(SRC_DIR)/src/wifidog /bin/wifidog; \
	fi
	if [ -f $(SRC_DIR)/src/wdctl ]; then \
		$(ROMFSINST) $(SRC_DIR)/src/wdctl /bin/wdctl; \
	fi
	# Install configuration files
	if [ -f wifidog_local_test.conf ]; then \
		$(ROMFSINST) wifidog_local_test.conf /etc/wifidog_local_test.conf; \
	fi
	if [ -f $(SRC_DIR)/wifidog-msg.html ]; then \
		$(ROMFSINST) $(SRC_DIR)/wifidog-msg.html /etc/wifidog-msg.html; \
	fi

.PHONY: all configure clean distclean romfs
