#*******************************************************************************
# include build/common makefile
#*******************************************************************************
include $(COMMON_MK)

EXEC = qrzl_app

# 基础必需的对象文件
OBJS_BASE = qrzl_app.o qrzl_device_control.o qrzl_utils.o \
		 common_utils/cjson.o common_utils/md5.o common_utils/http_client.o

# FOTA 功能对象文件 (只在启用时编译)
OBJS_FOTA = 
ifdef QRZL_CS_FOTA
OBJS_FOTA += fota/fota_utils.o
endif

ifdef JCV_FEATURE_CAPTIVE_PORTAL_SERVER
OBJS_FOTA += qrzl_captive_portal_server.o
endif

# 根据选择的云端协议添加对应的对象文件
OBJS_CLOUD =
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_HTTP)
OBJS_CLOUD += qrzl_http_control_client.o
endif
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_MQTT)
OBJS_CLOUD += qrzl_mqtt_control_client.o
endif
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_XLX)
OBJS_CLOUD += cloud_control/xlx_control.o
endif
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_HMM_MQTT)
OBJS_CLOUD += cloud_control/hmm_control.o
endif
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_QC_HTTP_POST)
OBJS_CLOUD += cloud_control/qc_http_post_control.o
endif
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_CS_HTTP)
OBJS_CLOUD += cloud_control/cshttp_control.o
endif
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_QC_TCP)
OBJS_CLOUD += cloud_control/qc_tcp_control.o
endif
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_JIJIA_HTTP)
OBJS_CLOUD += cloud_control/jijia_http_control.o
endif

# 根据选择的认证方法添加对应的对象文件
OBJS_AUTH =
ifeq ($(QRZL_AUTH_ONE_LINK_HTTP),yes)
OBJS_AUTH += cloud_control/one_link_http_control.o
endif
ifeq ($(QRZL_AUTH_CMP_HTTP),yes)
OBJS_AUTH += cloud_control/cmp_auth_control.o
endif

# 根据选择的客户类型添加对应的认证控制对象文件
OBJS_AUTH_CONTROL =
ifeq ($(QRZL_AUTH_CUSTOMER),CHUANGSAN)
OBJS_AUTH_CONTROL += auth_control/chuangsan_auth_control.o
endif
ifeq ($(QRZL_AUTH_CUSTOMER),WUXING)
OBJS_AUTH_CONTROL += auth_control/wuxing_auth_control.o
endif
ifeq ($(QRZL_AUTH_CUSTOMER),XUNYOU)
OBJS_AUTH_CONTROL += auth_control/xunyou_auth_control.o
endif
ifeq ($(QRZL_AUTH_CUSTOMER),JIUYAO)
OBJS_AUTH_CONTROL += auth_control/jiuyao_auth_control.o
endif
ifeq ($(QRZL_AUTH_CUSTOMER),JNZY)
OBJS_AUTH_CONTROL += auth_control/jnzy_auth_control.o
endif
ifeq ($(QRZL_AUTH_CUSTOMER),BEIWEI)
OBJS_AUTH_CONTROL += auth_control/beiwei_auth_control.o
endif
ifeq ($(QRZL_AUTH_CUSTOMER),SHAYIN)
OBJS_AUTH_CONTROL += auth_control/shayin_auth_control.o
endif
ifeq ($(QRZL_AUTH_CUSTOMER),MY)
OBJS_AUTH_CONTROL += auth_control/my_auth_control.o
endif
ifeq ($(QRZL_AUTH_CUSTOMER),XIANJI)
OBJS_AUTH_CONTROL += auth_control/xianji_auth_control.o
endif
ifeq ($(QRZL_AUTH_CUSTOMER),CMP_ORIGINAL)
OBJS_AUTH_CONTROL += auth_control/cmp_original_auth_control.o
endif
ifeq ($(QRZL_AUTH_CUSTOMER),ALL)
OBJS_AUTH_CONTROL += auth_control/cmp_original_auth_control.o auth_control/wuxing_auth_control.o auth_control/xunyou_auth_control.o \
		auth_control/jnzy_auth_control.o auth_control/chuangsan_auth_control.o auth_control/jiuyao_auth_control.o \
		auth_control/shayin_auth_control.o auth_control/my_auth_control.o  auth_control/beiwei_auth_control.o
endif

# 合并所有对象文件
OBJS = $(OBJS_BASE) $(OBJS_FOTA) $(OBJS_CLOUD) $(OBJS_AUTH) $(OBJS_AUTH_CONTROL)
		
# 宏和头文件目录在CFLAGS里定义，要用+=,不要用=,否则会覆盖COMMON_MK里的值
CFLAGS += -I$(APP_DIR)/include
CFLAGS += -I$(zte_lib_path)/libsoftap
CFLAGS += -I$(zte_lib_path)/libsoft_timer
CFLAGS += -I$(zte_lib_path)/libatutils
CFLAGS += -I$(zte_lib_path)/libnvram
CFLAGS += -I$(zte_lib_path)/libcurl/install/include
CFLAGS += -I$(zte_lib_path)/libpahomqttc/install/include
CFLAGS += -I$(zte_lib_path)/libwolfssl/install/include

# 添加编译优化选项以减小文件大小
CFLAGS += -Os -ffunction-sections -fdata-sections

# 添加动态链接和大小优化选项
LDFLAGS += -Wl,--as-needed -Wl,--gc-sections

# 公共库链接选项
LDLIBS = -lpthread

LDLIBS_qrzl_app  = -lsoftap -L$(zte_lib_path)/libsoftap
LDLIBS_qrzl_app  += -lsoft_timer -L$(zte_lib_path)/libsoft_timer
LDLIBS_qrzl_app  += -latutils -L$(zte_lib_path)/libatutils
LDLIBS_qrzl_app  += -lnvram -L$(zte_lib_path)/libnvram
LDLIBS_qrzl_app  += -lcurl -L$(zte_lib_path)/libcurl/install/lib/
LDLIBS_qrzl_app  += -lm
LDLIBS_qrzl_app  += -lpaho-mqtt3c -L$(zte_lib_path)/libpahomqttc/install/lib/
LDLIBS_qrzl_app  += -lwolfssl -L$(zte_lib_path)/libwolfssl/install/lib/


#*******************************************************************************
# targets
#*******************************************************************************
all: $(EXEC)


$(EXEC): $(OBJS)
	$(CC) $(LDFLAGS) -o $@ $^ $(LDLIBS) $(LDLIBS_$@)
	@cp $@ $@.elf

romfs:
	$(ROMFSINST) $(EXEC) /bin/$(EXEC)
ifeq ($(QRZL_USERDATA_OTA),yes)
	@cp -rvf $(PRJ_BIN_DIR)/../../../../../output/rootfs/bin/$(EXEC)  $(PRJ_BIN_DIR)/../../fs/normal/qrzl_ota/bin/
endif

clean:
	-@rm -f $(EXEC) *.elf *.gdb
	-@find . -name "*.o" -delete
